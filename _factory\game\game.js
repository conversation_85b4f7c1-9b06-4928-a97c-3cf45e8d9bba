!function(a,b){"object"==typeof module&&"object"==typeof module.exports?module.exports=a.document?b(a,!0):function(a){if(!a.document)throw Error("jQuery requires a window with a document");return b(a)}:b(a)}("undefined"!=typeof window?window:this,function(a,b){function c(a,b,c){var f,e,d=(c=c||N).createElement("script");if(d.text=a,b)for(f in xc)(e=b[f]||b.getAttribute&&b.getAttribute(f))&&d.setAttribute(f,e);c.head.appendChild(d).parentNode.removeChild(d)}function d(a){return null==a?a+"":"object"==
typeof a||"function"==typeof a?eb[Lb.call(a)]||"object":typeof a}function e(a){var b=!!a&&"length"in a&&a.length,c=d(a);return!O(a)&&!Ia(a)&&("array"===c||0===b||"number"==typeof b&&0<b&&b-1 in a)}function g(a,b){return a.nodeName&&a.nodeName.toLowerCase()===b.toLowerCase()}function j(a,b){return b?"\x00"===a?"\ufffd":a.slice(0,-1)+"\\"+a.charCodeAt(a.length-1).toString(16)+" ":"\\"+a}function p(a,b,c){return O(b)?m.grep(a,function(a,I){return!!b.call(a,I,a)!==c}):b.nodeType?m.grep(a,function(a){return a===
b!==c}):"string"!=typeof b?m.grep(a,function(a){return-1<ra.call(b,a)!==c}):m.filter(b,a,c)}function f(a,b){for(;(a=a[b])&&1!==a.nodeType;);return a}function n(a){return a}function l(a){throw a;}function q(a,b,c,f){var e;try{a&&O(e=a.promise)?e.call(a).done(b).fail(c):a&&O(e=a.then)?e.call(a,b,c):b.apply(void 0,[a].slice(f))}catch(d){c.apply(void 0,[d])}}function r(){N.removeEventListener("DOMContentLoaded",r);a.removeEventListener("load",r);m.ready()}function t(a,b){return b.toUpperCase()}function s(a){return a.replace(yc,
"ms-").replace(zc,t)}function C(){this.expando=m.expando+C.uid++}function F(a,b,c){var f,e;if(void 0===c&&1===a.nodeType)if(f="data-"+b.replace(Ac,"-$&").toLowerCase(),"string"==typeof(c=a.getAttribute(f))){try{c="true"===(e=c)||"false"!==e&&("null"===e?null:e===+e+""?+e:Bc.test(e)?JSON.parse(e):e)}catch(d){}ea.set(a,b,c)}else c=void 0;return c}function A(a,b,c,f){var e,d,g=20,l=f?function(){return f.cur()}:function(){return m.css(a,b,"")},n=l(),j=c&&c[3]||(m.cssNumber[b]?"":"px"),r=a.nodeType&&(m.cssNumber[b]||
"px"!==j&&+n)&&Pa.exec(m.css(a,b));if(r&&r[3]!==j){n/=2;j=j||r[3];for(r=+n||1;g--;)m.style(a,b,r+j),0>=(1-d)*(1-(d=l()/n||0.5))&&(g=0),r/=d;r*=2;m.style(a,b,r+j);c=c||[]}return c&&(r=+r||+n||0,e=c[1]?r+(c[1]+1)*c[2]:+c[2],f&&(f.unit=j,f.start=r,f.end=e)),e}function L(a,b){for(var c,f,e,d,g,n,l,j=[],r=0,q=a.length;r<q;r++)(f=a[r]).style&&(c=f.style.display,b?("none"===c&&(j[r]=H.get(f,"display")||null,j[r]||(f.style.display="")),""===f.style.display&&fb(f)&&(j[r]=(l=g=d=void 0,g=(e=f).ownerDocument,
n=e.nodeName,(l=Mb[n])||(d=g.body.appendChild(g.createElement(n)),l=m.css(d,"display"),d.parentNode.removeChild(d),"none"===l&&(l="block"),Mb[n]=l)))):"none"!==c&&(j[r]="none",H.set(f,"display",c)));for(r=0;r<q;r++)null!=j[r]&&(a[r].style.display=j[r]);return a}function B(a,b){var c;return c="undefined"!=typeof a.getElementsByTagName?a.getElementsByTagName(b||"*"):"undefined"!=typeof a.querySelectorAll?a.querySelectorAll(b||"*"):[],void 0===b||b&&g(a,b)?m.merge([a],c):c}function K(a,b){for(var c=
0,f=a.length;c<f;c++)H.set(a[c],"globalEval",!b||H.get(b[c],"globalEval"))}function R(a,b,c,f,e){for(var g,n,l,j,r=b.createDocumentFragment(),q=[],p=0,t=a.length;p<t;p++)if((g=a[p])||0===g)if("object"===d(g))m.merge(q,g.nodeType?[g]:g);else if(Cc.test(g)){n=n||r.appendChild(b.createElement("div"));l=(Nb.exec(g)||["",""])[1].toLowerCase();l=ha[l]||ha._default;n.innerHTML=l[1]+m.htmlPrefilter(g)+l[2];for(l=l[0];l--;)n=n.lastChild;m.merge(q,n.childNodes);(n=r.firstChild).textContent=""}else q.push(b.createTextNode(g));
r.textContent="";for(p=0;g=q[p++];)if(f&&-1<m.inArray(g,f))e&&e.push(g);else if(j=Ja(g),n=B(r.appendChild(g),"script"),j&&K(n),c)for(l=0;g=n[l++];)Ob.test(g.type||"")&&c.push(g);return r}function D(){return!0}function J(){return!1}function M(a,b,c,f,e,d){var g,n;if("object"==typeof b){for(n in"string"!=typeof c&&(f=f||c,c=void 0),b)M(a,n,c,f,b[n],d);return a}if(null==f&&null==e?(e=c,f=c=void 0):null==e&&("string"==typeof c?(e=f,f=void 0):(e=f,f=c,c=void 0)),!1===e)e=J;else if(!e)return a;return 1===
d&&(g=e,(e=function(a){return m().off(a),g.apply(this,arguments)}).guid=g.guid||(g.guid=m.guid++)),a.each(function(){m.event.add(this,b,e,f,c)})}function Q(a,b,c){c?(H.set(a,b,!1),m.event.add(a,b,{namespace:!1,handler:function(a){var c,I=H.get(this,b);if(1&a.isTrigger&&this[b])if(I)(m.event.special[b]||{}).delegateType&&a.stopPropagation();else{if(I=aa.call(arguments),H.set(this,b,I),this[b](),c=H.get(this,b),H.set(this,b,!1),I!==c)return a.stopImmediatePropagation(),a.preventDefault(),c}else I&&
(H.set(this,b,m.event.trigger(I[0],I.slice(1),this)),a.stopPropagation(),a.isImmediatePropagationStopped=D)}})):void 0===H.get(a,b)&&m.event.add(a,b,D)}function E(a,b){return g(a,"table")&&g(11!==b.nodeType?b:b.firstChild,"tr")&&m(a).children("tbody")[0]||a}function y(a){return a.type=(null!==a.getAttribute("type"))+"/"+a.type,a}function G(a){return"true/"===(a.type||"").slice(0,5)?a.type=a.type.slice(5):a.removeAttribute("type"),a}function Aa(a,b){var c,f,e,d,g,n;if(1===b.nodeType){if(H.hasData(a)&&
(n=H.get(a).events))for(e in H.remove(b,"handle events"),n){c=0;for(f=n[e].length;c<f;c++)m.event.add(b,e,n[e][c])}ea.hasData(a)&&(d=ea.access(a),g=m.extend({},d),ea.set(b,g))}}function ka(a,b,f,e){b=gb(b);var d,g,n,l,j=0,r=a.length,q=r-1,p=b[0],t=O(p);if(t||1<r&&"string"==typeof p&&!P.checkClone&&Dc.test(p))return a.each(function(c){var d=a.eq(c);t&&(b[0]=p.call(this,c,d.html()));ka(d,b,f,e)});if(r&&(g=(d=R(b,a[0].ownerDocument,!1,a,e)).firstChild,1===d.childNodes.length&&(d=g),g||e)){for(n=(g=m.map(B(d,
"script"),y)).length;j<r;j++)l=d,j!==q&&(l=m.clone(l,!0,!0),n&&m.merge(g,B(l,"script"))),f.call(a[j],l,j);if(n){d=g[g.length-1].ownerDocument;m.map(g,G);for(j=0;j<n;j++)l=g[j],Ob.test(l.type||"")&&!H.access(l,"globalEval")&&m.contains(d,l)&&(l.src&&"module"!==(l.type||"").toLowerCase()?m._evalUrl&&!l.noModule&&m._evalUrl(l.src,{nonce:l.nonce||l.getAttribute("nonce")},d):c(l.textContent.replace(Fc,""),l,d))}}return a}function Ba(a,b,c){for(var f=b?m.filter(b,a):a,e=0;null!=(b=f[e]);e++)c||1!==b.nodeType||
m.cleanData(B(b)),b.parentNode&&(c&&Ja(b)&&K(B(b,"script")),b.parentNode.removeChild(b));return a}function Y(a,b,c){var f,e,d,g,l=tb.test(b),n=a.style;return(c=c||hb(a))&&(g=c.getPropertyValue(b)||c[b],l&&g&&(g=g.replace(Qa,"$1")||void 0),""!==g||Ja(a)||(g=m.style(a,b)),!P.pixelBoxStyles()&&ub.test(g)&&Gc.test(b)&&(f=n.width,e=n.minWidth,d=n.maxWidth,n.minWidth=n.maxWidth=n.width=g,g=c.width,n.width=f,n.minWidth=e,n.maxWidth=d)),void 0!==g?g+"":g}function ia(a,b){return{get:function(){if(!a())return(this.get=
b).apply(this,arguments);delete this.get}}}function U(a){var b;if(!(b=m.cssProps[a]||Pb[a])){if(!(a in Qb)){b=Pb;var c;a:{c=a;for(var f=c[0].toUpperCase()+c.slice(1),e=Rb.length;e--;)if((c=Rb[e]+f)in Qb)break a;c=void 0}a=b[a]=c||a}b=a}return b}function v(a,b,c){return(a=Pa.exec(b))?Math.max(0,a[2]-(c||0))+(a[3]||"px"):b}function u(a,b,c,f,e,d){var g="width"===b?1:0,n=0,l=0,j=0;if(c===(f?"border":"content"))return 0;for(;4>g;g+=2)"margin"===c&&(j+=m.css(a,c+sa[g],!0,e)),f?("content"===c&&(l-=m.css(a,
"padding"+sa[g],!0,e)),"margin"!==c&&(l-=m.css(a,"border"+sa[g]+"Width",!0,e))):(l+=m.css(a,"padding"+sa[g],!0,e),"padding"!==c?l+=m.css(a,"border"+sa[g]+"Width",!0,e):n+=m.css(a,"border"+sa[g]+"Width",!0,e));return!f&&0<=d&&(l+=Math.max(0,Math.ceil(a["offset"+b[0].toUpperCase()+b.slice(1)]-d-l-n-0.5))||0),l+j}function z(a,b,c){var f=hb(a),e=(!P.boxSizingReliable()||c)&&"border-box"===m.css(a,"boxSizing",!1,f),d=e,l=Y(a,b,f),n="offset"+b[0].toUpperCase()+b.slice(1);if(ub.test(l)){if(!c)return l;l=
"auto"}return(!P.boxSizingReliable()&&e||!P.reliableTrDimensions()&&g(a,"tr")||"auto"===l||!parseFloat(l)&&"inline"===m.css(a,"display",!1,f))&&a.getClientRects().length&&(e="border-box"===m.css(a,"boxSizing",!1,f),(d=n in a)&&(l=a[n])),(l=parseFloat(l)||0)+u(a,b,c||(e?"border":"content"),d,f,l)+"px"}function x(a,b,c,f,e){return new x.prototype.init(a,b,c,f,e)}function vb(){ib&&(!1===N.hidden&&a.requestAnimationFrame?a.requestAnimationFrame(vb):a.setTimeout(vb,m.fx.interval),m.fx.tick())}function Ra(){return a.setTimeout(function(){Ka=
void 0}),Ka=Date.now()}function Ca(a,b){var c,f=0,e={height:a};for(b=b?1:0;4>f;f+=2-b)e["margin"+(c=sa[f])]=e["padding"+c]=a;return b&&(e.opacity=e.width=a),e}function Sa(a,b,c){for(var f,e=(Z.tweeners[b]||[]).concat(Z.tweeners["*"]),d=0,g=e.length;d<g;d++)if(f=e[d].call(c,b,a))return f}function Z(a,b,c){var f,e,d=0,g=Z.prefilters.length,l=m.Deferred().always(function(){delete n.elem}),n=function(){if(e)return!1;for(var b=Ka||Ra(),b=Math.max(0,j.startTime+j.duration-b),c=1-(b/j.duration||0),f=0,d=
j.tweens.length;f<d;f++)j.tweens[f].run(c);return l.notifyWith(a,[j,c,b]),1>c&&d?b:(d||l.notifyWith(a,[j,1,0]),l.resolveWith(a,[j]),!1)},j=l.promise({elem:a,props:m.extend({},b),opts:m.extend(!0,{specialEasing:{},easing:m.easing._default},c),originalProperties:b,originalOptions:c,startTime:Ka||Ra(),duration:c.duration,tweens:[],createTween:function(b,c){var f=m.Tween(a,j.opts,b,c,j.opts.specialEasing[b]||j.opts.easing);return j.tweens.push(f),f},stop:function(b){var c=0,f=b?j.tweens.length:0;if(e)return this;
for(e=!0;c<f;c++)j.tweens[c].run(1);return b?(l.notifyWith(a,[j,1,0]),l.resolveWith(a,[j,b])):l.rejectWith(a,[j,b]),this}});b=j.props;c=j.opts.specialEasing;var r,q,p,t;for(f in b)if(q=c[r=s(f)],p=b[f],Array.isArray(p)&&(q=p[1],p=b[f]=p[0]),f!==r&&(b[r]=p,delete b[f]),(t=m.cssHooks[r])&&"expand"in t)for(f in p=t.expand(p),delete b[r],p)f in b||(b[f]=p[f],c[f]=q);else c[r]=q;for(!0;d<g;d++)if(f=Z.prefilters[d].call(j,a,b,j.opts))return O(f.stop)&&(m._queueHooks(j.elem,j.opts.queue).stop=f.stop.bind(f)),
f;return m.map(b,Sa,j),O(j.opts.start)&&j.opts.start.call(a,j),j.progress(j.opts.progress).done(j.opts.done,j.opts.complete).fail(j.opts.fail).always(j.opts.always),m.fx.timer(m.extend(n,{elem:a,anim:j,queue:j.opts.queue})),j}function ga(a){return(a.match(pa)||[]).join(" ")}function fa(a){return a.getAttribute&&a.getAttribute("class")||""}function Ta(a){return Array.isArray(a)?a:"string"==typeof a&&a.match(pa)||[]}function Ua(a,b,c,f){var e;if(Array.isArray(b))m.each(b,function(b,e){c||Hc.test(a)?
f(a,e):Ua(a+"["+("object"==typeof e&&null!=e?b:"")+"]",e,c,f)});else if(c||"object"!==d(b))f(a,b);else for(e in b)Ua(a+"["+e+"]",b[e],c,f)}function la(a){return function(b,c){"string"!=typeof b&&(c=b,b="*");var f,e=0,d=b.toLowerCase().match(pa)||[];if(O(c))for(;f=d[e++];)"+"===f[0]?(f=f.slice(1)||"*",(a[f]=a[f]||[]).unshift(c)):(a[f]=a[f]||[]).push(c)}}function Da(a,b,c,f){function e(l){var n;return d[l]=!0,m.each(a[l]||[],function(a,I){var l=I(b,c,f);return"string"!=typeof l||g||d[l]?g?!(n=l):void 0:
(b.dataTypes.unshift(l),e(l),!1)}),n}var d={},g=a===wb;return e(b.dataTypes[0])||!d["*"]&&e("*")}function Ea(a,b){var c,f,e=m.ajaxSettings.flatOptions||{};for(c in b)void 0!==b[c]&&((e[c]?a:f||(f={}))[c]=b[c]);return f&&m.extend(!0,a,f),a}var ba=[],Va=Object.getPrototypeOf,aa=ba.slice,gb=ba.flat?function(a){return ba.flat.call(a)}:function(a){return ba.concat.apply([],a)},ta=ba.push,ra=ba.indexOf,eb={},Lb=eb.toString,Wa=eb.hasOwnProperty,Sb=Wa.toString,Ic=Sb.call(Object),P={},O=function(a){return"function"==
typeof a&&"number"!=typeof a.nodeType&&"function"!=typeof a.item},Ia=function(a){return null!=a&&a===a.window},N=a.document,xc={type:!0,src:!0,nonce:!0,noModule:!0},Jc=/HTML$/i,m=function(a,b){return new m.fn.init(a,b)};m.fn=m.prototype={jquery:"3.7.0",constructor:m,length:0,toArray:function(){return aa.call(this)},get:function(a){return null==a?aa.call(this):0>a?this[a+this.length]:this[a]},pushStack:function(a){a=m.merge(this.constructor(),a);return a.prevObject=this,a},each:function(a){return m.each(this,
a)},map:function(a){return this.pushStack(m.map(this,function(b,c){return a.call(b,c,b)}))},slice:function(){return this.pushStack(aa.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(m.grep(this,function(a,b){return(b+1)%2}))},odd:function(){return this.pushStack(m.grep(this,function(a,b){return b%2}))},eq:function(a){var b=this.length;a=+a+(0>a?b:0);return this.pushStack(0<=a&&a<b?[this[a]]:[])},end:function(){return this.prevObject||
this.constructor()},push:ta,sort:ba.sort,splice:ba.splice};m.extend=m.fn.extend=function(){var a,b,c,f,e,d,g=arguments[0]||{},l=1,n=arguments.length,j=!1;"boolean"==typeof g&&(j=g,g=arguments[l]||{},l++);"object"==typeof g||O(g)||(g={});for(l===n&&(g=this,l--);l<n;l++)if(null!=(a=arguments[l]))for(b in a)f=a[b],"__proto__"!==b&&g!==f&&(j&&f&&(m.isPlainObject(f)||(e=Array.isArray(f)))?(c=g[b],d=e&&!Array.isArray(c)?[]:e||m.isPlainObject(c)?c:{},e=!1,g[b]=m.extend(j,d,f)):void 0!==f&&(g[b]=f));return g};
m.extend({expando:"jQuery"+("3.7.0"+Math.random()).replace(/\D/g,""),isReady:!0,error:function(a){throw Error(a);},noop:function(){},isPlainObject:function(a){var b,c;return!(!a||"[object Object]"!==Lb.call(a))&&(!(b=Va(a))||"function"==typeof(c=Wa.call(b,"constructor")&&b.constructor)&&Sb.call(c)===Ic)},isEmptyObject:function(a){for(var b in a)return!1;return!0},globalEval:function(a,b,f){c(a,{nonce:b&&b.nonce},f)},each:function(a,b){var c,f=0;if(e(a))for(c=a.length;f<c&&!1!==b.call(a[f],f,a[f]);f++);
else for(f in a)if(!1===b.call(a[f],f,a[f]))break;return a},text:function(a){var b,c="",f=0;if(b=a.nodeType){if(1===b||9===b||11===b)return a.textContent;if(3===b||4===b)return a.nodeValue}else for(;b=a[f++];)c+=m.text(b);return c},makeArray:function(a,b){var c=b||[];return null!=a&&(e(Object(a))?m.merge(c,"string"==typeof a?[a]:a):ta.call(c,a)),c},inArray:function(a,b,c){return null==b?-1:ra.call(b,a,c)},isXMLDoc:function(a){var b=a&&(a.ownerDocument||a).documentElement;return!Jc.test(a&&a.namespaceURI||
b&&b.nodeName||"HTML")},merge:function(a,b){for(var c=+b.length,f=0,e=a.length;f<c;f++)a[e++]=b[f];return a.length=e,a},grep:function(a,b,c){var f=[],e=0,d=a.length;for(c=!c;e<d;e++)!b(a[e],e)!==c&&f.push(a[e]);return f},map:function(a,b,c){var f,d,g=0,l=[];if(e(a))for(f=a.length;g<f;g++)null!=(d=b(a[g],g,c))&&l.push(d);else for(g in a)null!=(d=b(a[g],g,c))&&l.push(d);return gb(l)},guid:1,support:P});"function"==typeof Symbol&&(m.fn[Symbol.iterator]=ba[Symbol.iterator]);m.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),
function(a,b){eb["[object "+b+"]"]=b.toLowerCase()});var Kc=ba.pop,Lc=ba.sort,Mc=ba.splice,Qa=/^[\x20\t\r\n\f]+|((?:^|[^\\])(?:\\.)*)[\x20\t\r\n\f]+$/g;m.contains=function(a,b){var c=b&&b.parentNode;return a===c||!(!c||1!==c.nodeType||!(a.contains?a.contains(c):a.compareDocumentPosition&&16&a.compareDocumentPosition(c)))};var Nc=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;m.escapeSelector=function(a){return(a+"").replace(Nc,j)};var ua=N,T=function(a,b,c,f){var e,d,g,l,n,j=b&&b.ownerDocument;d=
b?b.nodeType:9;if(c=c||[],"string"!=typeof a||!a||1!==d&&9!==d&&11!==d)return c;if(!f&&(ya(b),b=b||V,ma)){if(11!==d&&(n=Oc.exec(a)))if(e=n[1])if(9===d){if(!(g=b.getElementById(e)))return c;if(g.id===e)return na.call(c,g),c}else{if(j&&(g=j.getElementById(e))&&T.contains(b,g)&&g.id===e)return na.call(c,g),c}else{if(n[2])return na.apply(c,b.getElementsByTagName(a)),c;if((e=n[3])&&b.getElementsByClassName)return na.apply(c,b.getElementsByClassName(e)),c}if(!(jb[a+" "]||da&&da.test(a))){if(e=a,j=b,1===
d&&(Pc.test(a)||Tb.test(a))){(j=xb.test(a)&&yb(b.parentNode)||b)==b&&P.scope||((l=b.getAttribute("id"))?l=m.escapeSelector(l):b.setAttribute("id",l=X));for(d=(e=kb(a)).length;d--;)e[d]=(l?"#"+l:":scope")+" "+lb(e[d]);e=e.join(",")}try{return na.apply(c,j.querySelectorAll(e)),c}catch(r){jb(a,!0)}finally{l===X&&b.removeAttribute("id")}}}return Ub(a.replace(Qa,"$1"),b,c,f)},mb=function(){var a=[];return function ca(b,c){return a.push(b+" ")>S.cacheLength&&delete ca[a.shift()],ca[b+" "]=c}},oa=function(a){return a[X]=
!0,a},La=function(a){var b=V.createElement("fieldset");try{return!!a(b)}catch(c){return!1}finally{b.parentNode&&b.parentNode.removeChild(b)}},Qc=function(a){return function(b){return g(b,"input")&&b.type===a}},Rc=function(a){return function(b){return(g(b,"input")||g(b,"button"))&&b.type===a}},Vb=function(a){return function(b){return"form"in b?b.parentNode&&!1===b.disabled?"label"in b?"label"in b.parentNode?b.parentNode.disabled===a:b.disabled===a:b.isDisabled===a||b.isDisabled!==!a&&Sc(b)===a:b.disabled===
a:"label"in b&&b.disabled===a}},Fa=function(a){return oa(function(b){return b=+b,oa(function(c,f){for(var e,d=a([],c.length,b),g=d.length;g--;)c[e=d[g]]&&(c[e]=!(f[e]=c[e]))})})},yb=function(a){return a&&"undefined"!=typeof a.getElementsByTagName&&a},ya=function(a){var b;a=a?a.ownerDocument||a:ua;return a!=V&&9===a.nodeType&&a.documentElement&&(za=(V=a).documentElement,ma=!m.isXMLDoc(V),zb=za.matches||za.webkitMatchesSelector||za.msMatchesSelector,ua!=V&&(b=V.defaultView)&&b.top!==b&&b.addEventListener("unload",
Tc),P.getById=La(function(a){return za.appendChild(a).id=m.expando,!V.getElementsByName||!V.getElementsByName(m.expando).length}),P.disconnectedMatch=La(function(a){return zb.call(a,"*")}),P.scope=La(function(){return V.querySelectorAll(":scope")}),P.cssHas=La(function(){try{return V.querySelector(":has(*,:jqfake)"),!1}catch(a){return!0}}),P.getById?(S.filter.ID=function(a){var b=a.replace(va,wa);return function(a){return a.getAttribute("id")===b}},S.find.ID=function(a,b){if("undefined"!=typeof b.getElementById&&
ma){var c=b.getElementById(a);return c?[c]:[]}}):(S.filter.ID=function(a){var b=a.replace(va,wa);return function(a){return(a="undefined"!=typeof a.getAttributeNode&&a.getAttributeNode("id"))&&a.value===b}},S.find.ID=function(a,b){if("undefined"!=typeof b.getElementById&&ma){var c,f,I,e=b.getElementById(a);if(e){if((c=e.getAttributeNode("id"))&&c.value===a)return[e];I=b.getElementsByName(a);for(f=0;e=I[f++];)if((c=e.getAttributeNode("id"))&&c.value===a)return[e]}return[]}}),S.find.TAG=function(a,b){return"undefined"!=
typeof b.getElementsByTagName?b.getElementsByTagName(a):b.querySelectorAll(a)},S.find.CLASS=function(a,b){if("undefined"!=typeof b.getElementsByClassName&&ma)return b.getElementsByClassName(a)},da=[],La(function(a){var b;za.appendChild(a).innerHTML="<a id='"+X+"' href='' disabled='disabled'></a><select id='"+X+"-\r\\' disabled='disabled'><option selected=''></option></select>";a.querySelectorAll("[selected]").length||da.push("\\[[\\x20\\t\\r\\n\\f]*(?:value|"+Wb+")");a.querySelectorAll("[id~="+X+
"-]").length||da.push("~=");a.querySelectorAll("a#"+X+"+*").length||da.push(".#.+[+~]");a.querySelectorAll(":checked").length||da.push(":checked");(b=V.createElement("input")).setAttribute("type","hidden");a.appendChild(b).setAttribute("name","D");za.appendChild(a).disabled=!0;2!==a.querySelectorAll(":disabled").length&&da.push(":enabled",":disabled");(b=V.createElement("input")).setAttribute("name","");a.appendChild(b);a.querySelectorAll("[name='']").length||da.push("\\[[\\x20\\t\\r\\n\\f]*name[\\x20\\t\\r\\n\\f]*=[\\x20\\t\\r\\n\\f]*(?:''|\"\")")}),
P.cssHas||da.push(":has"),da=da.length&&RegExp(da.join("|")),Ab=function(a,b){if(a===b)return nb=!0,0;var c=!a.compareDocumentPosition-!b.compareDocumentPosition;return c||(1&(c=(a.ownerDocument||a)==(b.ownerDocument||b)?a.compareDocumentPosition(b):1)||!P.sortDetached&&b.compareDocumentPosition(a)===c?a===V||a.ownerDocument==ua&&T.contains(ua,a)?-1:b===V||b.ownerDocument==ua&&T.contains(ua,b)?1:Xa?ra.call(Xa,a)-ra.call(Xa,b):0:4&c?-1:1)}),V},Xb=function(){},kb=function(a,b){var c,f,e,d,g,l,n;if(g=
Yb[a+" "])return b?0:g.slice(0);g=a;l=[];for(n=S.preFilter;g;){for(d in c&&!(f=Uc.exec(g))||(f&&(g=g.slice(f[0].length)||g),l.push(e=[])),c=!1,(f=Tb.exec(g))&&(c=f.shift(),e.push({value:c,type:f[0].replace(Qa," ")}),g=g.slice(c.length)),S.filter)!(f=ob[d].exec(g))||n[d]&&!(f=n[d](f))||(c=f.shift(),e.push({value:c,type:d,matches:f}),g=g.slice(c.length));if(!c)break}return b?g.length:g?T.error(a):Yb(a,l).slice(0)},lb=function(a){for(var b=0,c=a.length,f="";b<c;b++)f+=a[b].value;return f},pb=function(a,
b,c){var f=b.dir,e=b.next,d=e||f,l=c&&"parentNode"===d,n=Vc++;return b.first?function(b,c,e){for(;b=b[f];)if(1===b.nodeType||l)return a(b,c,e);return!1}:function(b,c,W){var j,r,q=[qa,n];if(W)for(;b=b[f];){if((1===b.nodeType||l)&&a(b,c,W))return!0}else for(;b=b[f];)if(1===b.nodeType||l)if(r=b[X]||(b[X]={}),e&&g(b,e))b=b[f]||b;else{if((j=r[d])&&j[0]===qa&&j[1]===n)return q[2]=j[2];if((r[d]=q)[2]=a(b,c,W))return!0}return!1}},Bb=function(a){return 1<a.length?function(b,c,f){for(var e=a.length;e--;)if(!a[e](b,
c,f))return!1;return!0}:a[0]},qb=function(a,b,c,f,e){for(var d,g=[],l=0,n=a.length,j=null!=b;l<n;l++)(d=a[l])&&(c&&!c(d,f,e)||(g.push(d),j&&b.push(l)));return g},Cb=function(a,b,c,f,e,d){return f&&!f[X]&&(f=Cb(f)),e&&!e[X]&&(e=Cb(e,d)),oa(function(d,g,l,n){var j,r,q,p=[],m=[],t=g.length,s;if(!(s=d)){s=b||"*";for(var B=l.nodeType?[l]:l,db=[],L=0,F=B.length;L<F;L++)T(s,B[L],db);s=db}s=!a||!d&&b?s:qb(s,p,a,l,n);if(c?c(s,q=e||(d?a:t||f)?[]:g,l,n):q=s,f){j=qb(q,m);f(j,[],l,n);for(l=j.length;l--;)(r=j[l])&&
(q[m[l]]=!(s[m[l]]=r))}if(d){if(e||a){if(e){j=[];for(l=q.length;l--;)(r=q[l])&&j.push(s[l]=r);e(null,q=[],j,n)}for(l=q.length;l--;)(r=q[l])&&-1<(j=e?ra.call(d,r):p[l])&&(d[j]=!(g[j]=r))}}else q=qb(q===g?q.splice(t,q.length):q),e?e(null,g,q,n):na.apply(g,q)})},Db=function(a){var b,c,f,e=a.length,d=S.relative[a[0].type];c=d||S.relative[" "];for(var g=d?1:0,l=pb(function(a){return a===b},c,!0),n=pb(function(a){return-1<ra.call(b,a)},c,!0),j=[function(a,c,f){a=!d&&(f||c!=rb)||((b=c).nodeType?l(a,c,f):
n(a,c,f));return b=null,a}];g<e;g++)if(c=S.relative[a[g].type])j=[pb(Bb(j),c)];else{if((c=S.filter[a[g].type].apply(null,a[g].matches))[X]){for(f=++g;f<e&&!S.relative[a[f].type];f++);return Cb(1<g&&Bb(j),1<g&&lb(a.slice(0,g-1).concat({value:" "===a[g-2].type?"*":""})).replace(Qa,"$1"),c,g<f&&Db(a.slice(g,f)),f<e&&Db(a=a.slice(f)),f<e&&lb(a))}j.push(c)}return Bb(j)},Eb=function(a,b){var c,f,e,d,g=[],l=[],n=Zb[a+" "];if(!n){b||(b=kb(a));for(c=b.length;c--;)(n=Db(b[c]))[X]?g.push(n):l.push(n);(n=Zb(a,
(f=0<g.length,e=0<l.length,d=function(a,b,c,I,d){var n,W,j,r=0,q="0",p=a&&[],ca=[],t=rb,s=a||e&&S.find.TAG("*",d),B=qa+=null==t?1:Math.random()||0.1,db=s.length;for(d&&(rb=b==V||b||d);q!==db&&null!=(n=s[q]);q++){if(e&&n){W=0;for(b||n.ownerDocument==V||(ya(n),c=!ma);j=l[W++];)if(j(n,b||V,c)){na.call(I,n);break}d&&(qa=B)}f&&((n=!j&&n)&&r--,a&&p.push(n))}if(r+=q,f&&q!==r){for(W=0;j=g[W++];)j(p,ca,b,c);if(a){if(0<r)for(;q--;)p[q]||ca[q]||(ca[q]=Kc.call(I));ca=qb(ca)}na.apply(I,ca);d&&!a&&0<ca.length&&
1<r+g.length&&m.uniqueSort(I)}return d&&(qa=B,rb=t),p},f?oa(d):d))).selector=a}return n},Ub=function(a,b,c,f){var e,d,g,l,n,j="function"==typeof a&&a,r=!f&&kb(a=j.selector||a);if(c=c||[],1===r.length){if(2<(d=r[0]=r[0].slice(0)).length&&"ID"===(g=d[0]).type&&9===b.nodeType&&ma&&S.relative[d[1].type]){if(!(b=(S.find.ID(g.matches[0].replace(va,wa),b)||[])[0]))return c;j&&(b=b.parentNode);a=a.slice(d.shift().value.length)}for(e=ob.needsContext.test(a)?0:d.length;e--&&!(g=d[e],S.relative[l=g.type]);)if((n=
S.find[l])&&(f=n(g.matches[0].replace(va,wa),xb.test(d[0].type)&&yb(b.parentNode)||b))){if(d.splice(e,1),!(a=f.length&&lb(d)))return na.apply(c,f),c;break}}return(j||Eb(a,r))(f,b,!ma,c,!b||xb.test(a)&&yb(b.parentNode)||b),c},Ma,S,rb,Xa,nb,V,za,ma,da,zb,na=ta,X=m.expando,qa=0,Vc=0,$b=mb(),Yb=mb(),Zb=mb(),jb=mb(),Ab=function(a,b){return a===b&&(nb=!0),0},Wb="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",Wc=/[\x20\t\r\n\f]+/g,
Uc=/^[\x20\t\r\n\f]*,[\x20\t\r\n\f]*/,Tb=/^[\x20\t\r\n\f]*([>+~]|[\x20\t\r\n\f])[\x20\t\r\n\f]*/,Pc=/[\x20\t\r\n\f]|>/,Xc=RegExp(":((?:\\\\[\\da-fA-F]{1,6}[\\x20\\t\\r\\n\\f]?|\\\\[^\\r\\n\\f]|[\\w-]|[^\x00-\\x7f])+)(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|\\[[\\x20\\t\\r\\n\\f]*((?:\\\\[\\da-fA-F]{1,6}[\\x20\\t\\r\\n\\f]?|\\\\[^\\r\\n\\f]|[\\w-]|[^\x00-\\x7f])+)(?:[\\x20\\t\\r\\n\\f]*([*^$|!~]?=)[\\x20\\t\\r\\n\\f]*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|((?:\\\\[\\da-fA-F]{1,6}[\\x20\\t\\r\\n\\f]?|\\\\[^\\r\\n\\f]|[\\w-]|[^\x00-\\x7f])+))|)[\\x20\\t\\r\\n\\f]*\\])*)|.*)\\)|)"),
Yc=/^(?:\\[\da-fA-F]{1,6}[\x20\t\r\n\f]?|\\[^\r\n\f]|[\w-]|[^\x00-\x7f])+$/,ob={ID:/^#((?:\\[\da-fA-F]{1,6}[\x20\t\r\n\f]?|\\[^\r\n\f]|[\w-]|[^\x00-\x7f])+)/,CLASS:/^\.((?:\\[\da-fA-F]{1,6}[\x20\t\r\n\f]?|\\[^\r\n\f]|[\w-]|[^\x00-\x7f])+)/,TAG:/^((?:\\[\da-fA-F]{1,6}[\x20\t\r\n\f]?|\\[^\r\n\f]|[\w-]|[^\x00-\x7f])+|[*])/,ATTR:RegExp("^\\[[\\x20\\t\\r\\n\\f]*((?:\\\\[\\da-fA-F]{1,6}[\\x20\\t\\r\\n\\f]?|\\\\[^\\r\\n\\f]|[\\w-]|[^\x00-\\x7f])+)(?:[\\x20\\t\\r\\n\\f]*([*^$|!~]?=)[\\x20\\t\\r\\n\\f]*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|((?:\\\\[\\da-fA-F]{1,6}[\\x20\\t\\r\\n\\f]?|\\\\[^\\r\\n\\f]|[\\w-]|[^\x00-\\x7f])+))|)[\\x20\\t\\r\\n\\f]*\\]"),
PSEUDO:RegExp("^:((?:\\\\[\\da-fA-F]{1,6}[\\x20\\t\\r\\n\\f]?|\\\\[^\\r\\n\\f]|[\\w-]|[^\x00-\\x7f])+)(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|\\[[\\x20\\t\\r\\n\\f]*((?:\\\\[\\da-fA-F]{1,6}[\\x20\\t\\r\\n\\f]?|\\\\[^\\r\\n\\f]|[\\w-]|[^\x00-\\x7f])+)(?:[\\x20\\t\\r\\n\\f]*([*^$|!~]?=)[\\x20\\t\\r\\n\\f]*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|((?:\\\\[\\da-fA-F]{1,6}[\\x20\\t\\r\\n\\f]?|\\\\[^\\r\\n\\f]|[\\w-]|[^\x00-\\x7f])+))|)[\\x20\\t\\r\\n\\f]*\\])*)|.*)\\)|)"),
CHILD:RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\([\\x20\\t\\r\\n\\f]*(even|odd|(([+-]|)(\\d*)n|)[\\x20\\t\\r\\n\\f]*(?:([+-]|)[\\x20\\t\\r\\n\\f]*(\\d+)|))[\\x20\\t\\r\\n\\f]*\\)|)","i"),bool:RegExp("^(?:"+Wb+")$","i"),needsContext:RegExp("^[\\x20\\t\\r\\n\\f]*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\([\\x20\\t\\r\\n\\f]*((?:-\\d)?\\d*)[\\x20\\t\\r\\n\\f]*\\)|)(?=[^-]|$)","i")},Zc=/^(?:input|select|textarea|button)$/i,$c=/^h\d$/i,Oc=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,xb=/[+~]/,
va=/\\[\da-fA-F]{1,6}[\x20\t\r\n\f]?|\\([^\r\n\f])/g,wa=function(a,b){var c="0x"+a.slice(1)-65536;return b||(0>c?String.fromCharCode(c+65536):String.fromCharCode(c>>10|55296,1023&c|56320))},Tc=function(){ya()},Sc=pb(function(a){return!0===a.disabled&&g(a,"fieldset")},{dir:"parentNode",next:"legend"});try{na.apply(ba=aa.call(ua.childNodes),ua.childNodes),ba[ua.childNodes.length].nodeType}catch(zd){na={apply:function(a,b){ta.apply(a,aa.call(b))},call:function(a){ta.apply(a,aa.call(arguments,1))}}}for(Ma in T.matches=
function(a,b){return T(a,null,null,b)},T.matchesSelector=function(a,b){if(ya(a),ma&&!jb[b+" "]&&(!da||!da.test(b)))try{var c=zb.call(a,b);if(c||P.disconnectedMatch||a.document&&11!==a.document.nodeType)return c}catch(f){jb(b,!0)}return 0<T(b,V,null,[a]).length},T.contains=function(a,b){return(a.ownerDocument||a)!=V&&ya(a),m.contains(a,b)},T.attr=function(a,b){(a.ownerDocument||a)!=V&&ya(a);var c=S.attrHandle[b.toLowerCase()],c=c&&Wa.call(S.attrHandle,b.toLowerCase())?c(a,b,!ma):void 0;return void 0!==
c?c:a.getAttribute(b)},T.error=function(a){throw Error("Syntax error, unrecognized expression: "+a);},m.uniqueSort=function(a){var b,c=[],f=0,e=0;if(nb=!P.sortStable,Xa=!P.sortStable&&aa.call(a,0),Lc.call(a,Ab),nb){for(;b=a[e++];)b===a[e]&&(f=c.push(e));for(;f--;)Mc.call(a,c[f],1)}return Xa=null,a},m.fn.uniqueSort=function(){return this.pushStack(m.uniqueSort(aa.apply(this)))},(S=m.expr={cacheLength:50,createPseudo:oa,match:ob,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},
"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(a){return a[1]=a[1].replace(va,wa),a[3]=(a[3]||a[4]||a[5]||"").replace(va,wa),"~="===a[2]&&(a[3]=" "+a[3]+" "),a.slice(0,4)},CHILD:function(a){return a[1]=a[1].toLowerCase(),"nth"===a[1].slice(0,3)?(a[3]||T.error(a[0]),a[4]=+(a[4]?a[5]+(a[6]||1):2*("even"===a[3]||"odd"===a[3])),a[5]=+(a[7]+a[8]||"odd"===a[3])):a[3]&&T.error(a[0]),a},PSEUDO:function(a){var b,c=!a[6]&&a[2];return ob.CHILD.test(a[0])?null:(a[3]?
a[2]=a[4]||a[5]||"":c&&Xc.test(c)&&(b=kb(c,!0))&&(b=c.indexOf(")",c.length-b)-c.length)&&(a[0]=a[0].slice(0,b),a[2]=c.slice(0,b)),a.slice(0,3))}},filter:{TAG:function(a){var b=a.replace(va,wa).toLowerCase();return"*"===a?function(){return!0}:function(a){return g(a,b)}},CLASS:function(a){var b=$b[a+" "];return b||(b=RegExp("(^|[\\x20\\t\\r\\n\\f])"+a+"([\\x20\\t\\r\\n\\f]|$)"))&&$b(a,function(a){return b.test("string"==typeof a.className&&a.className||"undefined"!=typeof a.getAttribute&&a.getAttribute("class")||
"")})},ATTR:function(a,b,c){return function(f){f=T.attr(f,a);return null==f?"!="===b:!b||(f+="","="===b?f===c:"!="===b?f!==c:"^="===b?c&&0===f.indexOf(c):"*="===b?c&&-1<f.indexOf(c):"$="===b?c&&f.slice(-c.length)===c:"~="===b?-1<(" "+f.replace(Wc," ")+" ").indexOf(c):"|="===b&&(f===c||f.slice(0,c.length+1)===c+"-"))}},CHILD:function(a,b,c,f,e){var d="nth"!==a.slice(0,3),l="last"!==a.slice(-4),n="of-type"===b;return 1===f&&0===e?function(a){return!!a.parentNode}:function(b,c,j){var W,r,q,p;c=d!==l?
"nextSibling":"previousSibling";var m=b.parentNode,ca=n&&b.nodeName.toLowerCase(),t=!j&&!n,s=!1;if(m){if(d){for(;c;){for(r=b;r=r[c];)if(n?g(r,ca):1===r.nodeType)return!1;p=c="only"===a&&!p&&"nextSibling"}return!0}if(p=[l?m.firstChild:m.lastChild],l&&t){s=(q=(W=(j=m[X]||(m[X]={}))[a]||[])[0]===qa&&W[1])&&W[2];for(r=q&&m.childNodes[q];r=++q&&r&&r[c]||(s=q=0)||p.pop();)if(1===r.nodeType&&++s&&r===b){j[a]=[qa,q,s];break}}else if(t&&(s=q=(W=(b[X]||(b[X]={}))[a]||[])[0]===qa&&W[1]),!1===s)for(;(r=++q&&
r&&r[c]||(s=q=0)||p.pop())&&(!(n?g(r,ca):1===r.nodeType)||!++s||!(t&&((j=r[X]||(r[X]={}))[a]=[qa,s]),r===b)););return(s-=e)===f||0==s%f&&0<=s/f}}},PSEUDO:function(a,b){var c,f=S.pseudos[a]||S.setFilters[a.toLowerCase()]||T.error("unsupported pseudo: "+a);return f[X]?f(b):1<f.length?(c=[a,a,"",b],S.setFilters.hasOwnProperty(a.toLowerCase())?oa(function(a,c){for(var e,I=f(a,b),d=I.length;d--;)a[e=ra.call(a,I[d])]=!(c[e]=I[d])}):function(a){return f(a,0,c)}):f}},pseudos:{not:oa(function(a){var b=[],
c=[],f=Eb(a.replace(Qa,"$1"));return f[X]?oa(function(a,b,c,e){var I;c=f(a,null,e,[]);for(e=a.length;e--;)(I=c[e])&&(a[e]=!(b[e]=I))}):function(a,e,I){return b[0]=a,f(b,null,I,c),b[0]=null,!c.pop()}}),has:oa(function(a){return function(b){return 0<T(a,b).length}}),contains:oa(function(a){return a=a.replace(va,wa),function(b){return-1<(b.textContent||m.text(b)).indexOf(a)}}),lang:oa(function(a){return Yc.test(a||"")||T.error("unsupported lang: "+a),a=a.replace(va,wa).toLowerCase(),function(b){var c;
do if(c=ma?b.lang:b.getAttribute("xml:lang")||b.getAttribute("lang"))return(c=c.toLowerCase())===a||0===c.indexOf(a+"-");while((b=b.parentNode)&&1===b.nodeType);return!1}}),target:function(b){var c=a.location&&a.location.hash;return c&&c.slice(1)===b.id},root:function(a){return a===za},focus:function(a){var b;a:{try{b=V.activeElement;break a}catch(c){}b=void 0}return a===b&&V.hasFocus()&&!(!a.type&&!a.href&&!~a.tabIndex)},enabled:Vb(!1),disabled:Vb(!0),checked:function(a){return g(a,"input")&&!!a.checked||
g(a,"option")&&!!a.selected},selected:function(a){return a.parentNode&&a.parentNode.selectedIndex,!0===a.selected},empty:function(a){for(a=a.firstChild;a;a=a.nextSibling)if(6>a.nodeType)return!1;return!0},parent:function(a){return!S.pseudos.empty(a)},header:function(a){return $c.test(a.nodeName)},input:function(a){return Zc.test(a.nodeName)},button:function(a){return g(a,"input")&&"button"===a.type||g(a,"button")},text:function(a){var b;return g(a,"input")&&"text"===a.type&&(null==(b=a.getAttribute("type"))||
"text"===b.toLowerCase())},first:Fa(function(){return[0]}),last:Fa(function(a,b){return[b-1]}),eq:Fa(function(a,b,c){return[0>c?c+b:c]}),even:Fa(function(a,b){for(var c=0;c<b;c+=2)a.push(c);return a}),odd:Fa(function(a,b){for(var c=1;c<b;c+=2)a.push(c);return a}),lt:Fa(function(a,b,c){for(b=0>c?c+b:b<c?b:c;0<=--b;)a.push(b);return a}),gt:Fa(function(a,b,c){for(c=0>c?c+b:c;++c<b;)a.push(c);return a})}}).pseudos.nth=S.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})S.pseudos[Ma]=Qc(Ma);
for(Ma in{submit:!0,reset:!0})S.pseudos[Ma]=Rc(Ma);Xb.prototype=S.filters=S.pseudos;S.setFilters=new Xb;P.sortStable=X.split("").sort(Ab).join("")===X;ya();P.sortDetached=La(function(a){return 1&a.compareDocumentPosition(V.createElement("fieldset"))});m.find=T;m.expr[":"]=m.expr.pseudos;m.unique=m.uniqueSort;T.compile=Eb;T.select=Ub;T.setDocument=ya;T.escape=m.escapeSelector;T.getText=m.text;T.isXML=m.isXMLDoc;T.selectors=m.expr;T.support=m.support;T.uniqueSort=m.uniqueSort;!0;var Na=function(a,b,
c){for(var f=[],e=void 0!==c;(a=a[b])&&9!==a.nodeType;)if(1===a.nodeType){if(e&&m(a).is(c))break;f.push(a)}return f},ac=function(a,b){for(var c=[];a;a=a.nextSibling)1===a.nodeType&&a!==b&&c.push(a);return c},bc=m.expr.match.needsContext,cc=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;m.filter=function(a,b,c){var f=b[0];return c&&(a=":not("+a+")"),1===b.length&&1===f.nodeType?m.find.matchesSelector(f,a)?[f]:[]:m.find.matches(a,m.grep(b,function(a){return 1===a.nodeType}))};m.fn.extend({find:function(a){var b,
c,f=this.length,e=this;if("string"!=typeof a)return this.pushStack(m(a).filter(function(){for(b=0;b<f;b++)if(m.contains(e[b],this))return!0}));c=this.pushStack([]);for(b=0;b<f;b++)m.find(a,e[b],c);return 1<f?m.uniqueSort(c):c},filter:function(a){return this.pushStack(p(this,a||[],!1))},not:function(a){return this.pushStack(p(this,a||[],!0))},is:function(a){return!!p(this,"string"==typeof a&&bc.test(a)?m(a):a||[],!1).length}});var dc,ad=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(m.fn.init=function(a,b,
c){var f,e;if(!a)return this;if(c=c||dc,"string"==typeof a){if(!(f="<"===a[0]&&">"===a[a.length-1]&&3<=a.length?[null,a,null]:ad.exec(a))||!f[1]&&b)return!b||b.jquery?(b||c).find(a):this.constructor(b).find(a);if(f[1]){if(b=b instanceof m?b[0]:b,m.merge(this,m.parseHTML(f[1],b&&b.nodeType?b.ownerDocument||b:N,!0)),cc.test(f[1])&&m.isPlainObject(b))for(f in b)O(this[f])?this[f](b[f]):this.attr(f,b[f]);return this}return(e=N.getElementById(f[2]))&&(this[0]=e,this.length=1),this}return a.nodeType?(this[0]=
a,this.length=1,this):O(a)?void 0!==c.ready?c.ready(a):a(m):m.makeArray(a,this)}).prototype=m.fn;dc=m(N);var bd=/^(?:parents|prev(?:Until|All))/,cd={children:!0,contents:!0,next:!0,prev:!0};m.fn.extend({has:function(a){var b=m(a,this),c=b.length;return this.filter(function(){for(var a=0;a<c;a++)if(m.contains(this,b[a]))return!0})},closest:function(a,b){var c,f=0,e=this.length,d=[],g="string"!=typeof a&&m(a);if(!bc.test(a))for(;f<e;f++)for(c=this[f];c&&c!==b;c=c.parentNode)if(11>c.nodeType&&(g?-1<
g.index(c):1===c.nodeType&&m.find.matchesSelector(c,a))){d.push(c);break}return this.pushStack(1<d.length?m.uniqueSort(d):d)},index:function(a){return a?"string"==typeof a?ra.call(m(a),this[0]):ra.call(this,a.jquery?a[0]:a):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(a,b){return this.pushStack(m.uniqueSort(m.merge(this.get(),m(a,b))))},addBack:function(a){return this.add(null==a?this.prevObject:this.prevObject.filter(a))}});m.each({parent:function(a){return(a=a.parentNode)&&
11!==a.nodeType?a:null},parents:function(a){return Na(a,"parentNode")},parentsUntil:function(a,b,c){return Na(a,"parentNode",c)},next:function(a){return f(a,"nextSibling")},prev:function(a){return f(a,"previousSibling")},nextAll:function(a){return Na(a,"nextSibling")},prevAll:function(a){return Na(a,"previousSibling")},nextUntil:function(a,b,c){return Na(a,"nextSibling",c)},prevUntil:function(a,b,c){return Na(a,"previousSibling",c)},siblings:function(a){return ac((a.parentNode||{}).firstChild,a)},
children:function(a){return ac(a.firstChild)},contents:function(a){return null!=a.contentDocument&&Va(a.contentDocument)?a.contentDocument:(g(a,"template")&&(a=a.content||a),m.merge([],a.childNodes))}},function(a,b){m.fn[a]=function(c,f){var e=m.map(this,b,c);return"Until"!==a.slice(-5)&&(f=c),f&&"string"==typeof f&&(e=m.filter(f,e)),1<this.length&&(cd[a]||m.uniqueSort(e),bd.test(a)&&e.reverse()),this.pushStack(e)}});var pa=/[^\x20\t\r\n\f]+/g;m.Callbacks=function(a){var b,c;a="string"==typeof a?
(b=a,c={},m.each(b.match(pa)||[],function(a,b){c[b]=!0}),c):m.extend({},a);var f,e,g,l,n=[],j=[],r=-1,q=function(){l=l||a.once;for(g=f=!0;j.length;r=-1)for(e=j.shift();++r<n.length;)!1===n[r].apply(e[0],e[1])&&a.stopOnFalse&&(r=n.length,e=!1);a.memory||(e=!1);f=!1;l&&(n=e?[]:"")},p={add:function(){return n&&(e&&!f&&(r=n.length-1,j.push(e)),function Ec(b){m.each(b,function(b,c){O(c)?a.unique&&p.has(c)||n.push(c):c&&c.length&&"string"!==d(c)&&Ec(c)})}(arguments),e&&!f&&q()),this},remove:function(){return m.each(arguments,
function(a,b){for(var c;-1<(c=m.inArray(b,n,c));)n.splice(c,1),c<=r&&r--}),this},has:function(a){return a?-1<m.inArray(a,n):0<n.length},empty:function(){return n&&(n=[]),this},disable:function(){return l=j=[],n=e="",this},disabled:function(){return!n},lock:function(){return l=j=[],e||f||(n=e=""),this},locked:function(){return!!l},fireWith:function(a,b){return l||(b=[a,(b=b||[]).slice?b.slice():b],j.push(b),f||q()),this},fire:function(){return p.fireWith(this,arguments),this},fired:function(){return!!g}};
return p};m.extend({Deferred:function(b){var c=[["notify","progress",m.Callbacks("memory"),m.Callbacks("memory"),2],["resolve","done",m.Callbacks("once memory"),m.Callbacks("once memory"),0,"resolved"],["reject","fail",m.Callbacks("once memory"),m.Callbacks("once memory"),1,"rejected"]],f="pending",e={state:function(){return f},always:function(){return d.done(arguments).fail(arguments),this},"catch":function(a){return e.then(null,a)},pipe:function(){var a=arguments;return m.Deferred(function(b){m.each(c,
function(c,f){var e=O(a[f[4]])&&a[f[4]];d[f[1]](function(){var a=e&&e.apply(this,arguments);a&&O(a.promise)?a.promise().progress(b.notify).done(b.resolve).fail(b.reject):b[f[0]+"With"](this,e?[a]:arguments)})});a=null}).promise()},then:function(b,f,e){function d(b,c,f,e){return function(){var I=this,j=arguments,r=function(){var a,r;if(!(b<g)){if((a=f.apply(I,j))===c.promise())throw new TypeError("Thenable self-resolution");r=a&&("object"==typeof a||"function"==typeof a)&&a.then;O(r)?e?r.call(a,d(g,
c,n,e),d(g,c,l,e)):(g++,r.call(a,d(g,c,n,e),d(g,c,l,e),d(g,c,n,c.notifyWith))):(f!==n&&(I=void 0,j=[a]),(e||c.resolveWith)(I,j))}},q=e?r:function(){try{r()}catch(a){m.Deferred.exceptionHook&&m.Deferred.exceptionHook(a,q.error),g<=b+1&&(f!==l&&(I=void 0,j=[a]),c.rejectWith(I,j))}};b?q():(m.Deferred.getErrorHook?q.error=m.Deferred.getErrorHook():m.Deferred.getStackHook&&(q.error=m.Deferred.getStackHook()),a.setTimeout(q))}}var g=0;return m.Deferred(function(a){c[0][3].add(d(0,a,O(e)?e:n,a.notifyWith));
c[1][3].add(d(0,a,O(b)?b:n));c[2][3].add(d(0,a,O(f)?f:l))}).promise()},promise:function(a){return null!=a?m.extend(a,e):e}},d={};return m.each(c,function(a,b){var g=b[2],l=b[5];e[b[1]]=g.add;l&&g.add(function(){f=l},c[3-a][2].disable,c[3-a][3].disable,c[0][2].lock,c[0][3].lock);g.add(b[3].fire);d[b[0]]=function(){return d[b[0]+"With"](this===d?void 0:this,arguments),this};d[b[0]+"With"]=g.fireWith}),e.promise(d),b&&b.call(d,d),d},when:function(a){var b=arguments.length,c=b,f=Array(c),e=aa.call(arguments),
d=m.Deferred(),g=function(a){return function(c){f[a]=this;e[a]=1<arguments.length?aa.call(arguments):c;--b||d.resolveWith(f,e)}};if(1>=b&&(q(a,d.done(g(c)).resolve,d.reject,!b),"pending"===d.state()||O(e[c]&&e[c].then)))return d.then();for(;c--;)q(e[c],g(c),d.reject);return d.promise()}});var dd=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;m.Deferred.exceptionHook=function(b,c){a.console&&a.console.warn&&b&&dd.test(b.name)&&a.console.warn("jQuery.Deferred exception: "+b.message,b.stack,
c)};m.readyException=function(b){a.setTimeout(function(){throw b;})};var Fb=m.Deferred();m.fn.ready=function(a){return Fb.then(a)["catch"](function(a){m.readyException(a)}),this};m.extend({isReady:!1,readyWait:1,ready:function(a){(!0===a?--m.readyWait:m.isReady)||(m.isReady=!0)!==a&&0<--m.readyWait||Fb.resolveWith(N,[m])}});m.ready.then=Fb.then;"complete"===N.readyState||"loading"!==N.readyState&&!N.documentElement.doScroll?a.setTimeout(m.ready):(N.addEventListener("DOMContentLoaded",r),a.addEventListener("load",
r));var xa=function(a,b,c,f,e,g,l){var n=0,j=a.length,r=null==c;if("object"===d(c))for(n in e=!0,c)xa(a,b,n,c[n],!0,g,l);else if(void 0!==f&&(e=!0,O(f)||(l=!0),r&&(l?(b.call(a,f),b=null):(r=b,b=function(a,b,c){return r.call(m(a),c)})),b))for(;n<j;n++)b(a[n],c,l?f:f.call(a[n],n,b(a[n],c)));return e?a:r?b.call(a):j?b(a[0],c):g},yc=/^-ms-/,zc=/-([a-z])/g,Ya=function(a){return 1===a.nodeType||9===a.nodeType||!+a.nodeType};C.uid=1;C.prototype={cache:function(a){var b=a[this.expando];return b||(b={},Ya(a)&&
(a.nodeType?a[this.expando]=b:Object.defineProperty(a,this.expando,{value:b,configurable:!0}))),b},set:function(a,b,c){var f;a=this.cache(a);if("string"==typeof b)a[s(b)]=c;else for(f in b)a[s(f)]=b[f];return a},get:function(a,b){return void 0===b?this.cache(a):a[this.expando]&&a[this.expando][s(b)]},access:function(a,b,c){return void 0===b||b&&"string"==typeof b&&void 0===c?this.get(a,b):(this.set(a,b,c),void 0!==c?c:b)},remove:function(a,b){var c,f=a[this.expando];if(void 0!==f){if(void 0!==b){c=
(b=Array.isArray(b)?b.map(s):(b=s(b))in f?[b]:b.match(pa)||[]).length;for(;c--;)delete f[b[c]]}(void 0===b||m.isEmptyObject(f))&&(a.nodeType?a[this.expando]=void 0:delete a[this.expando])}},hasData:function(a){a=a[this.expando];return void 0!==a&&!m.isEmptyObject(a)}};var H=new C,ea=new C,Bc=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Ac=/[A-Z]/g;m.extend({hasData:function(a){return ea.hasData(a)||H.hasData(a)},data:function(a,b,c){return ea.access(a,b,c)},removeData:function(a,b){ea.remove(a,b)},_data:function(a,
b,c){return H.access(a,b,c)},_removeData:function(a,b){H.remove(a,b)}});m.fn.extend({data:function(a,b){var c,f,e,d=this[0],g=d&&d.attributes;if(void 0===a){if(this.length&&(e=ea.get(d),1===d.nodeType&&!H.get(d,"hasDataAttrs"))){for(c=g.length;c--;)g[c]&&0===(f=g[c].name).indexOf("data-")&&(f=s(f.slice(5)),F(d,f,e[f]));H.set(d,"hasDataAttrs",!0)}return e}return"object"==typeof a?this.each(function(){ea.set(this,a)}):xa(this,function(b){var c;if(d&&void 0===b)return void 0!==(c=ea.get(d,a))?c:void 0!==
(c=F(d,a))?c:void 0;this.each(function(){ea.set(this,a,b)})},null,b,1<arguments.length,null,!0)},removeData:function(a){return this.each(function(){ea.remove(this,a)})}});m.extend({queue:function(a,b,c){var f;if(a)return b=(b||"fx")+"queue",f=H.get(a,b),c&&(!f||Array.isArray(c)?f=H.access(a,b,m.makeArray(c)):f.push(c)),f||[]},dequeue:function(a,b){b=b||"fx";var c=m.queue(a,b),f=c.length,e=c.shift(),d=m._queueHooks(a,b);"inprogress"===e&&(e=c.shift(),f--);e&&("fx"===b&&c.unshift("inprogress"),delete d.stop,
e.call(a,function(){m.dequeue(a,b)},d));!f&&d&&d.empty.fire()},_queueHooks:function(a,b){var c=b+"queueHooks";return H.get(a,c)||H.access(a,c,{empty:m.Callbacks("once memory").add(function(){H.remove(a,[b+"queue",c])})})}});m.fn.extend({queue:function(a,b){var c=2;return"string"!=typeof a&&(b=a,a="fx",c--),arguments.length<c?m.queue(this[0],a):void 0===b?this:this.each(function(){var c=m.queue(this,a,b);m._queueHooks(this,a);"fx"===a&&"inprogress"!==c[0]&&m.dequeue(this,a)})},dequeue:function(a){return this.each(function(){m.dequeue(this,
a)})},clearQueue:function(a){return this.queue(a||"fx",[])},promise:function(a,b){var c,f=1,e=m.Deferred(),d=this,g=this.length,l=function(){--f||e.resolveWith(d,[d])};"string"!=typeof a&&(b=a,a=void 0);for(a=a||"fx";g--;)(c=H.get(d[g],a+"queueHooks"))&&c.empty&&(f++,c.empty.add(l));return l(),e.promise(b)}});var ec=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,Pa=RegExp("^(?:([+-])=|)("+ec+")([a-z%]*)$","i"),sa=["Top","Right","Bottom","Left"],Ga=N.documentElement,Ja=function(a){return m.contains(a.ownerDocument,
a)},ed={composed:!0};Ga.getRootNode&&(Ja=function(a){return m.contains(a.ownerDocument,a)||a.getRootNode(ed)===a.ownerDocument});var fb=function(a,b){return"none"===(a=b||a).style.display||""===a.style.display&&Ja(a)&&"none"===m.css(a,"display")},Mb={};m.fn.extend({show:function(){return L(this,!0)},hide:function(){return L(this)},toggle:function(a){return"boolean"==typeof a?a?this.show():this.hide():this.each(function(){fb(this)?m(this).show():m(this).hide()})}});var Ha,sb,Za=/^(?:checkbox|radio)$/i,
Nb=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,Ob=/^$|^module$|\/(?:java|ecma)script/i;Ha=N.createDocumentFragment().appendChild(N.createElement("div"));(sb=N.createElement("input")).setAttribute("type","radio");sb.setAttribute("checked","checked");sb.setAttribute("name","t");Ha.appendChild(sb);P.checkClone=Ha.cloneNode(!0).cloneNode(!0).lastChild.checked;Ha.innerHTML="<textarea>x</textarea>";P.noCloneChecked=!!Ha.cloneNode(!0).lastChild.defaultValue;Ha.innerHTML="<option></option>";P.option=!!Ha.lastChild;
var ha={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};ha.tbody=ha.tfoot=ha.colgroup=ha.caption=ha.thead;ha.th=ha.td;P.option||(ha.optgroup=ha.option=[1,"<select multiple='multiple'>","</select>"]);var Cc=/<|&#?\w+;/,fc=/^([^.]*)(?:\.(.+)|)/;m.event={global:{},add:function(a,b,c,f,e){var d,g,l,n,j,r,q,p,t,s;j=H.get(a);if(Ya(a)){c.handler&&(c=(d=c).handler,
e=d.selector);e&&m.find.matchesSelector(Ga,e);c.guid||(c.guid=m.guid++);(n=j.events)||(n=j.events=Object.create(null));(g=j.handle)||(g=j.handle=function(b){return"undefined"!=typeof m&&m.event.triggered!==b.type?m.event.dispatch.apply(a,arguments):void 0});for(j=(b=(b||"").match(pa)||[""]).length;j--;)t=s=(l=fc.exec(b[j])||[])[1],l=(l[2]||"").split(".").sort(),t&&(q=m.event.special[t]||{},t=(e?q.delegateType:q.bindType)||t,q=m.event.special[t]||{},r=m.extend({type:t,origType:s,data:f,handler:c,guid:c.guid,
selector:e,needsContext:e&&m.expr.match.needsContext.test(e),namespace:l.join(".")},d),(p=n[t])||((p=n[t]=[]).delegateCount=0,q.setup&&!1!==q.setup.call(a,f,l,g)||a.addEventListener&&a.addEventListener(t,g)),q.add&&(q.add.call(a,r),r.handler.guid||(r.handler.guid=c.guid)),e?p.splice(p.delegateCount++,0,r):p.push(r),m.event.global[t]=!0)}},remove:function(a,b,c,f,e){var d,g,l,n,j,r,q,p,t,s,B,L=H.hasData(a)&&H.get(a);if(L&&(n=L.events)){for(j=(b=(b||"").match(pa)||[""]).length;j--;)if(t=B=(l=fc.exec(b[j])||
[])[1],s=(l[2]||"").split(".").sort(),t){q=m.event.special[t]||{};p=n[t=(f?q.delegateType:q.bindType)||t]||[];l=l[2]&&RegExp("(^|\\.)"+s.join("\\.(?:.*\\.|)")+"(\\.|$)");for(g=d=p.length;d--;)r=p[d],!e&&B!==r.origType||c&&c.guid!==r.guid||l&&!l.test(r.namespace)||f&&f!==r.selector&&("**"!==f||!r.selector)||(p.splice(d,1),r.selector&&p.delegateCount--,q.remove&&q.remove.call(a,r));g&&!p.length&&(q.teardown&&!1!==q.teardown.call(a,s,L.handle)||m.removeEvent(a,t,L.handle),delete n[t])}else for(t in n)m.event.remove(a,
t+b[j],c,f,!0);m.isEmptyObject(n)&&H.remove(a,"handle events")}},dispatch:function(a){var b,c,f,e,d,g,l=Array(arguments.length),n=m.event.fix(a);c=(H.get(this,"events")||Object.create(null))[n.type]||[];var j=m.event.special[n.type]||{};l[0]=n;for(b=1;b<arguments.length;b++)l[b]=arguments[b];if(n.delegateTarget=this,!j.preDispatch||!1!==j.preDispatch.call(this,n)){g=m.event.handlers.call(this,n,c);for(b=0;(e=g[b++])&&!n.isPropagationStopped();){n.currentTarget=e.elem;for(c=0;(d=e.handlers[c++])&&
!n.isImmediatePropagationStopped();)n.rnamespace&&!1!==d.namespace&&!n.rnamespace.test(d.namespace)||(n.handleObj=d,n.data=d.data,void 0!==(f=((m.event.special[d.origType]||{}).handle||d.handler).apply(e.elem,l))&&!1===(n.result=f)&&(n.preventDefault(),n.stopPropagation()))}return j.postDispatch&&j.postDispatch.call(this,n),n.result}},handlers:function(a,b){var c,f,e,d,g,l=[],n=b.delegateCount,j=a.target;if(n&&j.nodeType&&!("click"===a.type&&1<=a.button))for(;j!==this;j=j.parentNode||this)if(1===
j.nodeType&&("click"!==a.type||!0!==j.disabled)){d=[];g={};for(c=0;c<n;c++)void 0===g[e=(f=b[c]).selector+" "]&&(g[e]=f.needsContext?-1<m(e,this).index(j):m.find(e,this,null,[j]).length),g[e]&&d.push(f);d.length&&l.push({elem:j,handlers:d})}return j=this,n<b.length&&l.push({elem:j,handlers:b.slice(n)}),l},addProp:function(a,b){Object.defineProperty(m.Event.prototype,a,{enumerable:!0,configurable:!0,get:O(b)?function(){if(this.originalEvent)return b(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[a]},
set:function(b){Object.defineProperty(this,a,{enumerable:!0,configurable:!0,writable:!0,value:b})}})},fix:function(a){return a[m.expando]?a:new m.Event(a)},special:{load:{noBubble:!0},click:{setup:function(a){a=this||a;return Za.test(a.type)&&a.click&&g(a,"input")&&Q(a,"click",!0),!1},trigger:function(a){a=this||a;return Za.test(a.type)&&a.click&&g(a,"input")&&Q(a,"click"),!0},_default:function(a){a=a.target;return Za.test(a.type)&&a.click&&g(a,"input")&&H.get(a,"click")||g(a,"a")}},beforeunload:{postDispatch:function(a){void 0!==
a.result&&a.originalEvent&&(a.originalEvent.returnValue=a.result)}}}};m.removeEvent=function(a,b,c){a.removeEventListener&&a.removeEventListener(b,c)};m.Event=function(a,b){if(!(this instanceof m.Event))return new m.Event(a,b);a&&a.type?(this.originalEvent=a,this.type=a.type,this.isDefaultPrevented=a.defaultPrevented||void 0===a.defaultPrevented&&!1===a.returnValue?D:J,this.target=a.target&&3===a.target.nodeType?a.target.parentNode:a.target,this.currentTarget=a.currentTarget,this.relatedTarget=a.relatedTarget):
this.type=a;b&&m.extend(this,b);this.timeStamp=a&&a.timeStamp||Date.now();this[m.expando]=!0};m.Event.prototype={constructor:m.Event,isDefaultPrevented:J,isPropagationStopped:J,isImmediatePropagationStopped:J,isSimulated:!1,preventDefault:function(){var a=this.originalEvent;this.isDefaultPrevented=D;a&&!this.isSimulated&&a.preventDefault()},stopPropagation:function(){var a=this.originalEvent;this.isPropagationStopped=D;a&&!this.isSimulated&&a.stopPropagation()},stopImmediatePropagation:function(){var a=
this.originalEvent;this.isImmediatePropagationStopped=D;a&&!this.isSimulated&&a.stopImmediatePropagation();this.stopPropagation()}};m.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,"char":!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},m.event.addProp);
m.each({focus:"focusin",blur:"focusout"},function(a,b){function c(a){if(N.documentMode){var f=H.get(this,"handle"),e=m.event.fix(a);e.type="focusin"===a.type?"focus":"blur";e.isSimulated=!0;f(a);e.target===e.currentTarget&&f(e)}else m.event.simulate(b,a.target,m.event.fix(a))}m.event.special[a]={setup:function(){var f;if(Q(this,a,!0),!N.documentMode)return!1;(f=H.get(this,b))||this.addEventListener(b,c);H.set(this,b,(f||0)+1)},trigger:function(){return Q(this,a),!0},teardown:function(){var a;if(!N.documentMode)return!1;
(a=H.get(this,b)-1)?H.set(this,b,a):(this.removeEventListener(b,c),H.remove(this,b))},_default:function(b){return H.get(b.target,a)},delegateType:b};m.event.special[b]={setup:function(){var f=this.ownerDocument||this.document||this,e=N.documentMode?this:f,d=H.get(e,b);d||(N.documentMode?this.addEventListener(b,c):f.addEventListener(a,c,!0));H.set(e,b,(d||0)+1)},teardown:function(){var f=this.ownerDocument||this.document||this,e=N.documentMode?this:f,d=H.get(e,b)-1;d?H.set(e,b,d):(N.documentMode?this.removeEventListener(b,
c):f.removeEventListener(a,c,!0),H.remove(e,b))}}});m.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(a,b){m.event.special[a]={delegateType:b,bindType:b,handle:function(a){var c,f=a.relatedTarget,e=a.handleObj;return f&&(f===this||m.contains(this,f))||(a.type=e.origType,c=e.handler.apply(this,arguments),a.type=b),c}}});m.fn.extend({on:function(a,b,c,f){return M(this,a,b,c,f)},one:function(a,b,c,f){return M(this,a,b,c,f,1)},off:function(a,
b,c){var f,e;if(a&&a.preventDefault&&a.handleObj)return f=a.handleObj,m(a.delegateTarget).off(f.namespace?f.origType+"."+f.namespace:f.origType,f.selector,f.handler),this;if("object"==typeof a){for(e in a)this.off(e,b,a[e]);return this}return!1!==b&&"function"!=typeof b||(c=b,b=void 0),!1===c&&(c=J),this.each(function(){m.event.remove(this,a,c,b)})}});var fd=/<script|<style|<link/i,Dc=/checked\s*(?:[^=]|=\s*.checked.)/i,Fc=/^\s*<!\[CDATA\[|\]\]>\s*$/g;m.extend({htmlPrefilter:function(a){return a},
clone:function(a,b,c){var f,e,d,g,l,n,j,r=a.cloneNode(!0),q=Ja(a);if(!P.noCloneChecked&&!(1!==a.nodeType&&11!==a.nodeType||m.isXMLDoc(a))){g=B(r);f=0;for(e=(d=B(a)).length;f<e;f++)l=d[f],n=g[f],void 0,"input"===(j=n.nodeName.toLowerCase())&&Za.test(l.type)?n.checked=l.checked:"input"!==j&&"textarea"!==j||(n.defaultValue=l.defaultValue)}if(b)if(c){d=d||B(a);g=g||B(r);f=0;for(e=d.length;f<e;f++)Aa(d[f],g[f])}else Aa(a,r);return 0<(g=B(r,"script")).length&&K(g,!q&&B(a,"script")),r},cleanData:function(a){for(var b,
c,f,e=m.event.special,d=0;void 0!==(c=a[d]);d++)if(Ya(c)){if(b=c[H.expando]){if(b.events)for(f in b.events)e[f]?m.event.remove(c,f):m.removeEvent(c,f,b.handle);c[H.expando]=void 0}c[ea.expando]&&(c[ea.expando]=void 0)}}});m.fn.extend({detach:function(a){return Ba(this,a,!0)},remove:function(a){return Ba(this,a)},text:function(a){return xa(this,function(a){return void 0===a?m.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=a)})},null,
a,arguments.length)},append:function(){return ka(this,arguments,function(a){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||E(this,a).appendChild(a)})},prepend:function(){return ka(this,arguments,function(a){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var b=E(this,a);b.insertBefore(a,b.firstChild)}})},before:function(){return ka(this,arguments,function(a){this.parentNode&&this.parentNode.insertBefore(a,this)})},after:function(){return ka(this,arguments,function(a){this.parentNode&&
this.parentNode.insertBefore(a,this.nextSibling)})},empty:function(){for(var a,b=0;null!=(a=this[b]);b++)1===a.nodeType&&(m.cleanData(B(a,!1)),a.textContent="");return this},clone:function(a,b){return a=null!=a&&a,b=null==b?a:b,this.map(function(){return m.clone(this,a,b)})},html:function(a){return xa(this,function(a){var b=this[0]||{},c=0,f=this.length;if(void 0===a&&1===b.nodeType)return b.innerHTML;if("string"==typeof a&&!fd.test(a)&&!ha[(Nb.exec(a)||["",""])[1].toLowerCase()]){a=m.htmlPrefilter(a);
try{for(;c<f;c++)1===(b=this[c]||{}).nodeType&&(m.cleanData(B(b,!1)),b.innerHTML=a);b=0}catch(e){}}b&&this.empty().append(a)},null,a,arguments.length)},replaceWith:function(){var a=[];return ka(this,arguments,function(b){var c=this.parentNode;0>m.inArray(this,a)&&(m.cleanData(B(this)),c&&c.replaceChild(b,this))},a)}});m.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(a,b){m.fn[a]=function(a){for(var c=[],f=m(a),e=f.length-1,
d=0;d<=e;d++)a=d===e?this:this.clone(!0),m(f[d])[b](a),ta.apply(c,a.get());return this.pushStack(c)}});var ub=RegExp("^("+ec+")(?!px)[a-z%]+$","i"),tb=/^--/,hb=function(b){var c=b.ownerDocument.defaultView;return c&&c.opener||(c=a),c.getComputedStyle(b)},gc=function(a,b,c){var f,e={};for(f in b)e[f]=a.style[f],a.style[f]=b[f];for(f in c=c.call(a),b)a.style[f]=e[f];return c},Gc=RegExp(sa.join("|"),"i"),$a=function(){if(ja){Gb.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0";
ja.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%";Ga.appendChild(Gb).appendChild(ja);var b=a.getComputedStyle(ja);hc="1%"!==b.top;ic=12===Math.round(parseFloat(b.marginLeft));ja.style.right="60%";jc=36===Math.round(parseFloat(b.right));kc=36===Math.round(parseFloat(b.width));ja.style.position="absolute";lc=12===Math.round(parseFloat(ja.offsetWidth/3));Ga.removeChild(Gb);ja=null}},hc,kc,lc,jc,Hb,ic,Gb=N.createElement("div"),
ja=N.createElement("div");ja.style&&(ja.style.backgroundClip="content-box",ja.cloneNode(!0).style.backgroundClip="",P.clearCloneStyle="content-box"===ja.style.backgroundClip,m.extend(P,{boxSizingReliable:function(){return $a(),kc},pixelBoxStyles:function(){return $a(),jc},pixelPosition:function(){return $a(),hc},reliableMarginLeft:function(){return $a(),ic},scrollboxSize:function(){return $a(),lc},reliableTrDimensions:function(){var b,c,f,e;return null==Hb&&(b=N.createElement("table"),c=N.createElement("tr"),
f=N.createElement("div"),b.style.cssText="position:absolute;left:-11111px;border-collapse:separate",c.style.cssText="border:1px solid",c.style.height="1px",f.style.height="9px",f.style.display="block",Ga.appendChild(b).appendChild(c).appendChild(f),e=a.getComputedStyle(c),Hb=parseInt(e.height,10)+parseInt(e.borderTopWidth,10)+parseInt(e.borderBottomWidth,10)===c.offsetHeight,Ga.removeChild(b)),Hb}}));!0;var Rb=["Webkit","Moz","ms"],Qb=N.createElement("div").style,Pb={},gd=/^(none|table(?!-c[ea]).+)/,
hd={position:"absolute",visibility:"hidden",display:"block"},mc={letterSpacing:"0",fontWeight:"400"};m.extend({cssHooks:{opacity:{get:function(a,b){if(b){var c=Y(a,"opacity");return""===c?"1":c}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,
zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(a,b,c,f){if(a&&3!==a.nodeType&&8!==a.nodeType&&a.style){var e,d,g,l=s(b),n=tb.test(b),j=a.style;if(n||(b=U(l)),g=m.cssHooks[b]||m.cssHooks[l],void 0===c)return g&&"get"in g&&void 0!==(e=g.get(a,!1,f))?e:j[b];"string"===(d=typeof c)&&(e=Pa.exec(c))&&e[1]&&(c=A(a,b,e),d="number");null!=c&&c==c&&("number"!==d||n||(c+=e&&e[3]||(m.cssNumber[l]?"":"px")),P.clearCloneStyle||""!==c||0!==
b.indexOf("background")||(j[b]="inherit"),g&&"set"in g&&void 0===(c=g.set(a,c,f))||(n?j.setProperty(b,c):j[b]=c))}},css:function(a,b,c,f){var e,d,g,l=s(b);return tb.test(b)||(b=U(l)),(g=m.cssHooks[b]||m.cssHooks[l])&&"get"in g&&(e=g.get(a,!0,c)),void 0===e&&(e=Y(a,b,f)),"normal"===e&&b in mc&&(e=mc[b]),""===c||c?(d=parseFloat(e),!0===c||isFinite(d)?d||0:e):e}});m.each(["height","width"],function(a,b){m.cssHooks[b]={get:function(a,c,f){if(c)return!gd.test(m.css(a,"display"))||a.getClientRects().length&&
a.getBoundingClientRect().width?z(a,b,f):gc(a,hd,function(){return z(a,b,f)})},set:function(a,c,f){var e,d=hb(a),g=!P.scrollboxSize()&&"absolute"===d.position,l=(g||f)&&"border-box"===m.css(a,"boxSizing",!1,d);f=f?u(a,b,f,l,d):0;return l&&g&&(f-=Math.ceil(a["offset"+b[0].toUpperCase()+b.slice(1)]-parseFloat(d[b])-u(a,b,"border",!1,d)-0.5)),f&&(e=Pa.exec(c))&&"px"!==(e[3]||"px")&&(a.style[b]=c,c=m.css(a,b)),v(0,c,f)}}});m.cssHooks.marginLeft=ia(P.reliableMarginLeft,function(a,b){if(b)return(parseFloat(Y(a,
"marginLeft"))||a.getBoundingClientRect().left-gc(a,{marginLeft:0},function(){return a.getBoundingClientRect().left}))+"px"});m.each({margin:"",padding:"",border:"Width"},function(a,b){m.cssHooks[a+b]={expand:function(c){var f=0,e={};for(c="string"==typeof c?c.split(" "):[c];4>f;f++)e[a+sa[f]+b]=c[f]||c[f-2]||c[0];return e}};"margin"!==a&&(m.cssHooks[a+b].set=v)});m.fn.extend({css:function(a,b){return xa(this,function(a,b,c){var f,e={},d=0;if(Array.isArray(b)){c=hb(a);for(f=b.length;d<f;d++)e[b[d]]=
m.css(a,b[d],!1,c);return e}return void 0!==c?m.style(a,b,c):m.css(a,b)},a,b,1<arguments.length)}});((m.Tween=x).prototype={constructor:x,init:function(a,b,c,f,e,d){this.elem=a;this.prop=c;this.easing=e||m.easing._default;this.options=b;this.start=this.now=this.cur();this.end=f;this.unit=d||(m.cssNumber[c]?"":"px")},cur:function(){var a=x.propHooks[this.prop];return a&&a.get?a.get(this):x.propHooks._default.get(this)},run:function(a){var b,c=x.propHooks[this.prop];return this.options.duration?this.pos=
b=m.easing[this.easing](a,this.options.duration*a,0,1,this.options.duration):this.pos=b=a,this.now=(this.end-this.start)*b+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),c&&c.set?c.set(this):x.propHooks._default.set(this),this}}).init.prototype=x.prototype;(x.propHooks={_default:{get:function(a){var b;return 1!==a.elem.nodeType||null!=a.elem[a.prop]&&null==a.elem.style[a.prop]?a.elem[a.prop]:(b=m.css(a.elem,a.prop,""))&&"auto"!==b?b:0},set:function(a){m.fx.step[a.prop]?
m.fx.step[a.prop](a):1!==a.elem.nodeType||!m.cssHooks[a.prop]&&null==a.elem.style[U(a.prop)]?a.elem[a.prop]=a.now:m.style(a.elem,a.prop,a.now+a.unit)}}}).scrollTop=x.propHooks.scrollLeft={set:function(a){a.elem.nodeType&&a.elem.parentNode&&(a.elem[a.prop]=a.now)}};m.easing={linear:function(a){return a},swing:function(a){return 0.5-Math.cos(a*Math.PI)/2},_default:"swing"};m.fx=x.prototype.init;m.fx.step={};var Ka,ib,Oa,nc,id=/^(?:toggle|show|hide)$/,jd=/queueHooks$/;m.Animation=m.extend(Z,{tweeners:{"*":[function(a,
b){var c=this.createTween(a,b);return A(c.elem,a,Pa.exec(b),c),c}]},tweener:function(a,b){O(a)?(b=a,a=["*"]):a=a.match(pa);for(var c,f=0,e=a.length;f<e;f++)c=a[f],Z.tweeners[c]=Z.tweeners[c]||[],Z.tweeners[c].unshift(b)},prefilters:[function(a,b,c){var f,e,d,g,l,n,j,r="width"in b||"height"in b,q=this,p={},t=a.style,s=a.nodeType&&fb(a),B=H.get(a,"fxshow");for(f in c.queue||(null==(g=m._queueHooks(a,"fx")).unqueued&&(g.unqueued=0,l=g.empty.fire,g.empty.fire=function(){g.unqueued||l()}),g.unqueued++,
q.always(function(){q.always(function(){g.unqueued--;m.queue(a,"fx").length||g.empty.fire()})})),b)if(e=b[f],id.test(e)){if(delete b[f],d=d||"toggle"===e,e===(s?"hide":"show")){if("show"!==e||!B||void 0===B[f])continue;s=!0}p[f]=B&&B[f]||m.style(a,f)}if((b=!m.isEmptyObject(b))||!m.isEmptyObject(p))for(f in r&&1===a.nodeType&&(c.overflow=[t.overflow,t.overflowX,t.overflowY],null==(n=B&&B.display)&&(n=H.get(a,"display")),"none"===(j=m.css(a,"display"))&&(n?j=n:(L([a],!0),n=a.style.display||n,j=m.css(a,
"display"),L([a]))),("inline"===j||"inline-block"===j&&null!=n)&&"none"===m.css(a,"float")&&(b||(q.done(function(){t.display=n}),null==n&&(j=t.display,n="none"===j?"":j)),t.display="inline-block")),c.overflow&&(t.overflow="hidden",q.always(function(){t.overflow=c.overflow[0];t.overflowX=c.overflow[1];t.overflowY=c.overflow[2]})),b=!1,p)b||(B?"hidden"in B&&(s=B.hidden):B=H.access(a,"fxshow",{display:n}),d&&(B.hidden=!s),s&&L([a],!0),q.done(function(){for(f in s||L([a]),H.remove(a,"fxshow"),p)m.style(a,
f,p[f])})),b=Sa(s?B[f]:0,f,q),f in B||(B[f]=b.start,s&&(b.end=b.start,b.start=0))}],prefilter:function(a,b){b?Z.prefilters.unshift(a):Z.prefilters.push(a)}});m.speed=function(a,b,c){var f=a&&"object"==typeof a?m.extend({},a):{complete:c||!c&&b||O(a)&&a,duration:a,easing:c&&b||b&&!O(b)&&b};return m.fx.off?f.duration=0:"number"!=typeof f.duration&&(f.duration in m.fx.speeds?f.duration=m.fx.speeds[f.duration]:f.duration=m.fx.speeds._default),null!=f.queue&&!0!==f.queue||(f.queue="fx"),f.old=f.complete,
f.complete=function(){O(f.old)&&f.old.call(this);f.queue&&m.dequeue(this,f.queue)},f};m.fn.extend({fadeTo:function(a,b,c,f){return this.filter(fb).css("opacity",0).show().end().animate({opacity:b},a,c,f)},animate:function(a,b,c,f){var e=m.isEmptyObject(a),d=m.speed(b,c,f);b=function(){var b=Z(this,m.extend({},a),d);(e||H.get(this,"finish"))&&b.stop(!0)};return b.finish=b,e||!1===d.queue?this.each(b):this.queue(d.queue,b)},stop:function(a,b,c){var f=function(a){var b=a.stop;delete a.stop;b(c)};return"string"!=
typeof a&&(c=b,b=a,a=void 0),b&&this.queue(a||"fx",[]),this.each(function(){var b=!0,e=null!=a&&a+"queueHooks",d=m.timers,g=H.get(this);if(e)g[e]&&g[e].stop&&f(g[e]);else for(e in g)g[e]&&g[e].stop&&jd.test(e)&&f(g[e]);for(e=d.length;e--;)d[e].elem!==this||null!=a&&d[e].queue!==a||(d[e].anim.stop(c),b=!1,d.splice(e,1));!b&&c||m.dequeue(this,a)})},finish:function(a){return!1!==a&&(a=a||"fx"),this.each(function(){var b,c=H.get(this),f=c[a+"queue"];b=c[a+"queueHooks"];var e=m.timers,d=f?f.length:0;c.finish=
!0;m.queue(this,a,[]);b&&b.stop&&b.stop.call(this,!0);for(b=e.length;b--;)e[b].elem===this&&e[b].queue===a&&(e[b].anim.stop(!0),e.splice(b,1));for(b=0;b<d;b++)f[b]&&f[b].finish&&f[b].finish.call(this);delete c.finish})}});m.each(["toggle","show","hide"],function(a,b){var c=m.fn[b];m.fn[b]=function(a,f,e){return null==a||"boolean"==typeof a?c.apply(this,arguments):this.animate(Ca(b,!0),a,f,e)}});m.each({slideDown:Ca("show"),slideUp:Ca("hide"),slideToggle:Ca("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},
fadeToggle:{opacity:"toggle"}},function(a,b){m.fn[a]=function(a,c,f){return this.animate(b,a,c,f)}});m.timers=[];m.fx.tick=function(){var a,b=0,c=m.timers;for(Ka=Date.now();b<c.length;b++)(a=c[b])()||c[b]!==a||c.splice(b--,1);c.length||m.fx.stop();Ka=void 0};m.fx.timer=function(a){m.timers.push(a);m.fx.start()};m.fx.interval=13;m.fx.start=function(){ib||(ib=!0,vb())};m.fx.stop=function(){ib=null};m.fx.speeds={slow:600,fast:200,_default:400};m.fn.delay=function(b,c){return b=m.fx&&m.fx.speeds[b]||
b,c=c||"fx",this.queue(c,function(c,f){var e=a.setTimeout(c,b);f.stop=function(){a.clearTimeout(e)}})};Oa=N.createElement("input");nc=N.createElement("select").appendChild(N.createElement("option"));Oa.type="checkbox";P.checkOn=""!==Oa.value;P.optSelected=nc.selected;(Oa=N.createElement("input")).value="t";Oa.type="radio";P.radioValue="t"===Oa.value;var oc,ab=m.expr.attrHandle;m.fn.extend({attr:function(a,b){return xa(this,m.attr,a,b,1<arguments.length)},removeAttr:function(a){return this.each(function(){m.removeAttr(this,
a)})}});m.extend({attr:function(a,b,c){var f,e,d=a.nodeType;if(3!==d&&8!==d&&2!==d)return"undefined"==typeof a.getAttribute?m.prop(a,b,c):(1===d&&m.isXMLDoc(a)||(e=m.attrHooks[b.toLowerCase()]||(m.expr.match.bool.test(b)?oc:void 0)),void 0!==c?null===c?void m.removeAttr(a,b):e&&"set"in e&&void 0!==(f=e.set(a,c,b))?f:(a.setAttribute(b,c+""),c):e&&"get"in e&&null!==(f=e.get(a,b))?f:null==(f=m.find.attr(a,b))?void 0:f)},attrHooks:{type:{set:function(a,b){if(!P.radioValue&&"radio"===b&&g(a,"input")){var c=
a.value;return a.setAttribute("type",b),c&&(a.value=c),b}}}},removeAttr:function(a,b){var c,f=0,e=b&&b.match(pa);if(e&&1===a.nodeType)for(;c=e[f++];)a.removeAttribute(c)}});oc={set:function(a,b,c){return!1===b?m.removeAttr(a,c):a.setAttribute(c,c),c}};m.each(m.expr.match.bool.source.match(/\w+/g),function(a,b){var c=ab[b]||m.find.attr;ab[b]=function(a,b,f){var e,d,g=b.toLowerCase();return f||(d=ab[g],ab[g]=e,e=null!=c(a,b,f)?g:null,ab[g]=d),e}});var kd=/^(?:input|select|textarea|button)$/i,ld=/^(?:a|area)$/i;
m.fn.extend({prop:function(a,b){return xa(this,m.prop,a,b,1<arguments.length)},removeProp:function(a){return this.each(function(){delete this[m.propFix[a]||a]})}});m.extend({prop:function(a,b,c){var f,e,d=a.nodeType;if(3!==d&&8!==d&&2!==d)return 1===d&&m.isXMLDoc(a)||(b=m.propFix[b]||b,e=m.propHooks[b]),void 0!==c?e&&"set"in e&&void 0!==(f=e.set(a,c,b))?f:a[b]=c:e&&"get"in e&&null!==(f=e.get(a,b))?f:a[b]},propHooks:{tabIndex:{get:function(a){var b=m.find.attr(a,"tabindex");return b?parseInt(b,10):
kd.test(a.nodeName)||ld.test(a.nodeName)&&a.href?0:-1}}},propFix:{"for":"htmlFor","class":"className"}});P.optSelected||(m.propHooks.selected={get:function(a){a=a.parentNode;return a&&a.parentNode&&a.parentNode.selectedIndex,null},set:function(a){a=a.parentNode;a&&(a.selectedIndex,a.parentNode&&a.parentNode.selectedIndex)}});m.each("tabIndex readOnly maxLength cellSpacing cellPadding rowSpan colSpan useMap frameBorder contentEditable".split(" "),function(){m.propFix[this.toLowerCase()]=this});m.fn.extend({addClass:function(a){var b,
c,f,e,d,g;return O(a)?this.each(function(b){m(this).addClass(a.call(this,b,fa(this)))}):(b=Ta(a)).length?this.each(function(){if(f=fa(this),c=1===this.nodeType&&" "+ga(f)+" "){for(d=0;d<b.length;d++)e=b[d],0>c.indexOf(" "+e+" ")&&(c+=e+" ");g=ga(c);f!==g&&this.setAttribute("class",g)}}):this},removeClass:function(a){var b,c,f,e,d,g;return O(a)?this.each(function(b){m(this).removeClass(a.call(this,b,fa(this)))}):arguments.length?(b=Ta(a)).length?this.each(function(){if(f=fa(this),c=1===this.nodeType&&
" "+ga(f)+" "){for(d=0;d<b.length;d++)for(e=b[d];-1<c.indexOf(" "+e+" ");)c=c.replace(" "+e+" "," ");g=ga(c);f!==g&&this.setAttribute("class",g)}}):this:this.attr("class","")},toggleClass:function(a,b){var c,f,e,d,g=typeof a,l="string"===g||Array.isArray(a);return O(a)?this.each(function(c){m(this).toggleClass(a.call(this,c,fa(this),b),b)}):"boolean"==typeof b&&l?b?this.addClass(a):this.removeClass(a):(c=Ta(a),this.each(function(){if(l){d=m(this);for(e=0;e<c.length;e++)f=c[e],d.hasClass(f)?d.removeClass(f):
d.addClass(f)}else void 0!==a&&"boolean"!==g||((f=fa(this))&&H.set(this,"__className__",f),this.setAttribute&&this.setAttribute("class",f||!1===a?"":H.get(this,"__className__")||""))}))},hasClass:function(a){var b,c=0;for(a=" "+a+" ";b=this[c++];)if(1===b.nodeType&&-1<(" "+ga(fa(b))+" ").indexOf(a))return!0;return!1}});var md=/\r/g;m.fn.extend({val:function(a){var b,c,f,e=this[0];return arguments.length?(f=O(a),this.each(function(c){var e;1===this.nodeType&&(null==(e=f?a.call(this,c,m(this).val()):
a)?e="":"number"==typeof e?e+="":Array.isArray(e)&&(e=m.map(e,function(a){return null==a?"":a+""})),(b=m.valHooks[this.type]||m.valHooks[this.nodeName.toLowerCase()])&&"set"in b&&void 0!==b.set(this,e,"value")||(this.value=e))})):e?(b=m.valHooks[e.type]||m.valHooks[e.nodeName.toLowerCase()])&&"get"in b&&void 0!==(c=b.get(e,"value"))?c:"string"==typeof(c=e.value)?c.replace(md,""):null==c?"":c:void 0}});m.extend({valHooks:{option:{get:function(a){var b=m.find.attr(a,"value");return null!=b?b:ga(m.text(a))}},
select:{get:function(a){var b,c,f=a.options,e=a.selectedIndex,d="select-one"===a.type,l=d?null:[],n=d?e+1:f.length;for(c=0>e?n:d?e:0;c<n;c++)if(((b=f[c]).selected||c===e)&&!b.disabled&&(!b.parentNode.disabled||!g(b.parentNode,"optgroup"))){if(a=m(b).val(),d)return a;l.push(a)}return l},set:function(a,b){for(var c,f,e=a.options,d=m.makeArray(b),g=e.length;g--;)((f=e[g]).selected=-1<m.inArray(m.valHooks.option.get(f),d))&&(c=!0);return c||(a.selectedIndex=-1),d}}}});m.each(["radio","checkbox"],function(){m.valHooks[this]=
{set:function(a,b){if(Array.isArray(b))return a.checked=-1<m.inArray(m(a).val(),b)}};P.checkOn||(m.valHooks[this].get=function(a){return null===a.getAttribute("value")?"on":a.value})});var bb=a.location,pc=Date.now(),Ib=/\?/;m.parseXML=function(b){var c,f;if(!b||"string"!=typeof b)return null;try{c=(new a.DOMParser).parseFromString(b,"text/xml")}catch(e){}return f=c&&c.getElementsByTagName("parsererror")[0],c&&!f||m.error("Invalid XML: "+(f?m.map(f.childNodes,function(a){return a.textContent}).join("\n"):
b)),c};var qc=/^(?:focusinfocus|focusoutblur)$/,rc=function(a){a.stopPropagation()};m.extend(m.event,{trigger:function(b,c,f,e){var d,g,l,n,j,r,q,p,t=[f||N],s=Wa.call(b,"type")?b.type:b;d=Wa.call(b,"namespace")?b.namespace.split("."):[];if(g=p=l=f=f||N,3!==f.nodeType&&8!==f.nodeType&&!qc.test(s+m.event.triggered)&&(-1<s.indexOf(".")&&(s=(d=s.split(".")).shift(),d.sort()),j=0>s.indexOf(":")&&"on"+s,(b=b[m.expando]?b:new m.Event(s,"object"==typeof b&&b)).isTrigger=e?2:3,b.namespace=d.join("."),b.rnamespace=
b.namespace?RegExp("(^|\\.)"+d.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,b.result=void 0,b.target||(b.target=f),c=null==c?[b]:m.makeArray(c,[b]),q=m.event.special[s]||{},e||!q.trigger||!1!==q.trigger.apply(f,c))){if(!e&&!q.noBubble&&!Ia(f)){n=q.delegateType||s;for(qc.test(n+s)||(g=g.parentNode);g;g=g.parentNode)t.push(g),l=g;l===(f.ownerDocument||N)&&t.push(l.defaultView||l.parentWindow||a)}for(d=0;(g=t[d++])&&!b.isPropagationStopped();)p=g,b.type=1<d?n:q.bindType||s,(r=(H.get(g,"events")||Object.create(null))[b.type]&&
H.get(g,"handle"))&&r.apply(g,c),(r=j&&g[j])&&r.apply&&Ya(g)&&(b.result=r.apply(g,c),!1===b.result&&b.preventDefault());return b.type=s,e||b.isDefaultPrevented()||q._default&&!1!==q._default.apply(t.pop(),c)||!Ya(f)||j&&O(f[s])&&!Ia(f)&&((l=f[j])&&(f[j]=null),m.event.triggered=s,b.isPropagationStopped()&&p.addEventListener(s,rc),f[s](),b.isPropagationStopped()&&p.removeEventListener(s,rc),m.event.triggered=void 0,l&&(f[j]=l)),b.result}},simulate:function(a,b,c){a=m.extend(new m.Event,c,{type:a,isSimulated:!0});
m.event.trigger(a,null,b)}});m.fn.extend({trigger:function(a,b){return this.each(function(){m.event.trigger(a,b,this)})},triggerHandler:function(a,b){var c=this[0];if(c)return m.event.trigger(a,b,c,!0)}});var Hc=/\[\]$/,sc=/\r?\n/g,nd=/^(?:submit|button|image|reset|file)$/i,od=/^(?:input|select|textarea|keygen)/i;m.param=function(a,b){var c,f=[],e=function(a,b){var c=O(b)?b():b;f[f.length]=encodeURIComponent(a)+"="+encodeURIComponent(null==c?"":c)};if(null==a)return"";if(Array.isArray(a)||a.jquery&&
!m.isPlainObject(a))m.each(a,function(){e(this.name,this.value)});else for(c in a)Ua(c,a[c],b,e);return f.join("&")};m.fn.extend({serialize:function(){return m.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var a=m.prop(this,"elements");return a?m.makeArray(a):this}).filter(function(){var a=this.type;return this.name&&!m(this).is(":disabled")&&od.test(this.nodeName)&&!nd.test(a)&&(this.checked||!Za.test(a))}).map(function(a,b){var c=m(this).val();return null==c?
null:Array.isArray(c)?m.map(c,function(a){return{name:b.name,value:a.replace(sc,"\r\n")}}):{name:b.name,value:c.replace(sc,"\r\n")}}).get()}});var pd=/%20/g,qd=/#.*$/,rd=/([?&])_=[^&]*/,sd=/^(.*?):[ \t]*([^\r\n]*)$/gm,td=/^(?:GET|HEAD)$/,ud=/^\/\//,tc={},wb={},uc="*/".concat("*"),Jb=N.createElement("a");Jb.href=bb.href;m.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:bb.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(bb.protocol),global:!0,processData:!0,
async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":uc,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":m.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(a,b){return b?Ea(Ea(a,m.ajaxSettings),
b):Ea(m.ajaxSettings,a)},ajaxPrefilter:la(tc),ajaxTransport:la(wb),ajax:function(b,c){function f(b,c,l,j){var p,t,I,M,K=c;if(!r){r=!0;n&&a.clearTimeout(n);e=void 0;g=j||"";A.readyState=0<b?4:0;j=200<=b&&300>b||304===b;if(l){I=s;for(var D=A,E,y,Q,G,x=I.contents,W=I.dataTypes;"*"===W[0];)W.shift(),void 0===E&&(E=I.mimeType||D.getResponseHeader("Content-Type"));if(E)for(y in x)if(x[y]&&x[y].test(E)){W.unshift(y);break}if(W[0]in l)Q=W[0];else{for(y in l){if(!W[0]||I.converters[y+" "+W[0]]){Q=y;break}G||
(G=y)}Q=Q||G}l=Q?(Q!==W[0]&&W.unshift(Q),l[Q]):void 0;I=l}!j&&-1<m.inArray("script",s.dataTypes)&&0>m.inArray("json",s.dataTypes)&&(s.converters["text script"]=function(){});var z;a:{l=s;E=I;y=A;Q=j;var v,u,J;I={};D=l.dataTypes.slice();if(D[1])for(v in l.converters)I[v.toLowerCase()]=l.converters[v];for(G=D.shift();G;)if(l.responseFields[G]&&(y[l.responseFields[G]]=E),!J&&Q&&l.dataFilter&&(E=l.dataFilter(E,l.dataType)),J=G,G=D.shift())if("*"===G)G=J;else if("*"!==J&&J!==G){if(!(v=I[J+" "+G]||I["* "+
G]))for(z in I)if((u=z.split(" "))[1]===G&&(v=I[J+" "+u[0]]||I["* "+u[0]])){!0===v?v=I[z]:!0!==I[z]&&(G=u[0],D.unshift(u[1]));break}if(!0!==v)if(v&&l["throws"])E=v(E);else try{E=v(E)}catch(ca){z={state:"parsererror",error:v?ca:"No conversion from "+J+" to "+G};break a}}z={state:"success",data:E}}I=z;j?(s.ifModified&&((M=A.getResponseHeader("Last-Modified"))&&(m.lastModified[d]=M),(M=A.getResponseHeader("etag"))&&(m.etag[d]=M)),204===b||"HEAD"===s.type?K="nocontent":304===b?K="notmodified":(K=I.state,
p=I.data,j=!(t=I.error))):(t=K,!b&&K||(K="error",0>b&&(b=0)));A.status=b;A.statusText=(c||K)+"";j?F.resolveWith(B,[p,K,A]):F.rejectWith(B,[A,K,t]);A.statusCode(R);R=void 0;q&&L.trigger(j?"ajaxSuccess":"ajaxError",[A,s,j?p:t]);C.fireWith(B,[A,K]);q&&(L.trigger("ajaxComplete",[A,s]),--m.active||m.event.trigger("ajaxStop"))}}"object"==typeof b&&(c=b,b=void 0);c=c||{};var e,d,g,l,n,j,r,q,p,t,s=m.ajaxSetup({},c),B=s.context||s,L=s.context&&(B.nodeType||B.jquery)?m(B):m.event,F=m.Deferred(),C=m.Callbacks("once memory"),
R=s.statusCode||{},M={},K={},D="canceled",A={readyState:0,getResponseHeader:function(a){var b;if(r){if(!l)for(l={};b=sd.exec(g);)l[b[1].toLowerCase()+" "]=(l[b[1].toLowerCase()+" "]||[]).concat(b[2]);b=l[a.toLowerCase()+" "]}return null==b?null:b.join(", ")},getAllResponseHeaders:function(){return r?g:null},setRequestHeader:function(a,b){return null==r&&(a=K[a.toLowerCase()]=K[a.toLowerCase()]||a,M[a]=b),this},overrideMimeType:function(a){return null==r&&(s.mimeType=a),this},statusCode:function(a){var b;
if(a)if(r)A.always(a[A.status]);else for(b in a)R[b]=[R[b],a[b]];return this},abort:function(a){a=a||D;return e&&e.abort(a),f(0,a),this}};if(F.promise(A),s.url=((b||s.url||bb.href)+"").replace(ud,bb.protocol+"//"),s.type=c.method||c.type||s.method||s.type,s.dataTypes=(s.dataType||"*").toLowerCase().match(pa)||[""],null==s.crossDomain){j=N.createElement("a");try{j.href=s.url,j.href=j.href,s.crossDomain=Jb.protocol+"//"+Jb.host!=j.protocol+"//"+j.host}catch(E){s.crossDomain=!0}}if(s.data&&s.processData&&
"string"!=typeof s.data&&(s.data=m.param(s.data,s.traditional)),Da(tc,s,c,A),r)return A;for(p in(q=m.event&&s.global)&&0==m.active++&&m.event.trigger("ajaxStart"),s.type=s.type.toUpperCase(),s.hasContent=!td.test(s.type),d=s.url.replace(qd,""),s.hasContent?s.data&&s.processData&&0===(s.contentType||"").indexOf("application/x-www-form-urlencoded")&&(s.data=s.data.replace(pd,"+")):(t=s.url.slice(d.length),s.data&&(s.processData||"string"==typeof s.data)&&(d+=(Ib.test(d)?"&":"?")+s.data,delete s.data),
!1===s.cache&&(d=d.replace(rd,"$1"),t=(Ib.test(d)?"&":"?")+"_="+pc++ +t),s.url=d+t),s.ifModified&&(m.lastModified[d]&&A.setRequestHeader("If-Modified-Since",m.lastModified[d]),m.etag[d]&&A.setRequestHeader("If-None-Match",m.etag[d])),(s.data&&s.hasContent&&!1!==s.contentType||c.contentType)&&A.setRequestHeader("Content-Type",s.contentType),A.setRequestHeader("Accept",s.dataTypes[0]&&s.accepts[s.dataTypes[0]]?s.accepts[s.dataTypes[0]]+("*"!==s.dataTypes[0]?", "+uc+"; q=0.01":""):s.accepts["*"]),s.headers)A.setRequestHeader(p,
s.headers[p]);if(s.beforeSend&&(!1===s.beforeSend.call(B,A,s)||r))return A.abort();if(D="abort",C.add(s.complete),A.done(s.success),A.fail(s.error),e=Da(wb,s,c,A)){if(A.readyState=1,q&&L.trigger("ajaxSend",[A,s]),r)return A;s.async&&0<s.timeout&&(n=a.setTimeout(function(){A.abort("timeout")},s.timeout));try{r=!1,e.send(M,f)}catch(y){if(r)throw y;f(-1,y)}}else f(-1,"No Transport");return A},getJSON:function(a,b,c){return m.get(a,b,c,"json")},getScript:function(a,b){return m.get(a,void 0,b,"script")}});
m.each(["get","post"],function(a,b){m[b]=function(a,c,f,e){return O(c)&&(e=e||f,f=c,c=void 0),m.ajax(m.extend({url:a,type:b,dataType:e,data:c,success:f},m.isPlainObject(a)&&a))}});m.ajaxPrefilter(function(a){for(var b in a.headers)"content-type"===b.toLowerCase()&&(a.contentType=a.headers[b]||"")});m._evalUrl=function(a,b,c){return m.ajax({url:a,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(a){m.globalEval(a,b,c)}})};m.fn.extend({wrapAll:function(a){var b;
return this[0]&&(O(a)&&(a=a.call(this[0])),b=m(a,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&b.insertBefore(this[0]),b.map(function(){for(var a=this;a.firstElementChild;)a=a.firstElementChild;return a}).append(this)),this},wrapInner:function(a){return O(a)?this.each(function(b){m(this).wrapInner(a.call(this,b))}):this.each(function(){var b=m(this),c=b.contents();c.length?c.wrapAll(a):b.append(a)})},wrap:function(a){var b=O(a);return this.each(function(c){m(this).wrapAll(b?a.call(this,
c):a)})},unwrap:function(a){return this.parent(a).not("body").each(function(){m(this).replaceWith(this.childNodes)}),this}});m.expr.pseudos.hidden=function(a){return!m.expr.pseudos.visible(a)};m.expr.pseudos.visible=function(a){return!(!a.offsetWidth&&!a.offsetHeight&&!a.getClientRects().length)};m.ajaxSettings.xhr=function(){try{return new a.XMLHttpRequest}catch(b){}};var vd={"0":200,1223:204},cb=m.ajaxSettings.xhr();P.cors=!!cb&&"withCredentials"in cb;P.ajax=cb=!!cb;m.ajaxTransport(function(b){var c,
f;if(P.cors||cb&&!b.crossDomain)return{send:function(e,d){var g,l=b.xhr();if(l.open(b.type,b.url,b.async,b.username,b.password),b.xhrFields)for(g in b.xhrFields)l[g]=b.xhrFields[g];for(g in b.mimeType&&l.overrideMimeType&&l.overrideMimeType(b.mimeType),b.crossDomain||e["X-Requested-With"]||(e["X-Requested-With"]="XMLHttpRequest"),e)l.setRequestHeader(g,e[g]);c=function(a){return function(){c&&(c=f=l.onload=l.onerror=l.onabort=l.ontimeout=l.onreadystatechange=null,"abort"===a?l.abort():"error"===a?
"number"!=typeof l.status?d(0,"error"):d(l.status,l.statusText):d(vd[l.status]||l.status,l.statusText,"text"!==(l.responseType||"text")||"string"!=typeof l.responseText?{binary:l.response}:{text:l.responseText},l.getAllResponseHeaders()))}};l.onload=c();f=l.onerror=l.ontimeout=c("error");void 0!==l.onabort?l.onabort=f:l.onreadystatechange=function(){4===l.readyState&&a.setTimeout(function(){c&&f()})};c=c("abort");try{l.send(b.hasContent&&b.data||null)}catch(n){if(c)throw n;}},abort:function(){c&&
c()}}});m.ajaxPrefilter(function(a){a.crossDomain&&(a.contents.script=!1)});m.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(a){return m.globalEval(a),a}}});m.ajaxPrefilter("script",function(a){void 0===a.cache&&(a.cache=!1);a.crossDomain&&(a.type="GET")});m.ajaxTransport("script",function(a){var b,c;if(a.crossDomain||a.scriptAttrs)return{send:function(f,
e){b=m("<script>").attr(a.scriptAttrs||{}).prop({charset:a.scriptCharset,src:a.url}).on("load error",c=function(a){b.remove();c=null;a&&e("error"===a.type?404:200,a.type)});N.head.appendChild(b[0])},abort:function(){c&&c()}}});var vc,wc=[],Kb=/(=)\?(?=&|$)|\?\?/;m.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var a=wc.pop()||m.expando+"_"+pc++;return this[a]=!0,a}});m.ajaxPrefilter("json jsonp",function(b,c,f){var e,d,g,l=!1!==b.jsonp&&(Kb.test(b.url)?"url":"string"==typeof b.data&&0===(b.contentType||
"").indexOf("application/x-www-form-urlencoded")&&Kb.test(b.data)&&"data");if(l||"jsonp"===b.dataTypes[0])return e=b.jsonpCallback=O(b.jsonpCallback)?b.jsonpCallback():b.jsonpCallback,l?b[l]=b[l].replace(Kb,"$1"+e):!1!==b.jsonp&&(b.url+=(Ib.test(b.url)?"&":"?")+b.jsonp+"="+e),b.converters["script json"]=function(){return g||m.error(e+" was not called"),g[0]},b.dataTypes[0]="json",d=a[e],a[e]=function(){g=arguments},f.always(function(){void 0===d?m(a).removeProp(e):a[e]=d;b[e]&&(b.jsonpCallback=c.jsonpCallback,
wc.push(e));g&&O(d)&&d(g[0]);g=d=void 0}),"script"});P.createHTMLDocument=((vc=N.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===vc.childNodes.length);m.parseHTML=function(a,b,c){return"string"!=typeof a?[]:("boolean"==typeof b&&(c=b,b=!1),b||(P.createHTMLDocument?((f=(b=N.implementation.createHTMLDocument("")).createElement("base")).href=N.location.href,b.head.appendChild(f)):b=N),d=!c&&[],(e=cc.exec(a))?[b.createElement(e[1])]:(e=R([a],b,d),d&&d.length&&m(d).remove(),
m.merge([],e.childNodes)));var f,e,d};m.fn.load=function(a,b,c){var f,e,d,g=this,l=a.indexOf(" ");return-1<l&&(f=ga(a.slice(l)),a=a.slice(0,l)),O(b)?(c=b,b=void 0):b&&"object"==typeof b&&(e="POST"),0<g.length&&m.ajax({url:a,type:e||"GET",dataType:"html",data:b}).done(function(a){d=arguments;g.html(f?m("<div>").append(m.parseHTML(a)).find(f):a)}).always(c&&function(a,b){g.each(function(){c.apply(this,d||[a.responseText,b,a])})}),this};m.expr.pseudos.animated=function(a){return m.grep(m.timers,function(b){return a===
b.elem}).length};m.offset={setOffset:function(a,b,c){var f,e,d,g,l,n,j=m.css(a,"position"),r=m(a),q={};"static"===j&&(a.style.position="relative");l=r.offset();d=m.css(a,"top");n=m.css(a,"left");("absolute"===j||"fixed"===j)&&-1<(d+n).indexOf("auto")?(g=(f=r.position()).top,e=f.left):(g=parseFloat(d)||0,e=parseFloat(n)||0);O(b)&&(b=b.call(a,c,m.extend({},l)));null!=b.top&&(q.top=b.top-l.top+g);null!=b.left&&(q.left=b.left-l.left+e);"using"in b?b.using.call(a,q):r.css(q)}};m.fn.extend({offset:function(a){if(arguments.length)return void 0===
a?this:this.each(function(b){m.offset.setOffset(this,a,b)});var b,c,f=this[0];return f?f.getClientRects().length?(b=f.getBoundingClientRect(),c=f.ownerDocument.defaultView,{top:b.top+c.pageYOffset,left:b.left+c.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var a,b,c,f=this[0],e={top:0,left:0};if("fixed"===m.css(f,"position"))b=f.getBoundingClientRect();else{b=this.offset();c=f.ownerDocument;for(a=f.offsetParent||c.documentElement;a&&(a===c.body||a===c.documentElement)&&"static"===
m.css(a,"position");)a=a.parentNode;a&&a!==f&&1===a.nodeType&&((e=m(a).offset()).top+=m.css(a,"borderTopWidth",!0),e.left+=m.css(a,"borderLeftWidth",!0))}return{top:b.top-e.top-m.css(f,"marginTop",!0),left:b.left-e.left-m.css(f,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var a=this.offsetParent;a&&"static"===m.css(a,"position");)a=a.offsetParent;return a||Ga})}});m.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(a,b){var c="pageYOffset"===b;m.fn[a]=
function(f){return xa(this,function(a,f,e){var d;if(Ia(a)?d=a:9===a.nodeType&&(d=a.defaultView),void 0===e)return d?d[b]:a[f];d?d.scrollTo(c?d.pageXOffset:e,c?e:d.pageYOffset):a[f]=e},a,f,arguments.length)}});m.each(["top","left"],function(a,b){m.cssHooks[b]=ia(P.pixelPosition,function(a,c){if(c)return c=Y(a,b),ub.test(c)?m(a).position()[b]+"px":c})});m.each({Height:"height",Width:"width"},function(a,b){m.each({padding:"inner"+a,content:b,"":"outer"+a},function(c,f){m.fn[f]=function(e,d){var g=arguments.length&&
(c||"boolean"!=typeof e),l=c||(!0===e||!0===d?"margin":"border");return xa(this,function(b,c,e){var d;return Ia(b)?0===f.indexOf("outer")?b["inner"+a]:b.document.documentElement["client"+a]:9===b.nodeType?(d=b.documentElement,Math.max(b.body["scroll"+a],d["scroll"+a],b.body["offset"+a],d["offset"+a],d["client"+a])):void 0===e?m.css(b,c,l):m.style(b,c,e,l)},b,g?e:void 0,g)}})});m.each("ajaxStart ajaxStop ajaxComplete ajaxError ajaxSuccess ajaxSend".split(" "),function(a,b){m.fn[b]=function(a){return this.on(b,
a)}});m.fn.extend({bind:function(a,b,c){return this.on(a,null,b,c)},unbind:function(a,b){return this.off(a,null,b)},delegate:function(a,b,c,f){return this.on(b,a,c,f)},undelegate:function(a,b,c){return 1===arguments.length?this.off(a,"**"):this.off(b,a||"**",c)},hover:function(a,b){return this.mouseenter(a).mouseleave(b||a)}});m.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),
function(a,b){m.fn[b]=function(a,c){return 0<arguments.length?this.on(b,null,a,c):this.trigger(b)}});var wd=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;m.proxy=function(a,b){var c,f,e;if("string"==typeof b&&(c=a[b],b=a,a=c),O(a))return f=aa.call(arguments,2),(e=function(){return a.apply(b||this,f.concat(aa.call(arguments)))}).guid=a.guid=a.guid||m.guid++,e};m.holdReady=function(a){a?m.readyWait++:m.ready(!0)};m.isArray=Array.isArray;m.parseJSON=JSON.parse;m.nodeName=g;m.isFunction=O;m.isWindow=
Ia;m.camelCase=s;m.type=d;m.now=Date.now;m.isNumeric=function(a){var b=m.type(a);return("number"===b||"string"===b)&&!isNaN(a-parseFloat(a))};m.trim=function(a){return null==a?"":(a+"").replace(wd,"$1")};"function"==typeof define&&define.amd&&define("jquery",[],function(){return m});var xd=a.jQuery,yd=a.$;return m.noConflict=function(b){return a.$===m&&(a.$=yd),b&&a.jQuery===m&&(a.jQuery=xd),m},"undefined"==typeof b&&(a.jQuery=a.$=m),m});function getInternetExplorerVersion(){var a=-1;"Microsoft Internet Explorer"==navigator.appName&&null!=/MSIE ([0-9]{1,}[.0-9]{0,})/.exec(navigator.userAgent)&&(a=parseFloat(RegExp.$1));return a}var ie=getInternetExplorerVersion();function getQueryVariable(a){for(var b=window.location.search.substring(1).split("&"),c=0;c<b.length;c++){var d=b[c].match(/([^=]+?)=(.+)/);if(d&&decodeURIComponent(d[1])==a)return decodeURIComponent(d[2])}};(function(){var a=function(){this.init()};a.prototype={init:function(){var a=this||b;a._counter=1E3;a._html5AudioPool=[];a.html5PoolSize=10;a._codecs={};a._howls=[];a._muted=!1;a._volume=1;a._canPlayEvent="canplaythrough";a._navigator="undefined"!==typeof window&&window.navigator?window.navigator:null;a.masterGain=null;a.noAudio=!1;a.usingWebAudio=!0;a.autoSuspend=!1;a.ctx=null;a.autoUnlock=!0;a._setup();return a},volume:function(a){var c=this||b;a=parseFloat(a);c.ctx||p();if("undefined"!==typeof a&&
0<=a&&1>=a){c._volume=a;if(c._muted)return c;c.usingWebAudio&&c.masterGain.gain.setValueAtTime(a,b.ctx.currentTime);for(var e=0;e<c._howls.length;e++)if(!c._howls[e]._webAudio)for(var d=c._howls[e]._getSoundIds(),g=0;g<d.length;g++){var j=c._howls[e]._soundById(d[g]);j&&j._node&&(j._node.volume=j._volume*a)}return c}return c._volume},mute:function(a){var c=this||b;c.ctx||p();c._muted=a;c.usingWebAudio&&c.masterGain.gain.setValueAtTime(a?0:c._volume,b.ctx.currentTime);for(var e=0;e<c._howls.length;e++)if(!c._howls[e]._webAudio)for(var d=
c._howls[e]._getSoundIds(),g=0;g<d.length;g++){var j=c._howls[e]._soundById(d[g]);j&&j._node&&(j._node.muted=a?!0:j._muted)}return c},stop:function(){for(var a=this||b,c=0;c<a._howls.length;c++)a._howls[c].stop();return a},unload:function(){for(var a=this||b,c=a._howls.length-1;0<=c;c--)a._howls[c].unload();a.usingWebAudio&&(a.ctx&&"undefined"!==typeof a.ctx.close)&&(a.ctx.close(),a.ctx=null,p());return a},codecs:function(a){return(this||b)._codecs[a.replace(/^x-/,"")]},_setup:function(){var a=this||
b;a.state=a.ctx?a.ctx.state||"suspended":"suspended";a._autoSuspend();if(!a.usingWebAudio)if("undefined"!==typeof Audio)try{var c=new Audio;"undefined"===typeof c.oncanplaythrough&&(a._canPlayEvent="canplay")}catch(e){a.noAudio=!0}else a.noAudio=!0;try{c=new Audio,c.muted&&(a.noAudio=!0)}catch(d){}a.noAudio||a._setupCodecs();return a},_setupCodecs:function(){var a=this||b,c=null;try{c="undefined"!==typeof Audio?new Audio:null}catch(e){return a}if(!c||"function"!==typeof c.canPlayType)return a;var d=
c.canPlayType("audio/mpeg;").replace(/^no$/,""),g=a._navigator?a._navigator.userAgent:"",j=g.match(/OPR\/(\d+)/g),j=j&&33>parseInt(j[0].split("/")[1],10),p=-1!==g.indexOf("Safari")&&-1===g.indexOf("Chrome"),g=g.match(/Version\/(.*?) /),g=p&&g&&15>parseInt(g[1],10);a._codecs={mp3:!(j||!d&&!c.canPlayType("audio/mp3;").replace(/^no$/,"")),mpeg:!!d,opus:!!c.canPlayType('audio/ogg; codecs="opus"').replace(/^no$/,""),ogg:!!c.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/,""),oga:!!c.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/,
""),wav:!!(c.canPlayType('audio/wav; codecs="1"')||c.canPlayType("audio/wav")).replace(/^no$/,""),aac:!!c.canPlayType("audio/aac;").replace(/^no$/,""),caf:!!c.canPlayType("audio/x-caf;").replace(/^no$/,""),m4a:!!(c.canPlayType("audio/x-m4a;")||c.canPlayType("audio/m4a;")||c.canPlayType("audio/aac;")).replace(/^no$/,""),m4b:!!(c.canPlayType("audio/x-m4b;")||c.canPlayType("audio/m4b;")||c.canPlayType("audio/aac;")).replace(/^no$/,""),mp4:!!(c.canPlayType("audio/x-mp4;")||c.canPlayType("audio/mp4;")||
c.canPlayType("audio/aac;")).replace(/^no$/,""),weba:!(g||!c.canPlayType('audio/webm; codecs="vorbis"').replace(/^no$/,"")),webm:!(g||!c.canPlayType('audio/webm; codecs="vorbis"').replace(/^no$/,"")),dolby:!!c.canPlayType('audio/mp4; codecs="ec-3"').replace(/^no$/,""),flac:!!(c.canPlayType("audio/x-flac;")||c.canPlayType("audio/flac;")).replace(/^no$/,"")};return a},_unlockAudio:function(){var a=this||b;if(!a._audioUnlocked&&a.ctx){a._audioUnlocked=!1;a.autoUnlock=!1;!a._mobileUnloaded&&44100!==a.ctx.sampleRate&&
(a._mobileUnloaded=!0,a.unload());a._scratchBuffer=a.ctx.createBuffer(1,1,22050);var c=function(){for(;a._html5AudioPool.length<a.html5PoolSize;)try{var b=new Audio;b._unlocked=!0;a._releaseHtml5Audio(b)}catch(e){a.noAudio=!0;break}for(b=0;b<a._howls.length;b++)if(!a._howls[b]._webAudio)for(var d=a._howls[b]._getSoundIds(),g=0;g<d.length;g++){var j=a._howls[b]._soundById(d[g]);j&&(j._node&&!j._node._unlocked)&&(j._node._unlocked=!0,j._node.load())}a._autoResume();var p=a.ctx.createBufferSource();
p.buffer=a._scratchBuffer;p.connect(a.ctx.destination);"undefined"===typeof p.start?p.noteOn(0):p.start(0);"function"===typeof a.ctx.resume&&a.ctx.resume();p.onended=function(){p.disconnect(0);a._audioUnlocked=!0;document.removeEventListener("touchstart",c,!0);document.removeEventListener("touchend",c,!0);document.removeEventListener("click",c,!0);document.removeEventListener("keydown",c,!0);for(var b=0;b<a._howls.length;b++)a._howls[b]._emit("unlock")}};document.addEventListener("touchstart",c,!0);
document.addEventListener("touchend",c,!0);document.addEventListener("click",c,!0);document.addEventListener("keydown",c,!0);return a}},_obtainHtml5Audio:function(){var a=this||b;if(a._html5AudioPool.length)return a._html5AudioPool.pop();(a=(new Audio).play())&&("undefined"!==typeof Promise&&(a instanceof Promise||"function"===typeof a.then))&&a.catch(function(){console.warn("HTML5 Audio pool exhausted, returning potentially locked audio object.")});return new Audio},_releaseHtml5Audio:function(a){var c=
this||b;a._unlocked&&c._html5AudioPool.push(a);return c},_autoSuspend:function(){var a=this;if(a.autoSuspend&&a.ctx&&"undefined"!==typeof a.ctx.suspend&&b.usingWebAudio){for(var c=0;c<a._howls.length;c++)if(a._howls[c]._webAudio)for(var e=0;e<a._howls[c]._sounds.length;e++)if(!a._howls[c]._sounds[e]._paused)return a;a._suspendTimer&&clearTimeout(a._suspendTimer);a._suspendTimer=setTimeout(function(){if(a.autoSuspend){a._suspendTimer=null;a.state="suspending";var b=function(){a.state="suspended";a._resumeAfterSuspend&&
(delete a._resumeAfterSuspend,a._autoResume())};a.ctx.suspend().then(b,b)}},3E4);return a}},_autoResume:function(){var a=this;if(a.ctx&&"undefined"!==typeof a.ctx.resume&&b.usingWebAudio)return"running"===a.state&&"interrupted"!==a.ctx.state&&a._suspendTimer?(clearTimeout(a._suspendTimer),a._suspendTimer=null):"suspended"===a.state||"running"===a.state&&"interrupted"===a.ctx.state?(a.ctx.resume().then(function(){a.state="running";for(var b=0;b<a._howls.length;b++)a._howls[b]._emit("resume")}),a._suspendTimer&&
(clearTimeout(a._suspendTimer),a._suspendTimer=null)):"suspending"===a.state&&(a._resumeAfterSuspend=!0),a}};var b=new a,c=function(a){!a.src||0===a.src.length?console.error("An array of source files must be passed with any new Howl."):this.init(a)};c.prototype={init:function(a){var c=this;b.ctx||p();c._autoplay=a.autoplay||!1;c._format="string"!==typeof a.format?a.format:[a.format];c._html5=a.html5||!1;c._muted=a.mute||!1;c._loop=a.loop||!1;c._pool=a.pool||5;c._preload="boolean"===typeof a.preload||
"metadata"===a.preload?a.preload:!0;c._rate=a.rate||1;c._sprite=a.sprite||{};c._src="string"!==typeof a.src?a.src:[a.src];c._volume=void 0!==a.volume?a.volume:1;c._xhr={method:a.xhr&&a.xhr.method?a.xhr.method:"GET",headers:a.xhr&&a.xhr.headers?a.xhr.headers:null,withCredentials:a.xhr&&a.xhr.withCredentials?a.xhr.withCredentials:!1};c._duration=0;c._state="unloaded";c._sounds=[];c._endTimers={};c._queue=[];c._playLock=!1;c._onend=a.onend?[{fn:a.onend}]:[];c._onfade=a.onfade?[{fn:a.onfade}]:[];c._onload=
a.onload?[{fn:a.onload}]:[];c._onloaderror=a.onloaderror?[{fn:a.onloaderror}]:[];c._onplayerror=a.onplayerror?[{fn:a.onplayerror}]:[];c._onpause=a.onpause?[{fn:a.onpause}]:[];c._onplay=a.onplay?[{fn:a.onplay}]:[];c._onstop=a.onstop?[{fn:a.onstop}]:[];c._onmute=a.onmute?[{fn:a.onmute}]:[];c._onvolume=a.onvolume?[{fn:a.onvolume}]:[];c._onrate=a.onrate?[{fn:a.onrate}]:[];c._onseek=a.onseek?[{fn:a.onseek}]:[];c._onunlock=a.onunlock?[{fn:a.onunlock}]:[];c._onresume=[];c._webAudio=b.usingWebAudio&&!c._html5;
"undefined"!==typeof b.ctx&&(b.ctx&&b.autoUnlock)&&b._unlockAudio();b._howls.push(c);c._autoplay&&c._queue.push({event:"play",action:function(){c.play()}});c._preload&&"none"!==c._preload&&c.load();return c},load:function(){var a=null;if(b.noAudio)this._emit("loaderror",null,"No audio support.");else{"string"===typeof this._src&&(this._src=[this._src]);for(var c=0;c<this._src.length;c++){var l,q;if(this._format&&this._format[c])l=this._format[c];else{q=this._src[c];if("string"!==typeof q){this._emit("loaderror",
null,"Non-string found in selected audio sources - ignoring.");continue}(l=/^data:audio\/([^;,]+);/i.exec(q))||(l=/\.([^.]+)$/.exec(q.split("?",1)[0]));l&&(l=l[1].toLowerCase())}l||console.warn('No file extension was found. Consider using the "format" property or specify an extension.');if(l&&b.codecs(l)){a=this._src[c];break}}if(a){this._src=a;this._state="loading";"https:"===window.location.protocol&&"http:"===a.slice(0,5)&&(this._html5=!0,this._webAudio=!1);new d(this);if(this._webAudio){var r=
this,p=r._src;if(e[p])r._duration=e[p].duration,j(r);else if(/^data:[^;]+;base64,/.test(p)){a=atob(p.split(",")[1]);c=new Uint8Array(a.length);for(l=0;l<a.length;++l)c[l]=a.charCodeAt(l);g(c.buffer,r)}else{var s=new XMLHttpRequest;s.open(r._xhr.method,p,!0);s.withCredentials=r._xhr.withCredentials;s.responseType="arraybuffer";r._xhr.headers&&Object.keys(r._xhr.headers).forEach(function(a){s.setRequestHeader(a,r._xhr.headers[a])});s.onload=function(){var a=(s.status+"")[0];"0"!==a&&"2"!==a&&"3"!==
a?r._emit("loaderror",null,"Failed loading audio file with status: "+s.status+"."):g(s.response,r)};s.onerror=function(){r._webAudio&&(r._html5=!0,r._webAudio=!1,r._sounds=[],delete e[p],r.load())};try{s.send()}catch(C){s.onerror()}}}return this}this._emit("loaderror",null,"No codec support for selected audio sources.")}},play:function(a,c){var e=this,d=null;if("number"===typeof a)d=a,a=null;else{if("string"===typeof a&&"loaded"===e._state&&!e._sprite[a])return null;if("undefined"===typeof a&&(a=
"__default",!e._playLock)){for(var g=0,j=0;j<e._sounds.length;j++)e._sounds[j]._paused&&!e._sounds[j]._ended&&(g++,d=e._sounds[j]._id);1===g?a=null:d=null}}var p=d?e._soundById(d):e._inactiveSound();if(!p)return null;d&&!a&&(a=p._sprite||"__default");if("loaded"!==e._state){p._sprite=a;p._ended=!1;var C=p._id;e._queue.push({event:"play",action:function(){e.play(C)}});return C}if(d&&!p._paused)return c||e._loadQueue("play"),p._id;e._webAudio&&b._autoResume();var F=Math.max(0,0<p._seek?p._seek:e._sprite[a][0]/
1E3),A=Math.max(0,(e._sprite[a][0]+e._sprite[a][1])/1E3-F),L=1E3*A/Math.abs(p._rate),B=e._sprite[a][0]/1E3,K=(e._sprite[a][0]+e._sprite[a][1])/1E3;p._sprite=a;p._ended=!1;var R=function(){p._paused=!1;p._seek=F;p._start=B;p._stop=K;p._loop=!(!p._loop&&!e._sprite[a][2])};if(F>=K)e._ended(p);else{var D=p._node;if(e._webAudio)d=function(){e._playLock=!1;R();e._refreshBuffer(p);D.gain.setValueAtTime(p._muted||e._muted?0:p._volume,b.ctx.currentTime);p._playStart=b.ctx.currentTime;"undefined"===typeof D.bufferSource.start?
p._loop?D.bufferSource.noteGrainOn(0,F,86400):D.bufferSource.noteGrainOn(0,F,A):p._loop?D.bufferSource.start(0,F,86400):D.bufferSource.start(0,F,A);Infinity!==L&&(e._endTimers[p._id]=setTimeout(e._ended.bind(e,p),L));c||setTimeout(function(){e._emit("play",p._id);e._loadQueue()},0)},"running"===b.state&&"interrupted"!==b.ctx.state?d():(e._playLock=!0,e.once("resume",d),e._clearTimer(p._id));else{var J=function(){D.currentTime=F;D.muted=p._muted||e._muted||b._muted||D.muted;D.volume=p._volume*b.volume();
D.playbackRate=p._rate;try{var d=D.play();d&&"undefined"!==typeof Promise&&(d instanceof Promise||"function"===typeof d.then)?(e._playLock=!0,R(),d.then(function(){e._playLock=!1;D._unlocked=!0;c?e._loadQueue():e._emit("play",p._id)}).catch(function(){e._playLock=!1;e._emit("playerror",p._id,"Playback was unable to start. This is most commonly an issue on mobile devices and Chrome where playback was not within a user interaction.");p._ended=!0;p._paused=!0})):c||(e._playLock=!1,R(),e._emit("play",
p._id));D.playbackRate=p._rate;D.paused?e._emit("playerror",p._id,"Playback was unable to start. This is most commonly an issue on mobile devices and Chrome where playback was not within a user interaction."):"__default"!==a||p._loop?e._endTimers[p._id]=setTimeout(e._ended.bind(e,p),L):(e._endTimers[p._id]=function(){e._ended(p);D.removeEventListener("ended",e._endTimers[p._id],!1)},D.addEventListener("ended",e._endTimers[p._id],!1))}catch(g){e._emit("playerror",p._id,g)}};"data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA"===
D.src&&(D.src=e._src,D.load());d=window&&window.ejecta||!D.readyState&&b._navigator.isCocoonJS;if(3<=D.readyState||d)J();else{e._playLock=!0;e._state="loading";var M=function(){e._state="loaded";J();D.removeEventListener(b._canPlayEvent,M,!1)};D.addEventListener(b._canPlayEvent,M,!1);e._clearTimer(p._id)}}return p._id}},pause:function(a,b){var c=this;if("loaded"!==c._state||c._playLock)return c._queue.push({event:"pause",action:function(){c.pause(a)}}),c;for(var e=c._getSoundIds(a),d=0;d<e.length;d++){c._clearTimer(e[d]);
var g=c._soundById(e[d]);if(g&&!g._paused&&(g._seek=c.seek(e[d]),g._rateSeek=0,g._paused=!0,c._stopFade(e[d]),g._node))if(c._webAudio){if(!g._node.bufferSource)continue;"undefined"===typeof g._node.bufferSource.stop?g._node.bufferSource.noteOff(0):g._node.bufferSource.stop(0);c._cleanBuffer(g._node)}else(!isNaN(g._node.duration)||Infinity===g._node.duration)&&g._node.pause();b||c._emit("pause",g?g._id:null)}return c},stop:function(a,b){var c=this;if("loaded"!==c._state||c._playLock)return c._queue.push({event:"stop",
action:function(){c.stop(a)}}),c;for(var e=c._getSoundIds(a),d=0;d<e.length;d++){c._clearTimer(e[d]);var g=c._soundById(e[d]);if(g){g._seek=g._start||0;g._rateSeek=0;g._paused=!0;g._ended=!0;c._stopFade(e[d]);if(g._node)if(c._webAudio)g._node.bufferSource&&("undefined"===typeof g._node.bufferSource.stop?g._node.bufferSource.noteOff(0):g._node.bufferSource.stop(0),c._cleanBuffer(g._node));else if(!isNaN(g._node.duration)||Infinity===g._node.duration)g._node.currentTime=g._start||0,g._node.pause(),
Infinity===g._node.duration&&c._clearSound(g._node);b||c._emit("stop",g._id)}}return c},mute:function(a,c){var e=this;if("loaded"!==e._state||e._playLock)return e._queue.push({event:"mute",action:function(){e.mute(a,c)}}),e;if("undefined"===typeof c)if("boolean"===typeof a)e._muted=a;else return e._muted;for(var d=e._getSoundIds(c),g=0;g<d.length;g++){var j=e._soundById(d[g]);j&&(j._muted=a,j._interval&&e._stopFade(j._id),e._webAudio&&j._node?j._node.gain.setValueAtTime(a?0:j._volume,b.ctx.currentTime):
j._node&&(j._node.muted=b._muted?!0:a),e._emit("mute",j._id))}return e},volume:function(){var a=this,c=arguments,e,d;if(0===c.length)return a._volume;1===c.length||2===c.length&&"undefined"===typeof c[1]?0<=a._getSoundIds().indexOf(c[0])?d=parseInt(c[0],10):e=parseFloat(c[0]):2<=c.length&&(e=parseFloat(c[0]),d=parseInt(c[1],10));var g;if("undefined"!==typeof e&&0<=e&&1>=e){if("loaded"!==a._state||a._playLock)return a._queue.push({event:"volume",action:function(){a.volume.apply(a,c)}}),a;"undefined"===
typeof d&&(a._volume=e);d=a._getSoundIds(d);for(var j=0;j<d.length;j++)if(g=a._soundById(d[j]))g._volume=e,c[2]||a._stopFade(d[j]),a._webAudio&&g._node&&!g._muted?g._node.gain.setValueAtTime(e,b.ctx.currentTime):g._node&&!g._muted&&(g._node.volume=e*b.volume()),a._emit("volume",g._id)}else return(g=d?a._soundById(d):a._sounds[0])?g._volume:0;return a},fade:function(a,c,e,d){var g=this;if("loaded"!==g._state||g._playLock)return g._queue.push({event:"fade",action:function(){g.fade(a,c,e,d)}}),g;a=Math.min(Math.max(0,
parseFloat(a)),1);c=Math.min(Math.max(0,parseFloat(c)),1);e=parseFloat(e);g.volume(a,d);for(var j=g._getSoundIds(d),p=0;p<j.length;p++){var C=g._soundById(j[p]);if(C){d||g._stopFade(j[p]);if(g._webAudio&&!C._muted){var F=b.ctx.currentTime,A=F+e/1E3;C._volume=a;C._node.gain.setValueAtTime(a,F);C._node.gain.linearRampToValueAtTime(c,A)}g._startFadeInterval(C,a,c,e,j[p],"undefined"===typeof d)}}return g},_startFadeInterval:function(a,b,c,e,d,g){var j=this,p=b,F=c-b;d=Math.abs(F/0.01);d=Math.max(4,0<
d?e/d:e);var A=Date.now();a._fadeTo=c;a._interval=setInterval(function(){var d=(Date.now()-A)/e;A=Date.now();p+=F*d;p=Math.round(100*p)/100;p=0>F?Math.max(c,p):Math.min(c,p);j._webAudio?a._volume=p:j.volume(p,a._id,!0);g&&(j._volume=p);if(c<b&&p<=c||c>b&&p>=c)clearInterval(a._interval),a._interval=null,a._fadeTo=null,j.volume(c,a._id),j._emit("fade",a._id)},d)},_stopFade:function(a){var c=this._soundById(a);c&&c._interval&&(this._webAudio&&c._node.gain.cancelScheduledValues(b.ctx.currentTime),clearInterval(c._interval),
c._interval=null,this.volume(c._fadeTo,a),c._fadeTo=null,this._emit("fade",a));return this},loop:function(){var a=arguments,b,c;if(0===a.length)return this._loop;if(1===a.length)if("boolean"===typeof a[0])this._loop=b=a[0];else return(a=this._soundById(parseInt(a[0],10)))?a._loop:!1;else 2===a.length&&(b=a[0],c=parseInt(a[1],10));c=this._getSoundIds(c);for(var e=0;e<c.length;e++)if(a=this._soundById(c[e]))if(a._loop=b,this._webAudio&&(a._node&&a._node.bufferSource)&&(a._node.bufferSource.loop=b))a._node.bufferSource.loopStart=
a._start||0,a._node.bufferSource.loopEnd=a._stop,this.playing(c[e])&&(this.pause(c[e],!0),this.play(c[e],!0));return this},rate:function(){var a=this,c=arguments,e,d;0===c.length?d=a._sounds[0]._id:1===c.length?0<=a._getSoundIds().indexOf(c[0])?d=parseInt(c[0],10):e=parseFloat(c[0]):2===c.length&&(e=parseFloat(c[0]),d=parseInt(c[1],10));var g;if("number"===typeof e){if("loaded"!==a._state||a._playLock)return a._queue.push({event:"rate",action:function(){a.rate.apply(a,c)}}),a;"undefined"===typeof d&&
(a._rate=e);d=a._getSoundIds(d);for(var j=0;j<d.length;j++)if(g=a._soundById(d[j])){a.playing(d[j])&&(g._rateSeek=a.seek(d[j]),g._playStart=a._webAudio?b.ctx.currentTime:g._playStart);g._rate=e;a._webAudio&&g._node&&g._node.bufferSource?g._node.bufferSource.playbackRate.setValueAtTime(e,b.ctx.currentTime):g._node&&(g._node.playbackRate=e);var p=a.seek(d[j]),p=1E3*((a._sprite[g._sprite][0]+a._sprite[g._sprite][1])/1E3-p)/Math.abs(g._rate);if(a._endTimers[d[j]]||!g._paused)a._clearTimer(d[j]),a._endTimers[d[j]]=
setTimeout(a._ended.bind(a,g),p);a._emit("rate",g._id)}}else return(g=a._soundById(d))?g._rate:a._rate;return a},seek:function(){var a=this,c=arguments,e,d;0===c.length?a._sounds.length&&(d=a._sounds[0]._id):1===c.length?0<=a._getSoundIds().indexOf(c[0])?d=parseInt(c[0],10):a._sounds.length&&(d=a._sounds[0]._id,e=parseFloat(c[0])):2===c.length&&(e=parseFloat(c[0]),d=parseInt(c[1],10));if("undefined"===typeof d)return 0;if("number"===typeof e&&("loaded"!==a._state||a._playLock))return a._queue.push({event:"seek",
action:function(){a.seek.apply(a,c)}}),a;var g=a._soundById(d);if(g)if("number"===typeof e&&0<=e){var j=a.playing(d);j&&a.pause(d,!0);g._seek=e;g._ended=!1;a._clearTimer(d);!a._webAudio&&(g._node&&!isNaN(g._node.duration))&&(g._node.currentTime=e);var p=function(){j&&a.play(d,!0);a._emit("seek",d)};if(j&&!a._webAudio){var C=function(){a._playLock?setTimeout(C,0):p()};setTimeout(C,0)}else p()}else return a._webAudio?(e=a.playing(d)?b.ctx.currentTime-g._playStart:0,g._seek+((g._rateSeek?g._rateSeek-
g._seek:0)+e*Math.abs(g._rate))):g._node.currentTime;return a},playing:function(a){if("number"===typeof a)return(a=this._soundById(a))?!a._paused:!1;for(a=0;a<this._sounds.length;a++)if(!this._sounds[a]._paused)return!0;return!1},duration:function(a){var b=this._duration;(a=this._soundById(a))&&(b=this._sprite[a._sprite][1]/1E3);return b},state:function(){return this._state},unload:function(){for(var a=this._sounds,c=0;c<a.length;c++)a[c]._paused||this.stop(a[c]._id),this._webAudio||(this._clearSound(a[c]._node),
a[c]._node.removeEventListener("error",a[c]._errorFn,!1),a[c]._node.removeEventListener(b._canPlayEvent,a[c]._loadFn,!1),a[c]._node.removeEventListener("ended",a[c]._endFn,!1),b._releaseHtml5Audio(a[c]._node)),delete a[c]._node,this._clearTimer(a[c]._id);c=b._howls.indexOf(this);0<=c&&b._howls.splice(c,1);a=!0;for(c=0;c<b._howls.length;c++)if(b._howls[c]._src===this._src||0<=this._src.indexOf(b._howls[c]._src)){a=!1;break}e&&a&&delete e[this._src];b.noAudio=!1;this._state="unloaded";this._sounds=
[];return null},on:function(a,b,c,e){a=this["_on"+a];"function"===typeof b&&a.push(e?{id:c,fn:b,once:e}:{id:c,fn:b});return this},off:function(a,b,c){var e=this["_on"+a],d=0;"number"===typeof b&&(c=b,b=null);if(b||c)for(d=0;d<e.length;d++){if(a=c===e[d].id,b===e[d].fn&&a||!b&&a){e.splice(d,1);break}}else if(a)this["_on"+a]=[];else{b=Object.keys(this);for(d=0;d<b.length;d++)0===b[d].indexOf("_on")&&Array.isArray(this[b[d]])&&(this[b[d]]=[])}return this},once:function(a,b,c){this.on(a,b,c,1);return this},
_emit:function(a,b,c){for(var e=this["_on"+a],d=e.length-1;0<=d;d--)if(!e[d].id||e[d].id===b||"load"===a)setTimeout(function(a){a.call(this,b,c)}.bind(this,e[d].fn),0),e[d].once&&this.off(a,e[d].fn,e[d].id);this._loadQueue(a);return this},_loadQueue:function(a){if(0<this._queue.length){var b=this._queue[0];b.event===a&&(this._queue.shift(),this._loadQueue());a||b.action()}return this},_ended:function(a){var c=a._sprite;if(!this._webAudio&&a._node&&!a._node.paused&&!a._node.ended&&a._node.currentTime<
a._stop)return setTimeout(this._ended.bind(this,a),100),this;c=!(!a._loop&&!this._sprite[c][2]);this._emit("end",a._id);!this._webAudio&&c&&this.stop(a._id,!0).play(a._id);if(this._webAudio&&c){this._emit("play",a._id);a._seek=a._start||0;a._rateSeek=0;a._playStart=b.ctx.currentTime;var e=1E3*(a._stop-a._start)/Math.abs(a._rate);this._endTimers[a._id]=setTimeout(this._ended.bind(this,a),e)}this._webAudio&&!c&&(a._paused=!0,a._ended=!0,a._seek=a._start||0,a._rateSeek=0,this._clearTimer(a._id),this._cleanBuffer(a._node),
b._autoSuspend());!this._webAudio&&!c&&this.stop(a._id,!0);return this},_clearTimer:function(a){if(this._endTimers[a]){if("function"!==typeof this._endTimers[a])clearTimeout(this._endTimers[a]);else{var b=this._soundById(a);b&&b._node&&b._node.removeEventListener("ended",this._endTimers[a],!1)}delete this._endTimers[a]}return this},_soundById:function(a){for(var b=0;b<this._sounds.length;b++)if(a===this._sounds[b]._id)return this._sounds[b];return null},_inactiveSound:function(){this._drain();for(var a=
0;a<this._sounds.length;a++)if(this._sounds[a]._ended)return this._sounds[a].reset();return new d(this)},_drain:function(){var a=this._pool,b=0,c=0;if(!(this._sounds.length<a)){for(c=0;c<this._sounds.length;c++)this._sounds[c]._ended&&b++;for(c=this._sounds.length-1;0<=c&&!(b<=a);c--)this._sounds[c]._ended&&(this._webAudio&&this._sounds[c]._node&&this._sounds[c]._node.disconnect(0),this._sounds.splice(c,1),b--)}},_getSoundIds:function(a){if("undefined"===typeof a){a=[];for(var b=0;b<this._sounds.length;b++)a.push(this._sounds[b]._id);
return a}return[a]},_refreshBuffer:function(a){a._node.bufferSource=b.ctx.createBufferSource();a._node.bufferSource.buffer=e[this._src];a._panner?a._node.bufferSource.connect(a._panner):a._node.bufferSource.connect(a._node);if(a._node.bufferSource.loop=a._loop)a._node.bufferSource.loopStart=a._start||0,a._node.bufferSource.loopEnd=a._stop||0;a._node.bufferSource.playbackRate.setValueAtTime(a._rate,b.ctx.currentTime);return this},_cleanBuffer:function(a){var c=b._navigator&&0<=b._navigator.vendor.indexOf("Apple");
if(!a.bufferSource)return this;if(b._scratchBuffer&&a.bufferSource&&(a.bufferSource.onended=null,a.bufferSource.disconnect(0),c))try{a.bufferSource.buffer=b._scratchBuffer}catch(e){}a.bufferSource=null;return this},_clearSound:function(a){/MSIE |Trident\//.test(b._navigator&&b._navigator.userAgent)||(a.src="data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA")}};var d=function(a){this._parent=a;this.init()};d.prototype={init:function(){var a=this._parent;this._muted=
a._muted;this._loop=a._loop;this._volume=a._volume;this._rate=a._rate;this._seek=0;this._ended=this._paused=!0;this._sprite="__default";this._id=++b._counter;a._sounds.push(this);this.create();return this},create:function(){var a=this._parent,c=b._muted||this._muted||this._parent._muted?0:this._volume;a._webAudio?(this._node="undefined"===typeof b.ctx.createGain?b.ctx.createGainNode():b.ctx.createGain(),this._node.gain.setValueAtTime(c,b.ctx.currentTime),this._node.paused=!0,this._node.connect(b.masterGain)):
b.noAudio||(this._node=b._obtainHtml5Audio(),this._errorFn=this._errorListener.bind(this),this._node.addEventListener("error",this._errorFn,!1),this._loadFn=this._loadListener.bind(this),this._node.addEventListener(b._canPlayEvent,this._loadFn,!1),this._endFn=this._endListener.bind(this),this._node.addEventListener("ended",this._endFn,!1),this._node.src=a._src,this._node.preload=!0===a._preload?"auto":a._preload,this._node.volume=c*b.volume(),this._node.load());return this},reset:function(){var a=
this._parent;this._muted=a._muted;this._loop=a._loop;this._volume=a._volume;this._rate=a._rate;this._rateSeek=this._seek=0;this._ended=this._paused=!0;this._sprite="__default";this._id=++b._counter;return this},_errorListener:function(){this._parent._emit("loaderror",this._id,this._node.error?this._node.error.code:0);this._node.removeEventListener("error",this._errorFn,!1)},_loadListener:function(){var a=this._parent;a._duration=Math.ceil(10*this._node.duration)/10;0===Object.keys(a._sprite).length&&
(a._sprite={__default:[0,1E3*a._duration]});"loaded"!==a._state&&(a._state="loaded",a._emit("load"),a._loadQueue());this._node.removeEventListener(b._canPlayEvent,this._loadFn,!1)},_endListener:function(){var a=this._parent;Infinity===a._duration&&(a._duration=Math.ceil(10*this._node.duration)/10,Infinity===a._sprite.__default[1]&&(a._sprite.__default[1]=1E3*a._duration),a._ended(this));this._node.removeEventListener("ended",this._endFn,!1)}};var e={},g=function(a,c){var d=function(){c._emit("loaderror",
null,"Decoding audio data failed.")},g=function(a){a&&0<c._sounds.length?(e[c._src]=a,j(c,a)):d()};"undefined"!==typeof Promise&&1===b.ctx.decodeAudioData.length?b.ctx.decodeAudioData(a).then(g).catch(d):b.ctx.decodeAudioData(a,g,d)},j=function(a,b){b&&!a._duration&&(a._duration=b.duration);0===Object.keys(a._sprite).length&&(a._sprite={__default:[0,1E3*a._duration]});"loaded"!==a._state&&(a._state="loaded",a._emit("load"),a._loadQueue())},p=function(){if(b.usingWebAudio){try{"undefined"!==typeof AudioContext?
b.ctx=new AudioContext:"undefined"!==typeof webkitAudioContext?b.ctx=new webkitAudioContext:b.usingWebAudio=!1}catch(a){b.usingWebAudio=!1}b.ctx||(b.usingWebAudio=!1);var c=/iP(hone|od|ad)/.test(b._navigator&&b._navigator.platform),e=b._navigator&&b._navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/),e=e?parseInt(e[1],10):null;c&&(e&&9>e)&&(c=/safari/.test(b._navigator&&b._navigator.userAgent.toLowerCase()),b._navigator&&!c&&(b.usingWebAudio=!1));b.usingWebAudio&&(b.masterGain="undefined"===typeof b.ctx.createGain?
b.ctx.createGainNode():b.ctx.createGain(),b.masterGain.gain.setValueAtTime(b._muted?0:b._volume,b.ctx.currentTime),b.masterGain.connect(b.ctx.destination));b._setup()}};"function"===typeof define&&define.amd&&define([],function(){return{Howler:b,Howl:c}});"undefined"!==typeof exports&&(exports.Howler=b,exports.Howl=c);"undefined"!==typeof global?(global.HowlerGlobal=a,global.Howler=b,global.Howl=c,global.Sound=d):"undefined"!==typeof window&&(window.HowlerGlobal=a,window.Howler=b,window.Howl=c,window.Sound=
d)})();
(function(){HowlerGlobal.prototype._pos=[0,0,0];HowlerGlobal.prototype._orientation=[0,0,-1,0,1,0];HowlerGlobal.prototype.stereo=function(a){if(!this.ctx||!this.ctx.listener)return this;for(var b=this._howls.length-1;0<=b;b--)this._howls[b].stereo(a);return this};HowlerGlobal.prototype.pos=function(a,b,c){if(!this.ctx||!this.ctx.listener)return this;b="number"!==typeof b?this._pos[1]:b;c="number"!==typeof c?this._pos[2]:c;if("number"===typeof a)this._pos=[a,b,c],"undefined"!==typeof this.ctx.listener.positionX?(this.ctx.listener.positionX.setTargetAtTime(this._pos[0],
Howler.ctx.currentTime,0.1),this.ctx.listener.positionY.setTargetAtTime(this._pos[1],Howler.ctx.currentTime,0.1),this.ctx.listener.positionZ.setTargetAtTime(this._pos[2],Howler.ctx.currentTime,0.1)):this.ctx.listener.setPosition(this._pos[0],this._pos[1],this._pos[2]);else return this._pos;return this};HowlerGlobal.prototype.orientation=function(a,b,c,d,f,n){if(!this.ctx||!this.ctx.listener)return this;var l=this._orientation;b="number"!==typeof b?l[1]:b;c="number"!==typeof c?l[2]:c;d="number"!==
typeof d?l[3]:d;f="number"!==typeof f?l[4]:f;n="number"!==typeof n?l[5]:n;if("number"===typeof a)this._orientation=[a,b,c,d,f,n],"undefined"!==typeof this.ctx.listener.forwardX?(this.ctx.listener.forwardX.setTargetAtTime(a,Howler.ctx.currentTime,0.1),this.ctx.listener.forwardY.setTargetAtTime(b,Howler.ctx.currentTime,0.1),this.ctx.listener.forwardZ.setTargetAtTime(c,Howler.ctx.currentTime,0.1),this.ctx.listener.upX.setTargetAtTime(d,Howler.ctx.currentTime,0.1),this.ctx.listener.upY.setTargetAtTime(f,
Howler.ctx.currentTime,0.1),this.ctx.listener.upZ.setTargetAtTime(n,Howler.ctx.currentTime,0.1)):this.ctx.listener.setOrientation(a,b,c,d,f,n);else return l;return this};var a=Howl.prototype.init;Howl.prototype.init=function(b){this._orientation=b.orientation||[1,0,0];this._stereo=b.stereo||null;this._pos=b.pos||null;this._pannerAttr={coneInnerAngle:"undefined"!==typeof b.coneInnerAngle?b.coneInnerAngle:360,coneOuterAngle:"undefined"!==typeof b.coneOuterAngle?b.coneOuterAngle:360,coneOuterGain:"undefined"!==
typeof b.coneOuterGain?b.coneOuterGain:0,distanceModel:"undefined"!==typeof b.distanceModel?b.distanceModel:"inverse",maxDistance:"undefined"!==typeof b.maxDistance?b.maxDistance:1E4,panningModel:"undefined"!==typeof b.panningModel?b.panningModel:"HRTF",refDistance:"undefined"!==typeof b.refDistance?b.refDistance:1,rolloffFactor:"undefined"!==typeof b.rolloffFactor?b.rolloffFactor:1};this._onstereo=b.onstereo?[{fn:b.onstereo}]:[];this._onpos=b.onpos?[{fn:b.onpos}]:[];this._onorientation=b.onorientation?
[{fn:b.onorientation}]:[];return a.call(this,b)};Howl.prototype.stereo=function(a,b){var c=this;if(!c._webAudio)return c;if("loaded"!==c._state)return c._queue.push({event:"stereo",action:function(){c.stereo(a,b)}}),c;var p="undefined"===typeof Howler.ctx.createStereoPanner?"spatial":"stereo";if("undefined"===typeof b)if("number"===typeof a)c._stereo=a,c._pos=[a,0,0];else return c._stereo;for(var f=c._getSoundIds(b),n=0;n<f.length;n++){var l=c._soundById(f[n]);if(l)if("number"===typeof a)l._stereo=
a,l._pos=[a,0,0],l._node&&(l._pannerAttr.panningModel="equalpower",(!l._panner||!l._panner.pan)&&d(l,p),"spatial"===p?"undefined"!==typeof l._panner.positionX?(l._panner.positionX.setValueAtTime(a,Howler.ctx.currentTime),l._panner.positionY.setValueAtTime(0,Howler.ctx.currentTime),l._panner.positionZ.setValueAtTime(0,Howler.ctx.currentTime)):l._panner.setPosition(a,0,0):l._panner.pan.setValueAtTime(a,Howler.ctx.currentTime)),c._emit("stereo",l._id);else return l._stereo}return c};Howl.prototype.pos=
function(a,b,c,p){var f=this;if(!f._webAudio)return f;if("loaded"!==f._state)return f._queue.push({event:"pos",action:function(){f.pos(a,b,c,p)}}),f;b="number"!==typeof b?0:b;c="number"!==typeof c?-0.5:c;if("undefined"===typeof p)if("number"===typeof a)f._pos=[a,b,c];else return f._pos;for(var n=f._getSoundIds(p),l=0;l<n.length;l++){var q=f._soundById(n[l]);if(q)if("number"===typeof a)q._pos=[a,b,c],q._node&&((!q._panner||q._panner.pan)&&d(q,"spatial"),"undefined"!==typeof q._panner.positionX?(q._panner.positionX.setValueAtTime(a,
Howler.ctx.currentTime),q._panner.positionY.setValueAtTime(b,Howler.ctx.currentTime),q._panner.positionZ.setValueAtTime(c,Howler.ctx.currentTime)):q._panner.setPosition(a,b,c)),f._emit("pos",q._id);else return q._pos}return f};Howl.prototype.orientation=function(a,b,c,p){var f=this;if(!f._webAudio)return f;if("loaded"!==f._state)return f._queue.push({event:"orientation",action:function(){f.orientation(a,b,c,p)}}),f;b="number"!==typeof b?f._orientation[1]:b;c="number"!==typeof c?f._orientation[2]:
c;if("undefined"===typeof p)if("number"===typeof a)f._orientation=[a,b,c];else return f._orientation;for(var n=f._getSoundIds(p),l=0;l<n.length;l++){var q=f._soundById(n[l]);if(q)if("number"===typeof a)q._orientation=[a,b,c],q._node&&(q._panner||(q._pos||(q._pos=f._pos||[0,0,-0.5]),d(q,"spatial")),"undefined"!==typeof q._panner.orientationX?(q._panner.orientationX.setValueAtTime(a,Howler.ctx.currentTime),q._panner.orientationY.setValueAtTime(b,Howler.ctx.currentTime),q._panner.orientationZ.setValueAtTime(c,
Howler.ctx.currentTime)):q._panner.setOrientation(a,b,c)),f._emit("orientation",q._id);else return q._orientation}return f};Howl.prototype.pannerAttr=function(){var a=arguments,b,c;if(!this._webAudio)return this;if(0===a.length)return this._pannerAttr;if(1===a.length)if("object"===typeof a[0])b=a[0],"undefined"===typeof c&&(b.pannerAttr||(b.pannerAttr={coneInnerAngle:b.coneInnerAngle,coneOuterAngle:b.coneOuterAngle,coneOuterGain:b.coneOuterGain,distanceModel:b.distanceModel,maxDistance:b.maxDistance,
refDistance:b.refDistance,rolloffFactor:b.rolloffFactor,panningModel:b.panningModel}),this._pannerAttr={coneInnerAngle:"undefined"!==typeof b.pannerAttr.coneInnerAngle?b.pannerAttr.coneInnerAngle:this._coneInnerAngle,coneOuterAngle:"undefined"!==typeof b.pannerAttr.coneOuterAngle?b.pannerAttr.coneOuterAngle:this._coneOuterAngle,coneOuterGain:"undefined"!==typeof b.pannerAttr.coneOuterGain?b.pannerAttr.coneOuterGain:this._coneOuterGain,distanceModel:"undefined"!==typeof b.pannerAttr.distanceModel?
b.pannerAttr.distanceModel:this._distanceModel,maxDistance:"undefined"!==typeof b.pannerAttr.maxDistance?b.pannerAttr.maxDistance:this._maxDistance,refDistance:"undefined"!==typeof b.pannerAttr.refDistance?b.pannerAttr.refDistance:this._refDistance,rolloffFactor:"undefined"!==typeof b.pannerAttr.rolloffFactor?b.pannerAttr.rolloffFactor:this._rolloffFactor,panningModel:"undefined"!==typeof b.pannerAttr.panningModel?b.pannerAttr.panningModel:this._panningModel});else return(a=this._soundById(parseInt(a[0],
10)))?a._pannerAttr:this._pannerAttr;else 2===a.length&&(b=a[0],c=parseInt(a[1],10));c=this._getSoundIds(c);for(var p=0;p<c.length;p++)if(a=this._soundById(c[p])){var f=a._pannerAttr,f={coneInnerAngle:"undefined"!==typeof b.coneInnerAngle?b.coneInnerAngle:f.coneInnerAngle,coneOuterAngle:"undefined"!==typeof b.coneOuterAngle?b.coneOuterAngle:f.coneOuterAngle,coneOuterGain:"undefined"!==typeof b.coneOuterGain?b.coneOuterGain:f.coneOuterGain,distanceModel:"undefined"!==typeof b.distanceModel?b.distanceModel:
f.distanceModel,maxDistance:"undefined"!==typeof b.maxDistance?b.maxDistance:f.maxDistance,refDistance:"undefined"!==typeof b.refDistance?b.refDistance:f.refDistance,rolloffFactor:"undefined"!==typeof b.rolloffFactor?b.rolloffFactor:f.rolloffFactor,panningModel:"undefined"!==typeof b.panningModel?b.panningModel:f.panningModel},n=a._panner;n||(a._pos||(a._pos=this._pos||[0,0,-0.5]),d(a,"spatial"),n=a._panner);n.coneInnerAngle=f.coneInnerAngle;n.coneOuterAngle=f.coneOuterAngle;n.coneOuterGain=f.coneOuterGain;
n.distanceModel=f.distanceModel;n.maxDistance=f.maxDistance;n.refDistance=f.refDistance;n.rolloffFactor=f.rolloffFactor;n.panningModel=f.panningModel}return this};var b=Sound.prototype.init;Sound.prototype.init=function(){var a=this._parent;this._orientation=a._orientation;this._stereo=a._stereo;this._pos=a._pos;this._pannerAttr=a._pannerAttr;b.call(this);this._stereo?a.stereo(this._stereo):this._pos&&a.pos(this._pos[0],this._pos[1],this._pos[2],this._id)};var c=Sound.prototype.reset;Sound.prototype.reset=
function(){var a=this._parent;this._orientation=a._orientation;this._stereo=a._stereo;this._pos=a._pos;this._pannerAttr=a._pannerAttr;this._stereo?a.stereo(this._stereo):this._pos?a.pos(this._pos[0],this._pos[1],this._pos[2],this._id):this._panner&&(this._panner.disconnect(0),this._panner=void 0,a._refreshBuffer(this));return c.call(this)};var d=function(a,b){"spatial"===(b||"spatial")?(a._panner=Howler.ctx.createPanner(),a._panner.coneInnerAngle=a._pannerAttr.coneInnerAngle,a._panner.coneOuterAngle=
a._pannerAttr.coneOuterAngle,a._panner.coneOuterGain=a._pannerAttr.coneOuterGain,a._panner.distanceModel=a._pannerAttr.distanceModel,a._panner.maxDistance=a._pannerAttr.maxDistance,a._panner.refDistance=a._pannerAttr.refDistance,a._panner.rolloffFactor=a._pannerAttr.rolloffFactor,a._panner.panningModel=a._pannerAttr.panningModel,"undefined"!==typeof a._panner.positionX?(a._panner.positionX.setValueAtTime(a._pos[0],Howler.ctx.currentTime),a._panner.positionY.setValueAtTime(a._pos[1],Howler.ctx.currentTime),
a._panner.positionZ.setValueAtTime(a._pos[2],Howler.ctx.currentTime)):a._panner.setPosition(a._pos[0],a._pos[1],a._pos[2]),"undefined"!==typeof a._panner.orientationX?(a._panner.orientationX.setValueAtTime(a._orientation[0],Howler.ctx.currentTime),a._panner.orientationY.setValueAtTime(a._orientation[1],Howler.ctx.currentTime),a._panner.orientationZ.setValueAtTime(a._orientation[2],Howler.ctx.currentTime)):a._panner.setOrientation(a._orientation[0],a._orientation[1],a._orientation[2])):(a._panner=
Howler.ctx.createStereoPanner(),a._panner.pan.setValueAtTime(a._stereo,Howler.ctx.currentTime));a._panner.connect(a._node);a._paused||a._parent.pause(a._id,!0).play(a._id,!0)}})();!function(a,b){"object"==typeof exports&&"undefined"!=typeof module?b():"function"==typeof define&&define.amd?define(b):b()}(0,function(){function a(a){var b=this.constructor;return this.then(function(c){return b.resolve(a()).then(function(){return c})},function(c){return b.resolve(a()).then(function(){return b.reject(c)})})}function b(){}function c(a){if(!(this instanceof c))throw new TypeError("Promises must be constructed via new");if("function"!=typeof a)throw new TypeError("not a function");
this._state=0;this._handled=!1;this._value=void 0;this._deferreds=[];p(a,this)}function d(a,b){for(;3===a._state;)a=a._value;0!==a._state?(a._handled=!0,c._immediateFn(function(){var c=1===a._state?b.onFulfilled:b.onRejected;if(null!==c){var d;try{d=c(a._value)}catch(f){return void g(b.promise,f)}e(b.promise,d)}else(1===a._state?e:g)(b.promise,a._value)})):a._deferreds.push(b)}function e(a,b){try{if(b===a)throw new TypeError("A promise cannot be resolved with itself.");if(b&&("object"==typeof b||
"function"==typeof b)){var e=b.then;if(b instanceof c)return a._state=3,a._value=b,void j(a);if("function"==typeof e)return void p(function(){e.apply(b,arguments)},a)}a._state=1;a._value=b;j(a)}catch(d){g(a,d)}}function g(a,b){a._state=2;a._value=b;j(a)}function j(a){2===a._state&&0===a._deferreds.length&&c._immediateFn(function(){a._handled||c._unhandledRejectionFn(a._value)});for(var b=0,e=a._deferreds.length;e>b;b++)d(a,a._deferreds[b]);a._deferreds=null}function p(a,b){var c=!1;try{a(function(a){c||
(c=!0,e(b,a))},function(a){c||(c=!0,g(b,a))})}catch(d){c||(c=!0,g(b,d))}}var f=setTimeout;c.prototype["catch"]=function(a){return this.then(null,a)};c.prototype.then=function(a,c){var e=new this.constructor(b);return d(this,new function(a,b,c){this.onFulfilled="function"==typeof a?a:null;this.onRejected="function"==typeof b?b:null;this.promise=c}(a,c,e)),e};c.prototype["finally"]=a;c.all=function(a){return new c(function(b,c){function e(a,g){try{if(g&&("object"==typeof g||"function"==typeof g)){var j=
g.then;if("function"==typeof j)return void j.call(g,function(b){e(a,b)},c)}d[a]=g;0==--f&&b(d)}catch(p){c(p)}}if(!a||"undefined"==typeof a.length)throw new TypeError("Promise.all accepts an array");var d=Array.prototype.slice.call(a);if(0===d.length)return b([]);for(var f=d.length,g=0;d.length>g;g++)e(g,d[g])})};c.resolve=function(a){return a&&"object"==typeof a&&a.constructor===c?a:new c(function(b){b(a)})};c.reject=function(a){return new c(function(b,c){c(a)})};c.race=function(a){return new c(function(b,
c){for(var e=0,d=a.length;d>e;e++)a[e].then(b,c)})};c._immediateFn="function"==typeof setImmediate&&function(a){setImmediate(a)}||function(a){f(a,0)};c._unhandledRejectionFn=function(a){void 0!==console&&console&&console.warn("Possible Unhandled Promise Rejection:",a)};var n=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw Error("unable to locate global object");}();"Promise"in n?n.Promise.prototype["finally"]||
(n.Promise.prototype["finally"]=a):n.Promise=c});(function(){function a(a,b){document.addEventListener?a.addEventListener("scroll",b,!1):a.attachEvent("scroll",b)}function b(a){this.a=document.createElement("div");this.a.setAttribute("aria-hidden","true");this.a.appendChild(document.createTextNode(a));this.b=document.createElement("span");this.c=document.createElement("span");this.h=document.createElement("span");this.f=document.createElement("span");this.g=-1;this.b.style.cssText="max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;";
this.c.style.cssText="max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;";this.f.style.cssText="max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;";this.h.style.cssText="display:inline-block;width:200%;height:200%;font-size:16px;max-width:none;";this.b.appendChild(this.h);this.c.appendChild(this.f);this.a.appendChild(this.b);this.a.appendChild(this.c)}function c(a,b){a.a.style.cssText=
"max-width:none;min-width:20px;min-height:20px;display:inline-block;overflow:hidden;position:absolute;width:auto;margin:0;padding:0;top:-999px;white-space:nowrap;font-synthesis:none;font:"+b+";"}function d(a){var b=a.a.offsetWidth,c=b+100;a.f.style.width=c+"px";a.c.scrollLeft=c;a.b.scrollLeft=a.b.scrollWidth+100;return a.g!==b?(a.g=b,!0):!1}function e(b,c){function e(){var a=f;d(a)&&a.a.parentNode&&c(a.g)}var f=b;a(b.b,e);a(b.c,e);d(b)}function g(a,b){var c=b||{};this.family=a;this.style=c.style||
"normal";this.weight=c.weight||"normal";this.stretch=c.stretch||"normal"}function j(){null===r&&(r=!!document.fonts);return r}function p(){if(null===q){var a=document.createElement("div");try{a.style.font="condensed 100px sans-serif"}catch(b){}q=""!==a.style.font}return q}function f(a,b){return[a.style,a.weight,p()?a.stretch:"","100px",b].join(" ")}var n=null,l=null,q=null,r=null;g.prototype.load=function(a,d){var g=this,p=a||"BESbswy",r=0,q=d||3E3,B=(new Date).getTime();return new Promise(function(a,
d){var s;if(s=j())null===l&&(j()&&/Apple/.test(window.navigator.vendor)?(s=/AppleWebKit\/([0-9]+)(?:\.([0-9]+))(?:\.([0-9]+))/.exec(window.navigator.userAgent),l=!!s&&603>parseInt(s[1],10)):l=!1),s=!l;if(s){s=new Promise(function(a,b){function c(){(new Date).getTime()-B>=q?b(Error(""+q+"ms timeout exceeded")):document.fonts.load(f(g,'"'+g.family+'"'),p).then(function(b){1<=b.length?a():setTimeout(c,25)},b)}c()});var t=new Promise(function(a,b){r=setTimeout(function(){b(Error(""+q+"ms timeout exceeded"))},
q)});Promise.race([t,s]).then(function(){clearTimeout(r);a(g)},d)}else{var M=function(){function j(){var b;if(b=-1!=D&&-1!=J||-1!=D&&-1!=Y||-1!=J&&-1!=Y)(b=D!=J&&D!=Y&&J!=Y)||(null===n&&(b=/AppleWebKit\/([0-9]+)(?:\.([0-9]+))/.exec(window.navigator.userAgent),n=!!b&&(536>parseInt(b[1],10)||536===parseInt(b[1],10)&&11>=parseInt(b[2],10))),b=n&&(D==ia&&J==ia&&Y==ia||D==U&&J==U&&Y==U||D==v&&J==v&&Y==v)),b=!b;b&&(u.parentNode&&u.parentNode.removeChild(u),clearTimeout(r),a(g))}function l(){if((new Date).getTime()-
B>=q)u.parentNode&&u.parentNode.removeChild(u),d(Error(""+q+"ms timeout exceeded"));else{var a=document.hidden;if(!0===a||void 0===a)D=s.a.offsetWidth,J=t.a.offsetWidth,Y=M.a.offsetWidth,j();r=setTimeout(l,50)}}var s=new b(p),t=new b(p),M=new b(p),D=-1,J=-1,Y=-1,ia=-1,U=-1,v=-1,u=document.createElement("div");u.dir="ltr";c(s,f(g,"sans-serif"));c(t,f(g,"serif"));c(M,f(g,"monospace"));u.appendChild(s.a);u.appendChild(t.a);u.appendChild(M.a);document.body.appendChild(u);ia=s.a.offsetWidth;U=t.a.offsetWidth;
v=M.a.offsetWidth;l();e(s,function(a){D=a;j()});c(s,f(g,'"'+g.family+'",sans-serif'));e(t,function(a){J=a;j()});c(t,f(g,'"'+g.family+'",serif'));e(M,function(a){Y=a;j()});c(M,f(g,'"'+g.family+'",monospace'))};document.body?M():document.addEventListener?document.addEventListener("DOMContentLoaded",function E(){document.removeEventListener("DOMContentLoaded",E);M()}):document.attachEvent("onreadystatechange",function y(){if("interactive"==document.readyState||"complete"==document.readyState)document.detachEvent("onreadystatechange",
y),M()})}})};"object"===typeof module?module.exports=g:(window.FontFaceObserver=g,window.FontFaceObserver.prototype.load=g.prototype.load)})();(function(a){Number.prototype.map=function(a,b,c,e){return c+(e-c)*((this-a)/(b-a))};Number.prototype.limit=function(a,b){return Math.min(b,Math.max(a,this))};Number.prototype.round=function(a){a=Math.pow(10,a||0);return Math.round(this*a)/a};Number.prototype.floor=function(){return Math.floor(this)};Number.prototype.ceil=function(){return Math.ceil(this)};Number.prototype.toInt=function(){return this|0};Number.prototype.toRad=function(){return this/180*Math.PI};Number.prototype.toDeg=function(){return 180*
this/Math.PI};Object.defineProperty(Array.prototype,"erase",{value:function(a){for(var b=this.length;b--;)this[b]===a&&this.splice(b,1);return this}});Object.defineProperty(Array.prototype,"random",{value:function(){return this[Math.floor(Math.random()*this.length)]}});Function.prototype.bind=Function.prototype.bind||function(a){if("function"!==typeof this)throw new TypeError("Function.prototype.bind - what is trying to be bound is not callable");var b=Array.prototype.slice.call(arguments,1),c=this,
e=function(){},d=function(){return c.apply(this instanceof e&&a?this:a,b.concat(Array.prototype.slice.call(arguments)))};e.prototype=this.prototype;d.prototype=new e;return d};a.ig={game:null,debug:null,version:"1.24",global:a,modules:{},resources:[],ready:!1,baked:!1,nocache:"",ua:{},prefix:a.ImpactPrefix||"",lib:"lib/",_current:null,_loadQueue:[],_waitForOnload:0,$:function(a){return"#"==a.charAt(0)?document.getElementById(a.substr(1)):document.getElementsByTagName(a)},$new:function(a){return document.createElement(a)},
copy:function(a){if(!a||"object"!=typeof a||a instanceof HTMLElement||a instanceof ig.Class)return a;if(a instanceof Array)for(var b=[],c=0,e=a.length;c<e;c++)b[c]=ig.copy(a[c]);else for(c in b={},a)b[c]=ig.copy(a[c]);return b},merge:function(a,b){for(var c in b){var e=b[c];if("object"!=typeof e||e instanceof HTMLElement||e instanceof ig.Class||null===e)a[c]=e;else{if(!a[c]||"object"!=typeof a[c])a[c]=e instanceof Array?[]:{};ig.merge(a[c],e)}}return a},ksort:function(a){if(!a||"object"!=typeof a)return[];
var b=[],c=[],e;for(e in a)b.push(e);b.sort();for(e=0;e<b.length;e++)c.push(a[b[e]]);return c},setVendorAttribute:function(a,b,c){var e=b.charAt(0).toUpperCase()+b.substr(1);a[b]=a["ms"+e]=a["moz"+e]=a["webkit"+e]=a["o"+e]=c},getVendorAttribute:function(a,b){var c=b.charAt(0).toUpperCase()+b.substr(1);return a[b]||a["ms"+c]||a["moz"+c]||a["webkit"+c]||a["o"+c]},normalizeVendorAttribute:function(a,b){var c=ig.getVendorAttribute(a,b);!a[b]&&c&&(a[b]=c)},getImagePixels:function(a,b,c,e,d){var g=ig.$new("canvas");
g.width=a.width;g.height=a.height;var j=g.getContext("2d");ig.System.SCALE.CRISP(g,j);var s=ig.getVendorAttribute(j,"backingStorePixelRatio")||1;ig.normalizeVendorAttribute(j,"getImageDataHD");var C=a.width/s,F=a.height/s;g.width=Math.ceil(C);g.height=Math.ceil(F);j.drawImage(a,0,0,C,F);return 1===s?j.getImageData(b,c,e,d):j.getImageDataHD(b,c,e,d)},module:function(a){if(ig._current)throw"Module '"+ig._current.name+"' defines nothing";if(ig.modules[a]&&ig.modules[a].body)throw"Module '"+a+"' is already defined";
ig._current={name:a,requires:[],loaded:!1,body:null};ig.modules[a]=ig._current;ig._loadQueue.push(ig._current);return ig},requires:function(){ig._current.requires=Array.prototype.slice.call(arguments);return ig},defines:function(a){ig._current.body=a;ig._current=null;ig._initDOMReady()},addResource:function(a){ig.resources.push(a)},setNocache:function(a){ig.nocache=a?"?"+Date.now():""},log:function(){},assert:function(){},show:function(){},mark:function(){},_loadScript:function(a,b){ig.modules[a]=
{name:a,requires:[],loaded:!1,body:null};ig._waitForOnload++;var c=ig.prefix+ig.lib+a.replace(/\./g,"/")+".js"+ig.nocache,e=ig.$new("script");e.type="text/javascript";e.src=c;e.onload=function(){ig._waitForOnload--;ig._execModules()};e.onerror=function(){throw"Failed to load module "+a+" at "+c+" required from "+b;};ig.$("head")[0].appendChild(e)},_execModules:function(){for(var a=!1,b=0;b<ig._loadQueue.length;b++){for(var c=ig._loadQueue[b],e=!0,d=0;d<c.requires.length;d++){var g=c.requires[d];ig.modules[g]?
ig.modules[g].loaded||(e=!1):(e=!1,ig._loadScript(g,c.name))}e&&c.body&&(ig._loadQueue.splice(b,1),c.loaded=!0,c.body(),a=!0,b--)}if(a)ig._execModules();else if(!ig.baked&&0==ig._waitForOnload&&0!=ig._loadQueue.length){a=[];for(b=0;b<ig._loadQueue.length;b++){e=[];g=ig._loadQueue[b].requires;for(d=0;d<g.length;d++)c=ig.modules[g[d]],(!c||!c.loaded)&&e.push(g[d]);a.push(ig._loadQueue[b].name+" (requires: "+e.join(", ")+")")}throw"Unresolved (or circular?) dependencies. Most likely there's a name/path mismatch for one of the listed modules or a previous syntax error prevents a module from loading:\n"+
a.join("\n");}},_DOMReady:function(){if(!ig.modules["dom.ready"].loaded){if(!document.body)return setTimeout(ig._DOMReady,13);ig.modules["dom.ready"].loaded=!0;ig._waitForOnload--;ig._execModules()}return 0},_boot:function(){document.location.href.match(/\?nocache/)&&ig.setNocache(!0);ig.ua.pixelRatio=a.devicePixelRatio||1;ig.ua.viewport={width:a.innerWidth,height:a.innerHeight};ig.ua.screen={width:a.screen.availWidth*ig.ua.pixelRatio,height:a.screen.availHeight*ig.ua.pixelRatio};ig.ua.iPhone=/iPhone|iPod/i.test(navigator.userAgent);
ig.ua.iPhone4=ig.ua.iPhone&&2==ig.ua.pixelRatio;ig.ua.iPad=/iPad/i.test(navigator.userAgent);ig.ua.android=/android/i.test(navigator.userAgent);ig.ua.winPhone=/Windows Phone/i.test(navigator.userAgent);ig.ua.iOS=ig.ua.iPhone||ig.ua.iPad;ig.ua.mobile=ig.ua.iOS||ig.ua.android||ig.ua.winPhone||/mobile/i.test(navigator.userAgent);ig.ua.touchDevice="ontouchstart"in a||a.navigator.msMaxTouchPoints},_initDOMReady:function(){ig.modules["dom.ready"]?ig._execModules():(ig._boot(),ig.modules["dom.ready"]={requires:[],
loaded:!1,body:null},ig._waitForOnload++,"complete"===document.readyState?ig._DOMReady():(document.addEventListener("DOMContentLoaded",ig._DOMReady,!1),a.addEventListener("load",ig._DOMReady,!1)))}};ig.normalizeVendorAttribute(a,"requestAnimationFrame");if(a.requestAnimationFrame){var b=1,c={};a.ig.setAnimation=function(e){var d=b++;c[d]=!0;var g=function(){c[d]&&(a.requestAnimationFrame(g),e())};a.requestAnimationFrame(g);return d};a.ig.clearAnimation=function(a){delete c[a]}}else a.ig.setAnimation=
function(b){return a.setInterval(b,1E3/60)},a.ig.clearAnimation=function(b){a.clearInterval(b)};var d=!1,e=/xyz/.test(function(){xyz})?/\bparent\b/:/.*/,g=0;a.ig.Class=function(){};var j=function(a){var b=this.prototype,c={},d;for(d in a)"function"==typeof a[d]&&"function"==typeof b[d]&&e.test(a[d])?(c[d]=b[d],b[d]=function(a,b){return function(){var e=this.parent;this.parent=c[a];var d=b.apply(this,arguments);this.parent=e;return d}}(d,a[d])):b[d]=a[d]};a.ig.Class.extend=function(b){function c(){if(!d){if(this.staticInstantiate){var a=
this.staticInstantiate.apply(this,arguments);if(a)return a}for(var b in this)"object"==typeof this[b]&&(this[b]=ig.copy(this[b]));this.init&&this.init.apply(this,arguments)}return this}var n=this.prototype;d=!0;var l=new this;d=!1;for(var q in b)l[q]="function"==typeof b[q]&&"function"==typeof n[q]&&e.test(b[q])?function(a,b){return function(){var c=this.parent;this.parent=n[a];var e=b.apply(this,arguments);this.parent=c;return e}}(q,b[q]):b[q];c.prototype=l;c.prototype.constructor=c;c.extend=a.ig.Class.extend;
c.inject=j;c.classId=l.classId=++g;return c};a.ImpactMixin&&ig.merge(ig,a.ImpactMixin)})(window);ig.baked=!0;
ig.module("impact.image").defines(function(){ig.Image=ig.Class.extend({data:null,width:0,height:0,loaded:!1,failed:!1,loadCallback:null,path:"",staticInstantiate:function(a){return ig.Image.cache[a]||null},init:function(a){this.path=a;this.load()},load:function(a){this.loaded?a&&a(this.path,!0):(!this.loaded&&ig.ready?(this.loadCallback=a||null,this.data=new Image,this.data.onload=this.onload.bind(this),this.data.onerror=this.onerror.bind(this),this.data.src=ig.prefix+this.path+ig.nocache):ig.addResource(this),
ig.Image.cache[this.path]=this)},reload:function(){this.loaded=!1;this.data=new Image;this.data.onload=this.onload.bind(this);this.data.src=this.path+"?"+Date.now()},onload:function(){this.width=this.data.width;this.height=this.data.height;this.loaded=!0;1!=ig.system.scale&&this.resize(ig.system.scale);this.loadCallback&&this.loadCallback(this.path,!0)},onerror:function(){this.failed=!0;this.loadCallback&&this.loadCallback(this.path,!1)},resize:function(a){var b=ig.getImagePixels(this.data,0,0,this.width,
this.height),c=this.width*a,d=this.height*a,e=ig.$new("canvas");e.width=c;e.height=d;for(var g=e.getContext("2d"),j=g.getImageData(0,0,c,d),p=0;p<d;p++)for(var f=0;f<c;f++){var n=4*(Math.floor(p/a)*this.width+Math.floor(f/a)),l=4*(p*c+f);j.data[l]=b.data[n];j.data[l+1]=b.data[n+1];j.data[l+2]=b.data[n+2];j.data[l+3]=b.data[n+3]}g.putImageData(j,0,0);this.data=e},draw:function(a,b,c,d,e,g){if(this.loaded){var j=ig.system.scale;e=(e?e:this.width)*j;g=(g?g:this.height)*j;ig.system.context.drawImage(this.data,
c?c*j:0,d?d*j:0,e,g,ig.system.getDrawPos(a),ig.system.getDrawPos(b),e,g);ig.Image.drawCount++}},drawTile:function(a,b,c,d,e,g,j){e=e?e:d;if(this.loaded&&!(d>this.width||e>this.height)){var p=ig.system.scale,f=Math.floor(d*p),n=Math.floor(e*p),l=g?-1:1,q=j?-1:1;if(g||j)ig.system.context.save(),ig.system.context.scale(l,q);ig.system.context.drawImage(this.data,Math.floor(c*d)%this.width*p,Math.floor(c*d/this.width)*e*p,f,n,ig.system.getDrawPos(a)*l-(g?f:0),ig.system.getDrawPos(b)*q-(j?n:0),f,n);(g||
j)&&ig.system.context.restore();ig.Image.drawCount++}}});ig.Image.drawCount=0;ig.Image.cache={};ig.Image.reloadCache=function(){for(var a in ig.Image.cache)ig.Image.cache[a].reload()}});ig.baked=!0;
ig.module("impact.font").requires("impact.image").defines(function(){ig.Font=ig.Image.extend({widthMap:[],indices:[],firstChar:32,alpha:1,letterSpacing:1,lineSpacing:0,onload:function(a){this._loadMetrics(this.data);this.parent(a);this.height-=2},widthForString:function(a){if(-1!==a.indexOf("\n")){a=a.split("\n");for(var b=0,c=0;c<a.length;c++)b=Math.max(b,this._widthForLine(a[c]));return b}return this._widthForLine(a)},_widthForLine:function(a){for(var b=0,c=0;c<a.length;c++)b+=this.widthMap[a.charCodeAt(c)-
this.firstChar];0<a.length&&(b+=this.letterSpacing*(a.length-1));return b},heightForString:function(a){return a.split("\n").length*(this.height+this.lineSpacing)},draw:function(a,b,c,d){"string"!=typeof a&&(a=a.toString());if(-1!==a.indexOf("\n")){a=a.split("\n");for(var e=this.height+this.lineSpacing,g=0;g<a.length;g++)this.draw(a[g],b,c+g*e,d)}else{if(d==ig.Font.ALIGN.RIGHT||d==ig.Font.ALIGN.CENTER)g=this._widthForLine(a),b-=d==ig.Font.ALIGN.CENTER?g/2:g;1!==this.alpha&&(ig.system.context.globalAlpha=
this.alpha);for(g=0;g<a.length;g++)d=a.charCodeAt(g),b+=this._drawChar(d-this.firstChar,b,c);1!==this.alpha&&(ig.system.context.globalAlpha=1);ig.Image.drawCount+=a.length}},_drawChar:function(a,b,c){if(!this.loaded||0>a||a>=this.indices.length)return 0;var d=ig.system.scale,e=this.widthMap[a]*d,g=this.height*d;ig.system.context.drawImage(this.data,this.indices[a]*d,0,e,g,ig.system.getDrawPos(b),ig.system.getDrawPos(c),e,g);return this.widthMap[a]+this.letterSpacing},_loadMetrics:function(a){this.widthMap=
[];this.indices=[];for(var b=ig.getImagePixels(a,0,a.height-1,a.width,1),c=0,d=0;d<a.width;d++){var e=4*d+3;127<b.data[e]?c++:128>b.data[e]&&c&&(this.widthMap.push(c),this.indices.push(d-c),c=0)}this.widthMap.push(c);this.indices.push(d-c)}});ig.Font.ALIGN={LEFT:0,RIGHT:1,CENTER:2}});ig.baked=!0;
ig.module("impact.sound").defines(function(){ig.SoundManager=ig.Class.extend({clips:{},volume:1,format:null,init:function(){if(!ig.Sound.enabled||!window.Audio)ig.Sound.enabled=!1;else{for(var a=new Audio,b=0;b<ig.Sound.use.length;b++){var c=ig.Sound.use[b];if(a.canPlayType(c.mime)){this.format=c;break}}this.format||(ig.Sound.enabled=!1);ig.Sound.enabled&&ig.Sound.useWebAudio&&(this.audioContext=new AudioContext,this.boundWebAudioUnlock=this.unlockWebAudio.bind(this),ig.system.canvas.addEventListener("touchstart",
this.boundWebAudioUnlock,!1),ig.system.canvas.addEventListener("mousedown",this.boundWebAudioUnlock,!1))}},unlockWebAudio:function(){ig.system.canvas.removeEventListener("touchstart",this.boundWebAudioUnlock,!1);ig.system.canvas.removeEventListener("mousedown",this.boundWebAudioUnlock,!1);var a=this.audioContext.createBuffer(1,1,22050),b=this.audioContext.createBufferSource();b.buffer=a;b.connect(this.audioContext.destination);b.start(0)},load:function(a,b,c){return b&&ig.Sound.useWebAudio?this.loadWebAudio(a,
b,c):this.loadHTML5Audio(a,b,c)},loadWebAudio:function(a,b,c){b=ig.prefix+a.replace(/[^\.]+$/,this.format.ext)+ig.nocache;if(this.clips[a])return this.clips[a];var d=new ig.Sound.WebAudioSource;this.clips[a]=d;var e=new XMLHttpRequest;e.open("GET",b,!0);e.responseType="arraybuffer";var g=this;e.onload=function(b){g.audioContext.decodeAudioData(e.response,function(e){d.buffer=e;c&&c(a,!0,b)},function(b){c&&c(a,!1,b)})};e.onerror=function(b){c&&c(a,!1,b)};e.send();return d},loadHTML5Audio:function(a,
b,c){var d=ig.prefix+a.replace(/[^\.]+$/,this.format.ext)+ig.nocache;if(this.clips[a]){if(this.clips[a]instanceof ig.Sound.WebAudioSource)return this.clips[a];if(b&&this.clips[a].length<ig.Sound.channels)for(b=this.clips[a].length;b<ig.Sound.channels;b++){var e=new Audio(d);e.load();this.clips[a].push(e)}return this.clips[a][0]}var g=new Audio(d);c&&(ig.ua.mobile?setTimeout(function(){c(a,!0,null)},0):(g.addEventListener("canplaythrough",function p(b){g.removeEventListener("canplaythrough",p,!1);
c(a,!0,b)},!1),g.addEventListener("error",function(b){c(a,!1,b)},!1)));g.preload="auto";g.load();this.clips[a]=[g];if(b)for(b=1;b<ig.Sound.channels;b++)e=new Audio(d),e.load(),this.clips[a].push(e);return g},get:function(a){if((a=this.clips[a])&&a instanceof ig.Sound.WebAudioSource)return a;for(var b=0,c;c=a[b++];)if(c.paused||c.ended)return c.ended&&(c.currentTime=0),c;a[0].pause();a[0].currentTime=0;return a[0]}});ig.Music=ig.Class.extend({tracks:[],namedTracks:{},currentTrack:null,currentIndex:0,
random:!1,_volume:1,_loop:!1,_fadeInterval:0,_fadeTimer:null,_endedCallbackBound:null,init:function(){this._endedCallbackBound=this._endedCallback.bind(this);Object.defineProperty(this,"volume",{get:this.getVolume.bind(this),set:this.setVolume.bind(this)});Object.defineProperty(this,"loop",{get:this.getLooping.bind(this),set:this.setLooping.bind(this)})},add:function(a,b){if(ig.Sound.enabled){var c=a instanceof ig.Sound?a.path:a,d=ig.soundManager.load(c,!1);if(d instanceof ig.Sound.WebAudioSource)throw ig.system.stopRunLoop(),
"Sound '"+c+"' loaded as Multichannel but used for Music. Set the multiChannel param to false when loading, e.g.: new ig.Sound(path, false)";d.loop=this._loop;d.volume=this._volume;d.addEventListener("ended",this._endedCallbackBound,!1);this.tracks.push(d);b&&(this.namedTracks[b]=d);this.currentTrack||(this.currentTrack=d)}},next:function(){this.tracks.length&&(this.stop(),this.currentIndex=this.random?Math.floor(Math.random()*this.tracks.length):(this.currentIndex+1)%this.tracks.length,this.currentTrack=
this.tracks[this.currentIndex],this.play())},pause:function(){this.currentTrack&&this.currentTrack.pause()},stop:function(){this.currentTrack&&(this.currentTrack.pause(),this.currentTrack.currentTime=0)},play:function(a){if(a&&this.namedTracks[a])a=this.namedTracks[a],a!=this.currentTrack&&(this.stop(),this.currentTrack=a);else if(!this.currentTrack)return;this.currentTrack.play()},getLooping:function(){return this._loop},setLooping:function(a){this._loop=a;for(var b in this.tracks)this.tracks[b].loop=
a},getVolume:function(){return this._volume},setVolume:function(a){this._volume=a.limit(0,1);for(var b in this.tracks)this.tracks[b].volume=this._volume},fadeOut:function(a){this.currentTrack&&(clearInterval(this._fadeInterval),this._fadeTimer=new ig.Timer(a),this._fadeInterval=setInterval(this._fadeStep.bind(this),50))},_fadeStep:function(){var a=this._fadeTimer.delta().map(-this._fadeTimer.target,0,1,0).limit(0,1)*this._volume;0.01>=a?(this.stop(),this.currentTrack.volume=this._volume,clearInterval(this._fadeInterval)):
this.currentTrack.volume=a},_endedCallback:function(){this._loop?this.play():this.next()}});ig.Sound=ig.Class.extend({path:"",volume:1,currentClip:null,multiChannel:!0,_loop:!1,init:function(a,b){this.path=a;this.multiChannel=!1!==b;Object.defineProperty(this,"loop",{get:this.getLooping.bind(this),set:this.setLooping.bind(this)});this.load()},getLooping:function(){return this._loop},setLooping:function(a){this._loop=a;this.currentClip&&(this.currentClip.loop=a)},load:function(a){ig.Sound.enabled?
ig.ready?ig.soundManager.load(this.path,this.multiChannel,a):ig.addResource(this):a&&a(this.path,!0)},play:function(){ig.Sound.enabled&&(this.currentClip=ig.soundManager.get(this.path),this.currentClip.loop=this._loop,this.currentClip.volume=ig.soundManager.volume*this.volume,this.currentClip.play())},stop:function(){this.currentClip&&(this.currentClip.pause(),this.currentClip.currentTime=0)}});ig.Sound.WebAudioSource=ig.Class.extend({sources:[],gain:null,buffer:null,_loop:!1,init:function(){this.gain=
ig.soundManager.audioContext.createGain();this.gain.connect(ig.soundManager.audioContext.destination);Object.defineProperty(this,"loop",{get:this.getLooping.bind(this),set:this.setLooping.bind(this)});Object.defineProperty(this,"volume",{get:this.getVolume.bind(this),set:this.setVolume.bind(this)})},play:function(){if(this.buffer){var a=ig.soundManager.audioContext.createBufferSource();a.buffer=this.buffer;a.connect(this.gain);a.loop=this._loop;var b=this;this.sources.push(a);a.onended=function(){b.sources.erase(a)};
a.start(0)}},pause:function(){for(var a=0;a<this.sources.length;a++)try{this.sources[a].stop()}catch(b){}},getLooping:function(){return this._loop},setLooping:function(a){this._loop=a;for(var b=0;b<this.sources.length;b++)this.sources[b].loop=a},getVolume:function(){return this.gain.gain.value},setVolume:function(a){this.gain.gain.value=a}});ig.Sound.FORMAT={MP3:{ext:"mp3",mime:"audio/mpeg"},M4A:{ext:"m4a",mime:"audio/mp4; codecs=mp4a.40.2"},OGG:{ext:"ogg",mime:"audio/ogg; codecs=vorbis"},WEBM:{ext:"webm",
mime:"audio/webm; codecs=vorbis"},CAF:{ext:"caf",mime:"audio/x-caf"}};ig.Sound.use=[ig.Sound.FORMAT.OGG,ig.Sound.FORMAT.MP3];ig.Sound.channels=4;ig.Sound.enabled=!0;ig.normalizeVendorAttribute(window,"AudioContext");ig.Sound.useWebAudio=!!window.AudioContext});ig.baked=!0;
ig.module("impact.loader").requires("impact.image","impact.font","impact.sound").defines(function(){ig.Loader=ig.Class.extend({resources:[],gameClass:null,status:0,done:!1,_unloaded:[],_drawStatus:0,_intervalId:0,_loadCallbackBound:null,init:function(a,b){this.gameClass=a;this.resources=b;this._loadCallbackBound=this._loadCallback.bind(this);for(var c=0;c<this.resources.length;c++)this._unloaded.push(this.resources[c].path)},load:function(){ig.system.clear("#000");if(this.resources.length){for(var a=
0;a<this.resources.length;a++)this.loadResource(this.resources[a]);this._intervalId=setInterval(this.draw.bind(this),16)}else this.end()},loadResource:function(a){a.load(this._loadCallbackBound)},end:function(){this.done||(this.done=!0,clearInterval(this._intervalId),ig.system.setGame(this.gameClass))},draw:function(){this._drawStatus+=(this.status-this._drawStatus)/5;var a=ig.system.scale,b=(0.6*ig.system.width).floor(),c=(0.1*ig.system.height).floor(),d=(0.5*ig.system.width-b/2).floor(),e=(0.5*
ig.system.height-c/2).floor();ig.system.context.fillStyle="#000";ig.system.context.fillRect(0,0,ig.system.width,ig.system.height);ig.system.context.fillStyle="#fff";ig.system.context.fillRect(d*a,e*a,b*a,c*a);ig.system.context.fillStyle="#000";ig.system.context.fillRect(d*a+a,e*a+a,b*a-a-a,c*a-a-a);ig.system.context.fillStyle="#fff";ig.system.context.fillRect(d*a,e*a,b*a*this._drawStatus,c*a)},_loadCallback:function(a,b){if(b)this._unloaded.erase(a);else throw"Failed to load resource: "+a;this.status=
1-this._unloaded.length/this.resources.length;0==this._unloaded.length&&setTimeout(this.end.bind(this),250)}})});ig.baked=!0;
ig.module("impact.timer").defines(function(){ig.Timer=ig.Class.extend({target:0,base:0,last:0,pausedAt:0,init:function(a){this.last=this.base=ig.Timer.time;this.target=a||0},set:function(a){this.target=a||0;this.base=ig.Timer.time;this.pausedAt=0},reset:function(){this.base=ig.Timer.time;this.pausedAt=0},tick:function(){var a=ig.Timer.time-this.last;this.last=ig.Timer.time;return this.pausedAt?0:a},delta:function(){return(this.pausedAt||ig.Timer.time)-this.base-this.target},pause:function(){this.pausedAt||
(this.pausedAt=ig.Timer.time)},unpause:function(){this.pausedAt&&(this.base+=ig.Timer.time-this.pausedAt,this.pausedAt=0)}});ig.Timer._last=0;ig.Timer.time=Number.MIN_VALUE;ig.Timer.timeScale=1;ig.Timer.maxStep=0.05;ig.Timer.step=function(){var a=Date.now();ig.Timer.time+=Math.min((a-ig.Timer._last)/1E3,ig.Timer.maxStep)*ig.Timer.timeScale;ig.Timer._last=a}});ig.baked=!0;
ig.module("impact.system").requires("impact.timer","impact.image").defines(function(){ig.System=ig.Class.extend({fps:30,width:320,height:240,realWidth:320,realHeight:240,scale:1,tick:0,animationId:0,newGameClass:null,running:!1,delegate:null,clock:null,canvas:null,context:null,init:function(a,b,c,d,e){this.fps=b;this.clock=new ig.Timer;this.canvas=ig.$(a);this.resize(c,d,e);this.context=this.canvas.getContext("2d");this.getDrawPos=ig.System.drawMode;1!=this.scale&&(ig.System.scaleMode=ig.System.SCALE.CRISP);
ig.System.scaleMode(this.canvas,this.context)},resize:function(a,b,c){this.width=a;this.height=b;this.scale=c||this.scale;this.realWidth=this.width*this.scale;this.realHeight=this.height*this.scale;this.canvas.width=this.realWidth;this.canvas.height=this.realHeight},setGame:function(a){this.running?this.newGameClass=a:this.setGameNow(a)},setGameNow:function(a){ig.game=new a;ig.system.setDelegate(ig.game)},setDelegate:function(a){if("function"==typeof a.run)this.delegate=a,this.startRunLoop();else throw"System.setDelegate: No run() function in object";
},stopRunLoop:function(){ig.clearAnimation(this.animationId);this.running=!1},startRunLoop:function(){this.stopRunLoop();this.animationId=ig.setAnimation(this.run.bind(this));this.running=!0},clear:function(a){this.context.fillStyle=a;this.context.fillRect(0,0,this.realWidth,this.realHeight)},run:function(){ig.Timer.step();this.tick=this.clock.tick();this.delegate.run();ig.input.clearPressed();this.newGameClass&&(this.setGameNow(this.newGameClass),this.newGameClass=null)},getDrawPos:null});ig.System.DRAW=
{AUTHENTIC:function(a){return Math.round(a)*this.scale},SMOOTH:function(a){return Math.round(a*this.scale)},SUBPIXEL:function(a){return a*this.scale}};ig.System.drawMode=ig.System.DRAW.SMOOTH;ig.System.SCALE={CRISP:function(a,b){ig.setVendorAttribute(b,"imageSmoothingEnabled",!1);a.style.imageRendering="-moz-crisp-edges";a.style.imageRendering="-o-crisp-edges";a.style.imageRendering="-webkit-optimize-contrast";a.style.imageRendering="crisp-edges";a.style.msInterpolationMode="nearest-neighbor"},SMOOTH:function(a,
b){ig.setVendorAttribute(b,"imageSmoothingEnabled",!0);a.style.imageRendering="";a.style.msInterpolationMode=""}};ig.System.scaleMode=ig.System.SCALE.SMOOTH});ig.baked=!0;
ig.module("impact.input").defines(function(){ig.KEY={MOUSE1:-1,MOUSE2:-3,MWHEEL_UP:-4,MWHEEL_DOWN:-5,BACKSPACE:8,TAB:9,ENTER:13,PAUSE:19,CAPS:20,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,LEFT_ARROW:37,UP_ARROW:38,RIGHT_ARROW:39,DOWN_ARROW:40,INSERT:45,DELETE:46,_0:48,_1:49,_2:50,_3:51,_4:52,_5:53,_6:54,_7:55,_8:56,_9:57,A:65,B:66,C:67,D:68,E:69,F:70,G:71,H:72,I:73,J:74,K:75,L:76,M:77,N:78,O:79,P:80,Q:81,R:82,S:83,T:84,U:85,V:86,W:87,X:88,Y:89,Z:90,NUMPAD_0:96,NUMPAD_1:97,NUMPAD_2:98,
NUMPAD_3:99,NUMPAD_4:100,NUMPAD_5:101,NUMPAD_6:102,NUMPAD_7:103,NUMPAD_8:104,NUMPAD_9:105,MULTIPLY:106,ADD:107,SUBSTRACT:109,DECIMAL:110,DIVIDE:111,F1:112,F2:113,F3:114,F4:115,F5:116,F6:117,F7:118,F8:119,F9:120,F10:121,F11:122,F12:123,SHIFT:16,CTRL:17,ALT:18,PLUS:187,COMMA:188,MINUS:189,PERIOD:190};ig.Input=ig.Class.extend({bindings:{},actions:{},presses:{},locks:{},delayedKeyup:{},isUsingMouse:!1,isUsingKeyboard:!1,isUsingAccelerometer:!1,mouse:{x:0,y:0},accel:{x:0,y:0,z:0},initMouse:function(){this.isUsingMouse||
(this.isUsingMouse=!0,ig.system.canvas.addEventListener("wheel",this.mousewheel.bind(this),!1),ig.system.canvas.addEventListener("contextmenu",this.contextmenu.bind(this),!1),ig.system.canvas.addEventListener("mousedown",this.keydown.bind(this),!1),ig.system.canvas.addEventListener("mouseup",this.keyup.bind(this),!1),ig.system.canvas.addEventListener("mousemove",this.mousemove.bind(this),!1),ig.ua.touchDevice&&(ig.system.canvas.addEventListener("touchstart",this.keydown.bind(this),!1),ig.system.canvas.addEventListener("touchend",
this.keyup.bind(this),!1),ig.system.canvas.addEventListener("touchcancel",this.keyup.bind(this),!1),ig.system.canvas.addEventListener("touchmove",this.mousemove.bind(this),!1),ig.system.canvas.addEventListener("MSPointerDown",this.keydown.bind(this),!1),ig.system.canvas.addEventListener("MSPointerUp",this.keyup.bind(this),!1),ig.system.canvas.addEventListener("MSPointerMove",this.mousemove.bind(this),!1),ig.system.canvas.style.msTouchAction="none"))},initKeyboard:function(){this.isUsingKeyboard||
(this.isUsingKeyboard=!0,window.addEventListener("keydown",this.keydown.bind(this),!1),window.addEventListener("keyup",this.keyup.bind(this),!1))},initAccelerometer:function(){this.isUsingAccelerometer||(this.isUsingAccelerometer=!0,window.addEventListener("devicemotion",this.devicemotion.bind(this),!1))},mousewheel:function(a){var b=this.bindings[0>a.deltaY?ig.KEY.MWHEEL_UP:ig.KEY.MWHEEL_DOWN];b&&(this.actions[b]=!0,this.presses[b]=!0,this.delayedKeyup[b]=!0,a.stopPropagation(),a.preventDefault())},
mousemove:function(a){var b=ig.system.scale*((ig.system.canvas.offsetWidth||ig.system.realWidth)/ig.system.realWidth),c={left:0,top:0};ig.system.canvas.getBoundingClientRect&&(c=ig.system.canvas.getBoundingClientRect());a=a.touches?a.touches[0]:a;this.mouse.x=(a.clientX-c.left)/b;this.mouse.y=(a.clientY-c.top)/b},contextmenu:function(a){this.bindings[ig.KEY.MOUSE2]&&(a.stopPropagation(),a.preventDefault())},keydown:function(a){var b=a.target.tagName;if(!("INPUT"==b||"TEXTAREA"==b))if(b="keydown"==
a.type?a.keyCode:2==a.button?ig.KEY.MOUSE2:ig.KEY.MOUSE1,0>b&&!ig.ua.mobile&&window.focus(),("touchstart"==a.type||"mousedown"==a.type)&&this.mousemove(a),b=this.bindings[b])this.actions[b]=!0,this.locks[b]||(this.presses[b]=!0,this.locks[b]=!0),a.preventDefault()},keyup:function(a){var b=a.target.tagName;if(!("INPUT"==b||"TEXTAREA"==b))if(b=this.bindings["keyup"==a.type?a.keyCode:2==a.button?ig.KEY.MOUSE2:ig.KEY.MOUSE1])this.delayedKeyup[b]=!0,a.preventDefault()},devicemotion:function(a){this.accel=
a.accelerationIncludingGravity},bind:function(a,b){0>a?this.initMouse():0<a&&this.initKeyboard();this.bindings[a]=b},bindTouch:function(a,b){var c=ig.$(a),d=this;c.addEventListener("touchstart",function(a){d.touchStart(a,b)},!1);c.addEventListener("touchend",function(a){d.touchEnd(a,b)},!1);c.addEventListener("MSPointerDown",function(a){d.touchStart(a,b)},!1);c.addEventListener("MSPointerUp",function(a){d.touchEnd(a,b)},!1)},unbind:function(a){this.delayedKeyup[this.bindings[a]]=!0;this.bindings[a]=
null},unbindAll:function(){this.bindings={};this.actions={};this.presses={};this.locks={};this.delayedKeyup={}},state:function(a){return this.actions[a]},pressed:function(a){return this.presses[a]},released:function(a){return!!this.delayedKeyup[a]},clearPressed:function(){for(var a in this.delayedKeyup)this.actions[a]=!1,this.locks[a]=!1;this.delayedKeyup={};this.presses={}},touchStart:function(a,b){this.actions[b]=!0;this.presses[b]=!0;a.stopPropagation();a.preventDefault();return!1},touchEnd:function(a,
b){this.delayedKeyup[b]=!0;a.stopPropagation();a.preventDefault();return!1}})});ig.baked=!0;ig.module("impact.impact").requires("dom.ready","impact.loader","impact.system","impact.input","impact.sound").defines(function(){ig.main=function(a,b,c,d,e,g,j){ig.system=new ig.System(a,c,d,e,g||1);ig.input=new ig.Input;ig.soundManager=new ig.SoundManager;ig.music=new ig.Music;ig.ready=!0;(new (j||ig.Loader)(b,ig.resources)).load()}});ig.baked=!0;
ig.module("impact.animation").requires("impact.timer","impact.image").defines(function(){ig.AnimationSheet=ig.Class.extend({width:8,height:8,image:null,init:function(a,b,c){this.width=b;this.height=c;this.image=new ig.Image(a)}});ig.Animation=ig.Class.extend({sheet:null,timer:null,sequence:[],flip:{x:!1,y:!1},pivot:{x:0,y:0},frameTime:0,frame:0,tile:0,stop:!1,loopCount:0,alpha:1,angle:0,init:function(a,b,c,d){this.sheet=a;this.pivot={x:a.width/2,y:a.height/2};this.timer=new ig.Timer;this.frameTime=
b;this.sequence=c;this.stop=!!d;this.tile=this.sequence[0]},rewind:function(){this.timer.set();this.frame=this.loopCount=0;this.tile=this.sequence[0];return this},gotoFrame:function(a){this.timer.set(this.frameTime*-a-1E-4);this.update()},gotoRandomFrame:function(){this.gotoFrame(Math.floor(Math.random()*this.sequence.length))},update:function(){var a=Math.floor(this.timer.delta()/this.frameTime);this.loopCount=Math.floor(a/this.sequence.length);this.frame=this.stop&&0<this.loopCount?this.sequence.length-
1:a%this.sequence.length;this.tile=this.sequence[this.frame]},draw:function(a,b){var c=Math.max(this.sheet.width,this.sheet.height);a>ig.system.width+150||(b>ig.system.height+150||0>a+c+150||0>b+c+150)||(1!=this.alpha&&(ig.system.context.globalAlpha=this.alpha),0==this.angle?this.sheet.image.drawTile(a,b,this.tile,this.sheet.width,this.sheet.height,this.flip.x,this.flip.y):(ig.system.context.save(),ig.system.context.translate(ig.system.getDrawPos(a+this.pivot.x),ig.system.getDrawPos(b+this.pivot.y)),
ig.system.context.rotate(this.angle),this.sheet.image.drawTile(-this.pivot.x,-this.pivot.y,this.tile,this.sheet.width,this.sheet.height,this.flip.x,this.flip.y),ig.system.context.restore()),1!=this.alpha&&(ig.system.context.globalAlpha=1))}})});ig.baked=!0;
ig.module("impact.entity").requires("impact.animation","impact.impact").defines(function(){ig.Entity=ig.Class.extend({id:0,settings:{},size:{x:16,y:16},offset:{x:0,y:0},pos:{x:0,y:0},last:{x:0,y:0},vel:{x:0,y:0},accel:{x:0,y:0},friction:{x:0,y:0},maxVel:{x:100,y:100},zIndex:0,gravityFactor:1,standing:!1,bounciness:0,minBounceVelocity:40,anims:{},animSheet:null,currentAnim:null,health:10,type:0,checkAgainst:0,collides:0,_killed:!1,slopeStanding:{min:(44).toRad(),max:(136).toRad()},init:function(a,
b,c){this.id=++ig.Entity._lastId;this.pos.x=this.last.x=a;this.pos.y=this.last.y=b;ig.merge(this,c)},reset:function(a,b,c){var d=this.constructor.prototype;this.pos.x=a;this.pos.y=b;this.last.x=a;this.last.y=b;this.vel.x=d.vel.x;this.vel.y=d.vel.y;this.accel.x=d.accel.x;this.accel.y=d.accel.y;this.health=d.health;this._killed=d._killed;this.standing=d.standing;this.type=d.type;this.checkAgainst=d.checkAgainst;this.collides=d.collides;ig.merge(this,c)},addAnim:function(a,b,c,d){if(!this.animSheet)throw"No animSheet to add the animation "+
a+" to.";b=new ig.Animation(this.animSheet,b,c,d);this.anims[a]=b;this.currentAnim||(this.currentAnim=b);return b},update:function(){this.last.x=this.pos.x;this.last.y=this.pos.y;this.vel.y+=ig.game.gravity*ig.system.tick*this.gravityFactor;this.vel.x=this.getNewVelocity(this.vel.x,this.accel.x,this.friction.x,this.maxVel.x);this.vel.y=this.getNewVelocity(this.vel.y,this.accel.y,this.friction.y,this.maxVel.y);var a=ig.game.collisionMap.trace(this.pos.x,this.pos.y,this.vel.x*ig.system.tick,this.vel.y*
ig.system.tick,this.size.x,this.size.y);this.handleMovementTrace(a);this.currentAnim&&this.currentAnim.update()},getNewVelocity:function(a,b,c,d){return b?(a+b*ig.system.tick).limit(-d,d):c?(b=c*ig.system.tick,0<a-b?a-b:0>a+b?a+b:0):a.limit(-d,d)},handleMovementTrace:function(a){this.standing=!1;a.collision.y&&(0<this.bounciness&&Math.abs(this.vel.y)>this.minBounceVelocity?this.vel.y*=-this.bounciness:(0<this.vel.y&&(this.standing=!0),this.vel.y=0));a.collision.x&&(this.vel.x=0<this.bounciness&&Math.abs(this.vel.x)>
this.minBounceVelocity?this.vel.x*-this.bounciness:0);if(a.collision.slope){var b=a.collision.slope;if(0<this.bounciness){var c=this.vel.x*b.nx+this.vel.y*b.ny;this.vel.x=(this.vel.x-2*b.nx*c)*this.bounciness;this.vel.y=(this.vel.y-2*b.ny*c)*this.bounciness}else c=(this.vel.x*b.x+this.vel.y*b.y)/(b.x*b.x+b.y*b.y),this.vel.x=b.x*c,this.vel.y=b.y*c,b=Math.atan2(b.x,b.y),b>this.slopeStanding.min&&b<this.slopeStanding.max&&(this.standing=!0)}this.pos=a.pos},draw:function(){this.currentAnim&&this.currentAnim.draw(this.pos.x-
this.offset.x-ig.game._rscreen.x,this.pos.y-this.offset.y-ig.game._rscreen.y)},kill:function(){ig.game.removeEntity(this)},receiveDamage:function(a){this.health-=a;0>=this.health&&this.kill()},touches:function(a){return!(this.pos.x>=a.pos.x+a.size.x||this.pos.x+this.size.x<=a.pos.x||this.pos.y>=a.pos.y+a.size.y||this.pos.y+this.size.y<=a.pos.y)},distanceTo:function(a){var b=this.pos.x+this.size.x/2-(a.pos.x+a.size.x/2);a=this.pos.y+this.size.y/2-(a.pos.y+a.size.y/2);return Math.sqrt(b*b+a*a)},angleTo:function(a){return Math.atan2(a.pos.y+
a.size.y/2-(this.pos.y+this.size.y/2),a.pos.x+a.size.x/2-(this.pos.x+this.size.x/2))},check:function(){},collideWith:function(){},ready:function(){},erase:function(){}});ig.Entity._lastId=0;ig.Entity.COLLIDES={NEVER:0,LITE:1,PASSIVE:2,ACTIVE:4,FIXED:8};ig.Entity.TYPE={NONE:0,A:1,B:2,BOTH:3};ig.Entity.checkPair=function(a,b){a.checkAgainst&b.type&&a.check(b);b.checkAgainst&a.type&&b.check(a);a.collides&&(b.collides&&a.collides+b.collides>ig.Entity.COLLIDES.ACTIVE)&&ig.Entity.solveCollision(a,b)};ig.Entity.solveCollision=
function(a,b){var c=null;if(a.collides==ig.Entity.COLLIDES.LITE||b.collides==ig.Entity.COLLIDES.FIXED)c=a;else if(b.collides==ig.Entity.COLLIDES.LITE||a.collides==ig.Entity.COLLIDES.FIXED)c=b;a.last.x+a.size.x>b.last.x&&a.last.x<b.last.x+b.size.x?(a.last.y<b.last.y?ig.Entity.seperateOnYAxis(a,b,c):ig.Entity.seperateOnYAxis(b,a,c),a.collideWith(b,"y"),b.collideWith(a,"y")):a.last.y+a.size.y>b.last.y&&a.last.y<b.last.y+b.size.y&&(a.last.x<b.last.x?ig.Entity.seperateOnXAxis(a,b,c):ig.Entity.seperateOnXAxis(b,
a,c),a.collideWith(b,"x"),b.collideWith(a,"x"))};ig.Entity.seperateOnXAxis=function(a,b,c){var d=a.pos.x+a.size.x-b.pos.x;c?(c.vel.x=-c.vel.x*c.bounciness+(a===c?b:a).vel.x,b=ig.game.collisionMap.trace(c.pos.x,c.pos.y,c==a?-d:d,0,c.size.x,c.size.y),c.pos.x=b.pos.x):(c=(a.vel.x-b.vel.x)/2,a.vel.x=-c,b.vel.x=c,c=ig.game.collisionMap.trace(a.pos.x,a.pos.y,-d/2,0,a.size.x,a.size.y),a.pos.x=Math.floor(c.pos.x),a=ig.game.collisionMap.trace(b.pos.x,b.pos.y,d/2,0,b.size.x,b.size.y),b.pos.x=Math.ceil(a.pos.x))};
ig.Entity.seperateOnYAxis=function(a,b,c){var d=a.pos.y+a.size.y-b.pos.y;if(c){b=a===c?b:a;c.vel.y=-c.vel.y*c.bounciness+b.vel.y;var e=0;c==a&&Math.abs(c.vel.y-b.vel.y)<c.minBounceVelocity&&(c.standing=!0,e=b.vel.x*ig.system.tick);a=ig.game.collisionMap.trace(c.pos.x,c.pos.y,e,c==a?-d:d,c.size.x,c.size.y);c.pos.y=a.pos.y;c.pos.x=a.pos.x}else ig.game.gravity&&(b.standing||0<a.vel.y)?(c=ig.game.collisionMap.trace(a.pos.x,a.pos.y,0,-(a.pos.y+a.size.y-b.pos.y),a.size.x,a.size.y),a.pos.y=c.pos.y,0<a.bounciness&&
a.vel.y>a.minBounceVelocity?a.vel.y*=-a.bounciness:(a.standing=!0,a.vel.y=0)):(c=(a.vel.y-b.vel.y)/2,a.vel.y=-c,b.vel.y=c,e=b.vel.x*ig.system.tick,c=ig.game.collisionMap.trace(a.pos.x,a.pos.y,e,-d/2,a.size.x,a.size.y),a.pos.y=c.pos.y,a=ig.game.collisionMap.trace(b.pos.x,b.pos.y,0,d/2,b.size.x,b.size.y),b.pos.y=a.pos.y)}});ig.baked=!0;
ig.module("impact.map").defines(function(){ig.Map=ig.Class.extend({tilesize:8,width:1,height:1,pxWidth:1,pxHeight:1,data:[[]],name:null,init:function(a,b){this.tilesize=a;this.data=b;this.height=b.length;this.width=b[0].length;this.pxWidth=this.width*this.tilesize;this.pxHeight=this.height*this.tilesize},getTile:function(a,b){var c=Math.floor(a/this.tilesize),d=Math.floor(b/this.tilesize);return 0<=c&&c<this.width&&0<=d&&d<this.height?this.data[d][c]:0},setTile:function(a,b,c){a=Math.floor(a/this.tilesize);
b=Math.floor(b/this.tilesize);0<=a&&a<this.width&&(0<=b&&b<this.height)&&(this.data[b][a]=c)}})});ig.baked=!0;
ig.module("impact.collision-map").requires("impact.map").defines(function(){ig.CollisionMap=ig.Map.extend({lastSlope:1,tiledef:null,init:function(a,b,e){this.parent(a,b);this.tiledef=e||ig.CollisionMap.defaultTileDef;for(var g in this.tiledef)g|0>this.lastSlope&&(this.lastSlope=g|0)},trace:function(a,b,e,g,j,p){var f={collision:{x:!1,y:!1,slope:!1},pos:{x:a,y:b},tile:{x:0,y:0}},n=Math.ceil((Math.max(Math.abs(e),Math.abs(g))+0.1)/this.tilesize);if(1<n)for(var l=e/n,q=g/n,r=0;r<n&&(l||q)&&!(this._traceStep(f,
a,b,l,q,j,p,e,g,r),a=f.pos.x,b=f.pos.y,f.collision.x&&(e=l=0),f.collision.y&&(g=q=0),f.collision.slope);r++);else this._traceStep(f,a,b,e,g,j,p,e,g,0);return f},_traceStep:function(a,b,e,g,j,p,f,n,l,q){a.pos.x+=g;a.pos.y+=j;var r=0;if(g){var t=0<g?p:0,s=0>g?this.tilesize:0,r=Math.max(Math.floor(e/this.tilesize),0),C=Math.min(Math.ceil((e+f)/this.tilesize),this.height);g=Math.floor((a.pos.x+t)/this.tilesize);var F=Math.floor((b+t)/this.tilesize);if(0<q||g==F||0>F||F>=this.width)F=-1;if(0<=g&&g<this.width)for(var A=
r;A<C&&!(-1!=F&&(r=this.data[A][F],1<r&&r<=this.lastSlope&&this._checkTileDef(a,r,b,e,n,l,p,f,F,A)));A++)if(r=this.data[A][g],1==r||r>this.lastSlope||1<r&&this._checkTileDef(a,r,b,e,n,l,p,f,g,A)){if(1<r&&r<=this.lastSlope&&a.collision.slope)break;a.collision.x=!0;a.tile.x=r;b=a.pos.x=g*this.tilesize-t+s;n=0;break}}if(j){t=0<j?f:0;j=0>j?this.tilesize:0;r=Math.max(Math.floor(a.pos.x/this.tilesize),0);s=Math.min(Math.ceil((a.pos.x+p)/this.tilesize),this.width);A=Math.floor((a.pos.y+t)/this.tilesize);
C=Math.floor((e+t)/this.tilesize);if(0<q||A==C||0>C||C>=this.height)C=-1;if(0<=A&&A<this.height)for(g=r;g<s&&!(-1!=C&&(r=this.data[C][g],1<r&&r<=this.lastSlope&&this._checkTileDef(a,r,b,e,n,l,p,f,g,C)));g++)if(r=this.data[A][g],1==r||r>this.lastSlope||1<r&&this._checkTileDef(a,r,b,e,n,l,p,f,g,A)){if(1<r&&r<=this.lastSlope&&a.collision.slope)break;a.collision.y=!0;a.tile.y=r;a.pos.y=A*this.tilesize-t+j;break}}},_checkTileDef:function(a,b,e,g,j,p,f,n,l,q){var r=this.tiledef[b];if(!r)return!1;b=(r[2]-
r[0])*this.tilesize;var t=(r[3]-r[1])*this.tilesize,s=r[4];f=e+j+(0>t?f:0)-(l+r[0])*this.tilesize;n=g+p+(0<b?n:0)-(q+r[1])*this.tilesize;if(0<b*n-t*f){if(0>j*-t+p*b)return s;l=Math.sqrt(b*b+t*t);q=t/l;l=-b/l;var C=f*q+n*l,r=q*C,C=l*C;if(r*r+C*C>=j*j+p*p)return s||0.5>b*(n-p)-t*(f-j);a.pos.x=e+j-r;a.pos.y=g+p-C;a.collision.slope={x:b,y:t,nx:q,ny:l};return!0}return!1}});var a=1/3,b=2/3;ig.CollisionMap.defaultTileDef={5:[0,1,1,b,!0],6:[0,b,1,a,!0],7:[0,a,1,0,!0],3:[0,1,1,0.5,!0],4:[0,0.5,1,0,!0],2:[0,
1,1,0,!0],10:[0.5,1,1,0,!0],21:[0,1,0.5,0,!0],32:[b,1,1,0,!0],43:[a,1,b,0,!0],54:[0,1,a,0,!0],27:[0,0,1,a,!0],28:[0,a,1,b,!0],29:[0,b,1,1,!0],25:[0,0,1,0.5,!0],26:[0,0.5,1,1,!0],24:[0,0,1,1,!0],11:[0,0,0.5,1,!0],22:[0.5,0,1,1,!0],33:[0,0,a,1,!0],44:[a,0,b,1,!0],55:[b,0,1,1,!0],16:[1,a,0,0,!0],17:[1,b,0,a,!0],18:[1,1,0,b,!0],14:[1,0.5,0,0,!0],15:[1,1,0,0.5,!0],13:[1,1,0,0,!0],8:[0.5,1,0,0,!0],19:[1,1,0.5,0,!0],30:[a,1,0,0,!0],41:[b,1,a,0,!0],52:[1,1,b,0,!0],38:[1,b,0,1,!0],39:[1,a,0,b,!0],40:[1,0,
0,a,!0],36:[1,0.5,0,1,!0],37:[1,0,0,0.5,!0],35:[1,0,0,1,!0],9:[1,0,0.5,1,!0],20:[0.5,0,0,1,!0],31:[1,0,b,1,!0],42:[b,0,a,1,!0],53:[a,0,0,1,!0],12:[0,0,1,0,!1],23:[1,1,0,1,!1],34:[1,0,1,1,!1],45:[0,1,0,0,!1]};ig.CollisionMap.staticNoCollision={trace:function(a,b,e,g){return{collision:{x:!1,y:!1,slope:!1},pos:{x:a+e,y:b+g},tile:{x:0,y:0}}}}});ig.baked=!0;
ig.module("impact.background-map").requires("impact.map","impact.image").defines(function(){ig.BackgroundMap=ig.Map.extend({tiles:null,scroll:{x:0,y:0},distance:1,repeat:!1,tilesetName:"",foreground:!1,enabled:!0,preRender:!1,preRenderedChunks:null,chunkSize:512,debugChunks:!1,anims:{},init:function(a,b,c){this.parent(a,b);this.setTileset(c)},setTileset:function(a){this.tilesetName=a instanceof ig.Image?a.path:a;this.tiles=new ig.Image(this.tilesetName);this.preRenderedChunks=null},setScreenPos:function(a,
b){this.scroll.x=a/this.distance;this.scroll.y=b/this.distance},preRenderMapToChunks:function(){var a=this.width*this.tilesize*ig.system.scale,b=this.height*this.tilesize*ig.system.scale;this.chunkSize=Math.min(Math.max(a,b),this.chunkSize);var c=Math.ceil(a/this.chunkSize),d=Math.ceil(b/this.chunkSize);this.preRenderedChunks=[];for(var e=0;e<d;e++){this.preRenderedChunks[e]=[];for(var g=0;g<c;g++)this.preRenderedChunks[e][g]=this.preRenderChunk(g,e,g==c-1?a-g*this.chunkSize:this.chunkSize,e==d-1?
b-e*this.chunkSize:this.chunkSize)}},preRenderChunk:function(a,b,c,d){var e=c/this.tilesize/ig.system.scale+1,g=d/this.tilesize/ig.system.scale+1,j=a*this.chunkSize/ig.system.scale%this.tilesize,p=b*this.chunkSize/ig.system.scale%this.tilesize;a=Math.floor(a*this.chunkSize/this.tilesize/ig.system.scale);var f=Math.floor(b*this.chunkSize/this.tilesize/ig.system.scale);b=ig.$new("canvas");b.width=c;b.height=d;b.retinaResolutionEnabled=!1;d=b.getContext("2d");ig.System.scaleMode(b,d);c=ig.system.context;
ig.system.context=d;for(d=0;d<e;d++)for(var n=0;n<g;n++)if(d+a<this.width&&n+f<this.height){var l=this.data[n+f][d+a];l&&this.tiles.drawTile(d*this.tilesize-j,n*this.tilesize-p,l-1,this.tilesize)}ig.system.context=c;e=new Image;e.src=b.toDataURL();e.width=b.width;e.height=b.height;return e},draw:function(){this.tiles.loaded&&this.enabled&&(this.preRender?this.drawPreRendered():this.drawTiled())},drawPreRendered:function(){this.preRenderedChunks||this.preRenderMapToChunks();var a=ig.system.getDrawPos(this.scroll.x),
b=ig.system.getDrawPos(this.scroll.y);if(this.repeat)var c=this.width*this.tilesize*ig.system.scale,a=(a%c+c)%c,c=this.height*this.tilesize*ig.system.scale,b=(b%c+c)%c;var c=Math.max(Math.floor(a/this.chunkSize),0),d=Math.max(Math.floor(b/this.chunkSize),0),e=Math.ceil((a+ig.system.realWidth)/this.chunkSize),g=Math.ceil((b+ig.system.realHeight)/this.chunkSize),j=this.preRenderedChunks[0].length,p=this.preRenderedChunks.length;this.repeat||(e=Math.min(e,j),g=Math.min(g,p));for(var f=0,n=d;n<g;n++){for(var l=
0,q=c;q<e;q++){var r=this.preRenderedChunks[n%p][q%j],t=-a+q*this.chunkSize-l,s=-b+n*this.chunkSize-f;ig.system.context.drawImage(r,t,s);ig.Image.drawCount++;this.debugChunks&&(ig.system.context.strokeStyle="#f0f",ig.system.context.strokeRect(t,s,this.chunkSize,this.chunkSize));this.repeat&&(r.width<this.chunkSize&&t+r.width<ig.system.realWidth)&&(l+=this.chunkSize-r.width,n==d&&e++)}this.repeat&&(r.height<this.chunkSize&&s+r.height<ig.system.realHeight)&&(f+=this.chunkSize-r.height,g++)}},drawTiled:function(){for(var a=
0,b=null,c=(this.scroll.x/this.tilesize).toInt(),d=(this.scroll.y/this.tilesize).toInt(),e=this.scroll.x%this.tilesize,g=this.scroll.y%this.tilesize,j=-e-this.tilesize,e=ig.system.width+this.tilesize-e,p=ig.system.height+this.tilesize-g,f=-1,g=-g-this.tilesize;g<p;f++,g+=this.tilesize){var n=f+d;if(n>=this.height||0>n){if(!this.repeat)continue;n=(n%this.height+this.height)%this.height}for(var l=-1,q=j;q<e;l++,q+=this.tilesize){a=l+c;if(a>=this.width||0>a){if(!this.repeat)continue;a=(a%this.width+
this.width)%this.width}if(a=this.data[n][a])(b=this.anims[a-1])?b.draw(q,g):this.tiles.drawTile(q,g,a-1,this.tilesize)}}}})});ig.baked=!0;
ig.module("impact.game").requires("impact.impact","impact.entity","impact.collision-map","impact.background-map").defines(function(){ig.Game=ig.Class.extend({clearColor:"#000000",gravity:0,screen:{x:0,y:0},_rscreen:{x:0,y:0},entities:[],namedEntities:{},collisionMap:ig.CollisionMap.staticNoCollision,backgroundMaps:[],backgroundAnims:{},autoSort:!1,sortBy:null,cellSize:64,_deferredKill:[],_levelToLoad:null,_doSortEntities:!1,staticInstantiate:function(){this.sortBy=this.sortBy||ig.Game.SORT.Z_INDEX;
ig.game=this;return null},loadLevel:function(a){this.screen={x:0,y:0};this.entities=[];this.namedEntities={};for(var b=0;b<a.entities.length;b++){var c=a.entities[b];this.spawnEntity(c.type,c.x,c.y,c.settings)}this.sortEntities();this.collisionMap=ig.CollisionMap.staticNoCollision;this.backgroundMaps=[];for(b=0;b<a.layer.length;b++)if(c=a.layer[b],"collision"==c.name)this.collisionMap=new ig.CollisionMap(c.tilesize,c.data);else{var d=new ig.BackgroundMap(c.tilesize,c.data,c.tilesetName);d.anims=this.backgroundAnims[c.tilesetName]||
{};d.repeat=c.repeat;d.distance=c.distance;d.foreground=!!c.foreground;d.preRender=!!c.preRender;d.name=c.name;this.backgroundMaps.push(d)}for(b=0;b<this.entities.length;b++)this.entities[b].ready()},loadLevelDeferred:function(a){this._levelToLoad=a},getMapByName:function(a){if("collision"==a)return this.collisionMap;for(var b=0;b<this.backgroundMaps.length;b++)if(this.backgroundMaps[b].name==a)return this.backgroundMaps[b];return null},getEntityByName:function(a){return this.namedEntities[a]},getEntitiesByType:function(a){a=
"string"===typeof a?ig.global[a]:a;for(var b=[],c=0;c<this.entities.length;c++){var d=this.entities[c];d instanceof a&&!d._killed&&b.push(d)}return b},spawnEntity:function(a,b,c,d){var e="string"===typeof a?ig.global[a]:a;if(!e)throw"Can't spawn entity of type "+a;a=new e(b,c,d||{});this.entities.push(a);a.name&&(this.namedEntities[a.name]=a);return a},sortEntities:function(){this.entities.sort(this.sortBy)},sortEntitiesDeferred:function(){this._doSortEntities=!0},removeEntity:function(a){a.name&&
delete this.namedEntities[a.name];a._killed=!0;a.type=ig.Entity.TYPE.NONE;a.checkAgainst=ig.Entity.TYPE.NONE;a.collides=ig.Entity.COLLIDES.NEVER;this._deferredKill.push(a)},run:function(){this.update();this.draw()},update:function(){this._levelToLoad&&(this.loadLevel(this._levelToLoad),this._levelToLoad=null);this.updateEntities();this.checkEntities();for(var a=0;a<this._deferredKill.length;a++)this._deferredKill[a].erase(),this.entities.erase(this._deferredKill[a]);this._deferredKill=[];if(this._doSortEntities||
this.autoSort)this.sortEntities(),this._doSortEntities=!1;for(var b in this.backgroundAnims){var a=this.backgroundAnims[b],c;for(c in a)a[c].update()}},updateEntities:function(){for(var a=0;a<this.entities.length;a++){var b=this.entities[a];b._killed||b.update()}},draw:function(){this.clearColor&&ig.system.clear(this.clearColor);this._rscreen.x=ig.system.getDrawPos(this.screen.x)/ig.system.scale;this._rscreen.y=ig.system.getDrawPos(this.screen.y)/ig.system.scale;var a;for(a=0;a<this.backgroundMaps.length;a++){var b=
this.backgroundMaps[a];if(b.foreground)break;b.setScreenPos(this.screen.x,this.screen.y);b.draw()}this.drawEntities();for(a;a<this.backgroundMaps.length;a++)b=this.backgroundMaps[a],b.setScreenPos(this.screen.x,this.screen.y),b.draw()},drawEntities:function(){for(var a=0;a<this.entities.length;a++)this.entities[a].draw()},checkEntities:function(){for(var a={},b=0;b<this.entities.length;b++){var c=this.entities[b];if(!(c.type==ig.Entity.TYPE.NONE&&c.checkAgainst==ig.Entity.TYPE.NONE&&c.collides==ig.Entity.COLLIDES.NEVER))for(var d=
{},e=Math.floor(c.pos.y/this.cellSize),g=Math.floor((c.pos.x+c.size.x)/this.cellSize)+1,j=Math.floor((c.pos.y+c.size.y)/this.cellSize)+1,p=Math.floor(c.pos.x/this.cellSize);p<g;p++)for(var f=e;f<j;f++)if(a[p])if(a[p][f]){for(var n=a[p][f],l=0;l<n.length;l++)c.touches(n[l])&&!d[n[l].id]&&(d[n[l].id]=!0,ig.Entity.checkPair(c,n[l]));n.push(c)}else a[p][f]=[c];else a[p]={},a[p][f]=[c]}}});ig.Game.SORT={Z_INDEX:function(a,b){return a.zIndex-b.zIndex},POS_X:function(a,b){return a.pos.x+a.size.x-(b.pos.x+
b.size.x)},POS_Y:function(a,b){return a.pos.y+a.size.y-(b.pos.y+b.size.y)}}});ig.baked=!0;
ig.module("impact.debug.menu").requires("dom.ready","impact.system").defines(function(){ig.System.inject({run:function(){ig.debug.beforeRun();this.parent();ig.debug.afterRun()},setGameNow:function(a){this.parent(a);ig.debug.ready()}});ig.Debug=ig.Class.extend({options:{},panels:{},numbers:{},container:null,panelMenu:null,numberContainer:null,activePanel:null,debugTime:0,debugTickAvg:0.016,debugRealTime:Date.now(),init:function(){var a=ig.$new("link");a.rel="stylesheet";a.type="text/css";a.href=ig.prefix+
"lib/impact/debug/debug.css";ig.$("body")[0].appendChild(a);this.container=ig.$new("div");this.container.className="ig_debug";ig.$("body")[0].appendChild(this.container);this.panelMenu=ig.$new("div");this.panelMenu.innerHTML='<div class="ig_debug_head">Impact.Debug:</div>';this.panelMenu.className="ig_debug_panel_menu";this.container.appendChild(this.panelMenu);this.numberContainer=ig.$new("div");this.numberContainer.className="ig_debug_stats";this.panelMenu.appendChild(this.numberContainer);window.console&&
(window.console.log&&window.console.assert)&&(ig.log=console.log.bind?console.log.bind(console):console.log,ig.assert=console.assert.bind?console.assert.bind(console):console.assert);ig.show=this.showNumber.bind(this)},addNumber:function(a){var b=ig.$new("span");this.numberContainer.appendChild(b);this.numberContainer.appendChild(document.createTextNode(a));this.numbers[a]=b},showNumber:function(a,b){this.numbers[a]||this.addNumber(a);this.numbers[a].textContent=b},addPanel:function(a){var b=new a.type(a.name,
a.label);if(a.options)for(var c=0;c<a.options.length;c++){var d=a.options[c];b.addOption(new ig.DebugOption(d.name,d.object,d.property))}this.panels[b.name]=b;b.container.style.display="none";this.container.appendChild(b.container);a=ig.$new("div");a.className="ig_debug_menu_item";a.textContent=b.label;a.addEventListener("click",function(){this.togglePanel(b)}.bind(this),!1);b.menuItem=a;d=!1;for(c=1;c<this.panelMenu.childNodes.length;c++){var e=this.panelMenu.childNodes[c];if(e.textContent>b.label){this.panelMenu.insertBefore(a,
e);d=!0;break}}d||this.panelMenu.appendChild(a)},showPanel:function(a){this.togglePanel(this.panels[a])},togglePanel:function(a){a!=this.activePanel&&this.activePanel&&(this.activePanel.toggle(!1),this.activePanel.menuItem.className="ig_debug_menu_item",this.activePanel=null);var b="block"!=a.container.style.display;a.toggle(b);a.menuItem.className="ig_debug_menu_item"+(b?" active":"");b&&(this.activePanel=a)},ready:function(){for(var a in this.panels)this.panels[a].ready()},beforeRun:function(){var a=
Date.now();this.debugTickAvg=0.8*this.debugTickAvg+0.2*(a-this.debugRealTime);this.debugRealTime=a;this.activePanel&&this.activePanel.beforeRun()},afterRun:function(){var a=Date.now()-this.debugRealTime;this.debugTime=0.8*this.debugTime+0.2*a;this.activePanel&&this.activePanel.afterRun();this.showNumber("ms",this.debugTime.toFixed(2));this.showNumber("fps",Math.round(1E3/this.debugTickAvg));this.showNumber("draws",ig.Image.drawCount);ig.game&&ig.game.entities&&this.showNumber("entities",ig.game.entities.length);
ig.Image.drawCount=0}});ig.DebugPanel=ig.Class.extend({active:!1,container:null,options:[],panels:[],label:"",name:"",init:function(a,b){this.name=a;this.label=b;this.container=ig.$new("div");this.container.className="ig_debug_panel "+this.name},toggle:function(a){this.active=a;this.container.style.display=a?"block":"none"},addPanel:function(a){this.panels.push(a);this.container.appendChild(a.container)},addOption:function(a){this.options.push(a);this.container.appendChild(a.container)},ready:function(){},
beforeRun:function(){},afterRun:function(){}});ig.DebugOption=ig.Class.extend({name:"",labelName:"",className:"ig_debug_option",label:null,mark:null,container:null,active:!1,colors:{enabled:"#fff",disabled:"#444"},init:function(a,b,c){this.name=a;this.object=b;this.property=c;this.active=this.object[this.property];this.container=ig.$new("div");this.container.className="ig_debug_option";this.label=ig.$new("span");this.label.className="ig_debug_label";this.label.textContent=this.name;this.mark=ig.$new("span");
this.mark.className="ig_debug_label_mark";this.container.appendChild(this.mark);this.container.appendChild(this.label);this.container.addEventListener("click",this.click.bind(this),!1);this.setLabel()},setLabel:function(){this.mark.style.backgroundColor=this.active?this.colors.enabled:this.colors.disabled},click:function(a){this.active=!this.active;this.object[this.property]=this.active;this.setLabel();a.stopPropagation();a.preventDefault();return!1}});ig.debug=new ig.Debug});ig.baked=!0;
ig.module("impact.debug.entities-panel").requires("impact.debug.menu","impact.entity").defines(function(){ig.Entity.inject({colors:{names:"#fff",velocities:"#0f0",boxes:"#f00"},draw:function(){this.parent();ig.Entity._debugShowBoxes&&(ig.system.context.strokeStyle=this.colors.boxes,ig.system.context.lineWidth=1,ig.system.context.strokeRect(ig.system.getDrawPos(this.pos.x.round()-ig.game.screen.x)-0.5,ig.system.getDrawPos(this.pos.y.round()-ig.game.screen.y)-0.5,this.size.x*ig.system.scale,this.size.y*
ig.system.scale));if(ig.Entity._debugShowVelocities){var a=this.pos.x+this.size.x/2,b=this.pos.y+this.size.y/2;this._debugDrawLine(this.colors.velocities,a,b,a+this.vel.x,b+this.vel.y)}if(ig.Entity._debugShowNames&&(this.name&&(ig.system.context.fillStyle=this.colors.names,ig.system.context.fillText(this.name,ig.system.getDrawPos(this.pos.x-ig.game.screen.x),ig.system.getDrawPos(this.pos.y-ig.game.screen.y))),"object"==typeof this.target))for(var c in this.target)(a=ig.game.getEntityByName(this.target[c]))&&
this._debugDrawLine(this.colors.names,this.pos.x+this.size.x/2,this.pos.y+this.size.y/2,a.pos.x+a.size.x/2,a.pos.y+a.size.y/2)},_debugDrawLine:function(a,b,c,d,e){ig.system.context.strokeStyle=a;ig.system.context.lineWidth=1;ig.system.context.beginPath();ig.system.context.moveTo(ig.system.getDrawPos(b-ig.game.screen.x),ig.system.getDrawPos(c-ig.game.screen.y));ig.system.context.lineTo(ig.system.getDrawPos(d-ig.game.screen.x),ig.system.getDrawPos(e-ig.game.screen.y));ig.system.context.stroke();ig.system.context.closePath()}});
ig.Entity._debugEnableChecks=!0;ig.Entity._debugShowBoxes=!1;ig.Entity._debugShowVelocities=!1;ig.Entity._debugShowNames=!1;ig.Entity.oldCheckPair=ig.Entity.checkPair;ig.Entity.checkPair=function(a,b){ig.Entity._debugEnableChecks&&ig.Entity.oldCheckPair(a,b)};ig.debug.addPanel({type:ig.DebugPanel,name:"entities",label:"Entities",options:[{name:"Checks & Collisions",object:ig.Entity,property:"_debugEnableChecks"},{name:"Show Collision Boxes",object:ig.Entity,property:"_debugShowBoxes"},{name:"Show Velocities",
object:ig.Entity,property:"_debugShowVelocities"},{name:"Show Names & Targets",object:ig.Entity,property:"_debugShowNames"}]})});ig.baked=!0;
ig.module("impact.debug.maps-panel").requires("impact.debug.menu","impact.game","impact.background-map").defines(function(){ig.Game.inject({loadLevel:function(a){this.parent(a);ig.debug.panels.maps.load(this)}});ig.DebugMapsPanel=ig.DebugPanel.extend({maps:[],mapScreens:[],init:function(a,b){this.parent(a,b);this.load()},load:function(a){this.options=[];this.panels=[];if(!a||!a.backgroundMaps.length)this.container.innerHTML="<em>No Maps Loaded</em>";else{this.maps=a.backgroundMaps;this.mapScreens=
[];this.container.innerHTML="";for(a=0;a<this.maps.length;a++){var b=this.maps[a],c=new ig.DebugPanel(a,"Layer "+a),d=ig.$new("strong");d.textContent=a+": "+b.tiles.path;c.container.appendChild(d);c.addOption(new ig.DebugOption("Enabled",b,"enabled"));c.addOption(new ig.DebugOption("Pre Rendered",b,"preRender"));c.addOption(new ig.DebugOption("Show Chunks",b,"debugChunks"));this.generateMiniMap(c,b,a);this.addPanel(c)}}},generateMiniMap:function(a,b,c){var d=ig.system.scale,e=ig.$new("canvas"),g=
e.getContext("2d"),j=b.tiles.width*d,p=b.tiles.height*d,f=j/b.tilesize,n=p/b.tilesize;e.width=f;e.height=n;g.drawImage(b.tiles.data,0,0,j,p,0,0,f,n);g=ig.$new("canvas");g.width=b.width*d;g.height=b.height*d;n=g.getContext("2d");ig.game.clearColor&&(n.fillStyle=ig.game.clearColor,n.fillRect(0,0,j,p));for(p=j=0;p<b.width;p++)for(var l=0;l<b.height;l++)(j=b.data[l][p])&&n.drawImage(e,Math.floor((j-1)*d%f),Math.floor((j-1)*d/f)*d,d,d,p*d,l*d,d,d);e=ig.$new("div");e.className="ig_debug_map_container";
e.style.width=b.width*d+"px";e.style.height=b.height*d+"px";f=ig.$new("div");f.className="ig_debug_map_screen";f.style.width=ig.system.width/b.tilesize*d-2+"px";f.style.height=ig.system.height/b.tilesize*d-2+"px";this.mapScreens[c]=f;e.appendChild(g);e.appendChild(f);a.container.appendChild(e)},afterRun:function(){for(var a=ig.system.scale,b=0;b<this.maps.length;b++){var c=this.maps[b],d=this.mapScreens[b];if(c&&d){var e=c.scroll.x/c.tilesize,g=c.scroll.y/c.tilesize;c.repeat&&(e%=c.width,g%=c.height);
d.style.left=e*a+"px";d.style.top=g*a+"px"}}}});ig.debug.addPanel({type:ig.DebugMapsPanel,name:"maps",label:"Background Maps"})});ig.baked=!0;
ig.module("impact.debug.graph-panel").requires("impact.debug.menu","impact.system","impact.game","impact.image").defines(function(){ig.Game.inject({draw:function(){ig.graph.beginClock("draw");this.parent();ig.graph.endClock("draw")},update:function(){ig.graph.beginClock("update");this.parent();ig.graph.endClock("update")},checkEntities:function(){ig.graph.beginClock("checks");this.parent();ig.graph.endClock("checks")}});ig.DebugGraphPanel=ig.DebugPanel.extend({clocks:{},marks:[],textY:0,height:128,
ms:64,timeBeforeRun:0,init:function(a,b){this.parent(a,b);this.mark16ms=(this.height-16*(this.height/this.ms)).round();this.mark33ms=(this.height-33*(this.height/this.ms)).round();this.msHeight=this.height/this.ms;this.graph=ig.$new("canvas");this.graph.width=window.innerWidth;this.graph.height=this.height;this.container.appendChild(this.graph);this.ctx=this.graph.getContext("2d");this.ctx.fillStyle="#444";this.ctx.fillRect(0,this.mark16ms,this.graph.width,1);this.ctx.fillRect(0,this.mark33ms,this.graph.width,
1);this.addGraphMark("16ms",this.mark16ms);this.addGraphMark("33ms",this.mark33ms);this.addClock("draw","Draw","#13baff");this.addClock("update","Entity Update","#bb0fff");this.addClock("checks","Entity Checks & Collisions","#a2e908");this.addClock("lag","System Lag","#f26900");ig.mark=this.mark.bind(this);ig.graph=this},addGraphMark:function(a,b){var c=ig.$new("span");c.className="ig_debug_graph_mark";c.textContent=a;c.style.top=b.round()+"px";this.container.appendChild(c)},addClock:function(a,b,
c){var d=ig.$new("span");d.className="ig_debug_legend_color";d.style.backgroundColor=c;var e=ig.$new("span");e.className="ig_debug_legend_number";e.appendChild(document.createTextNode("0"));var g=ig.$new("span");g.className="ig_debug_legend";g.appendChild(d);g.appendChild(document.createTextNode(b+" ("));g.appendChild(e);g.appendChild(document.createTextNode("ms)"));this.container.appendChild(g);this.clocks[a]={description:b,color:c,current:0,start:Date.now(),avg:0,html:e}},beginClock:function(a,
b){this.clocks[a].start=Date.now()+(b||0)},endClock:function(a){a=this.clocks[a];a.current=Math.round(Date.now()-a.start);a.avg=0.8*a.avg+0.2*a.current},mark:function(a,b){this.active&&this.marks.push({msg:a,color:b||"#fff"})},beforeRun:function(){this.endClock("lag");this.timeBeforeRun=Date.now()},afterRun:function(){var a=Date.now()-this.timeBeforeRun;this.beginClock("lag",Math.max(1E3/ig.system.fps-a,0));var a=this.graph.width-1,b=this.height;this.ctx.drawImage(this.graph,-1,0);this.ctx.fillStyle=
"#000";this.ctx.fillRect(a,0,1,this.height);this.ctx.fillStyle="#444";this.ctx.fillRect(a,this.mark16ms,1,1);this.ctx.fillStyle="#444";this.ctx.fillRect(a,this.mark33ms,1,1);for(var c in this.clocks){var d=this.clocks[c];d.html.textContent=d.avg.toFixed(2);if(d.color&&0<d.current){this.ctx.fillStyle=d.color;var e=d.current*this.msHeight,b=b-e;this.ctx.fillRect(a,b,1,e);d.current=0}}this.ctx.textAlign="right";this.ctx.textBaseline="top";this.ctx.globalAlpha=0.5;for(c=0;c<this.marks.length;c++)b=this.marks[c],
this.ctx.fillStyle=b.color,this.ctx.fillRect(a,0,1,this.height),b.msg&&(this.ctx.fillText(b.msg,a-1,this.textY),this.textY=(this.textY+8)%32);this.ctx.globalAlpha=1;this.marks=[]}});ig.debug.addPanel({type:ig.DebugGraphPanel,name:"graph",label:"Performance"})});ig.baked=!0;ig.module("impact.debug.debug").requires("impact.debug.entities-panel","impact.debug.maps-panel","impact.debug.graph-panel").defines(function(){});ig.baked=!0;
ig.module("plugins.patches.fps-limit-patch").requires("impact.system","impact.impact").defines(function(){ig.System.inject({fps:60});ig.system&&(ig.system.fps=60);ig.normalizeVendorAttribute(window,"requestAnimationFrame");if(window.requestAnimationFrame){var a=1,b={};window.ig.setAnimation=function(c,d){var e=a++;b[e]=!0;var g=1E3/60,j=60,p=0,f=0,n=0,l=0,q=function(){b[e]&&(timestamp=Date.now(),f=p,p=timestamp,l=p-f,j=0.8*j+0.2*(1E3/l),60<j&&63<=j?(n=Math.min(Math.max(2*g+f-timestamp,0),g),setTimeout(function(){window.requestAnimationFrame(q,
d)},n),c(timestamp)):(window.requestAnimationFrame(q,d),c()))};window.requestAnimationFrame(q,d);return e};window.ig.clearAnimation=function(a){delete b[a]}}else window.ig.setAnimation=function(a){return window.setInterval(a,1E3/60)},window.ig.clearAnimation=function(a){window.clearInterval(a)}});ig.baked=!0;
ig.module("plugins.patches.timer-patch").requires("impact.timer").defines(function(){ig.Timer.step=function(){var a=Date.now(),b=(a-ig.Timer._last)/1E3;0>b&&(b=0);ig.Timer.time+=Math.min(b,ig.Timer.maxStep)*ig.Timer.timeScale;ig.Timer._last=a}});ig.baked=!0;
ig.module("plugins.patches.user-agent-patch").requires("impact.impact").defines(function(){ig.ua.is_uiwebview=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(navigator.userAgent);ig.ua.is_safari_or_uiwebview=/(iPhone|iPod|iPad).*AppleWebKit/i.test(navigator.userAgent);ig.ua.iOS6_tag=/OS 6_/i.test(navigator.userAgent);ig.ua.iOS6=(ig.ua.iPhone||ig.ua.iPad)&&ig.ua.iOS6_tag;ig.ua.iOSgt5=ig.ua.iOS&&5<parseInt(navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/)[1]);ig.ua.HTCONE=/HTC_One/i.test(navigator.userAgent);
ig.ua.Kindle=/Silk/i.test(navigator.userAgent);ig.ua.touchDevice="ontouchstart"in window||window.navigator.msMaxTouchPoints||window.navigator.maxTouchPoints;ig.ua.is_mac="MacIntel"===navigator.platform;ig.ua.iOS=ig.ua.touchDevice&&ig.ua.is_mac||ig.ua.iOS;ig.ua.mobile=ig.ua.iOS||ig.ua.android||ig.ua.iOS6||ig.ua.winPhone||ig.ua.Kindle||/mobile/i.test(navigator.userAgent)});ig.baked=!0;
ig.module("plugins.patches.webkit-image-smoothing-patch").defines(function(){ig.System&&(ig.System.SCALE={CRISP:function(a,b){b.imageSmoothingEnabled=b.msImageSmoothingEnabled=b.mozImageSmoothingEnabled=b.oImageSmoothingEnabled=!1;a.style.imageRendering="-moz-crisp-edges";a.style.imageRendering="-o-crisp-edges";a.style.imageRendering="-webkit-optimize-contrast";a.style.imageRendering="crisp-edges";a.style.msInterpolationMode="nearest-neighbor"},SMOOTH:function(a,b){b.imageSmoothingEnabled=b.msImageSmoothingEnabled=
b.oImageSmoothingEnabled=!0;a.style.imageRendering="";a.style.msInterpolationMode=""}},ig.System.scaleMode=ig.System.SCALE.SMOOTH)});ig.baked=!0;
ig.module("plugins.patches.windowfocus-onMouseDown-patch").requires("impact.input").defines(function(){var a=!1;try{a=window.self!==window.top,!1===a&&(a=0<window.frames.length)}catch(b){a=!0}ig.Input.inject({keydown:function(b){var d=b.target.tagName;if(!("INPUT"==d||"TEXTAREA"==d))if(d="keydown"==b.type?b.keyCode:2==b.button?ig.KEY.MOUSE2:ig.KEY.MOUSE1,a&&0>d&&window.focus(),("touchstart"==b.type||"mousedown"==b.type)&&this.mousemove(b),d=this.bindings[d])this.actions[d]=!0,this.locks[d]||(this.presses[d]=
!0,this.locks[d]=!0),b.stopPropagation(),b.preventDefault()}})});ig.baked=!0;
ig.module("plugins.patches.input-patch").requires("impact.input").defines(function(){ig.Input.inject({initMouse:function(){this.parent();ig.system.canvas.addEventListener("mouseleave",this.mouseleave.bind(this),!1)},mousemove:function(a){this.parent(a);try{ig.soundHandler.unlockWebAudio()}catch(b){}},mouseleave:function(){this.clearState("click")},keyup:function(a){this.parent(a);if(ig.visibilityHandler)ig.visibilityHandler.onChange("focus");try{ig.soundHandler.unlockWebAudio()}catch(b){}},clearState:function(a){this.delayedKeyup[a]=
!0;this.actions[a]=!1},clearAllState:function(){for(var a in this.actions)this.clearState(a)}})});ig.baked=!0;
ig.module("plugins.data.vector").defines(function(){Vector2=function(a,b){this.x=a||0;this.y=b||0};Vector2.prototype={valType:"number",neg:function(){this.x=-this.x;this.y=-this.y;return this},row:function(a){typeof a===this.valType&&(this.y=a);return this.y},col:function(a){typeof a===this.valType&&(this.x=a);return this.x},add:function(a){a instanceof Vector2?(this.x+=a.x,this.y+=a.y):(this.x+=a,this.y+=a);return this},sub:function(a){a instanceof Vector2?(this.x-=a.x,this.y-=a.y):(this.x-=a,this.y-=
a);return this},mul:function(a){a instanceof Vector2?(this.x*=a.x,this.y*=a.y):(this.x*=a,this.y*=a);return this},div:function(a){a instanceof Vector2?(0!=a.x&&(this.x/=a.x),0!=a.y&&(this.y/=a.y)):0!=a&&(this.x/=a,this.y/=a);return this},equals:function(a){return this.x==a.x&&this.y==a.y},dot:function(a){return this.x*a.x+this.y*a.y},cross:function(a){return this.x*a.y-this.y*a.x},length:function(){return Math.sqrt(this.dot(this))},norm:function(){return this.div(this.length())},min:function(){return Math.min(this.x,
this.y)},max:function(){return Math.max(this.x,this.y)},toAngles:function(){return-Math.atan2(-this.y,this.x)},angleTo:function(a){return Math.acos(this.dot(a)/(this.length()*a.length()))},toArray:function(a){return[this.x,this.y].slice(0,a||2)},clone:function(){return new Vector2(this.x,this.y)},set:function(a,b){this.x=a;this.y=b;return this},unit:function(){var a=this.length();if(0<a)return new Vector2(this.x/a,this.y/a);throw"Divide by 0 error in unitVector function of vector:"+this;},turnRight:function(){var a=
this.x;this.x=-this.y;this.y=a;return this},turnLeft:function(){var a=this.x;this.x=this.y;this.y=-a;return this},rotate:function(a){var b=this.clone();this.x=b.x*Math.cos(a)-b.y*Math.sin(a);this.y=b.x*Math.sin(a)+b.y*Math.cos(a);return this}};Vector2.negative=function(a){return new Vector2(-a.x,-a.y)};Vector2.add=function(a,b){return b instanceof Vector2?new Vector2(a.x+b.x,a.y+b.y):new Vector2(a.x+b,a.y+b)};Vector2.subtract=function(a,b){return b instanceof Vector2?new Vector2(a.x-b.x,a.y-b.y):
new Vector2(a.x-b,a.y-b)};Vector2.multiply=function(a,b){return b instanceof Vector2?new Vector2(a.x*b.x,a.y*b.y):new Vector2(a.x*b,a.y*b)};Vector2.divide=function(a,b){return b instanceof Vector2?new Vector2(a.x/b.x,a.y/b.y):new Vector2(a.x/b,a.y/b)};Vector2.equals=function(a,b){return a.x==b.x&&a.y==b.y};Vector2.dot=function(a,b){return a.x*b.x+a.y*b.y};Vector2.cross=function(a,b){return a.x*b.y-a.y*b.x}});ig.baked=!0;
ig.module("plugins.data.color-rgb").defines(function(){ColorRGB=function(a,b,c,d){this.r=a||0;this.g=b||0;this.b=c||0;this.a=d||0};ColorRGB.prototype={setRandomColor:function(){this.r=Math.round(255*Math.random());this.g=Math.round(255*Math.random());this.b=Math.round(255*Math.random())},getStyle:function(){return"rgba("+this.r+","+this.g+","+this.b+","+this.a+")"},getHex:function(){for(var a=this.r.toString(16),b=this.g.toString(16),c=this.b.toString(16);2>a.length;)a="0"+a;for(;2>b.length;)b="0"+
b;for(;2>c.length;)c="0"+c;return"#"+a+b+c},getInvertedColor:function(){return new ColorRGB(255-this.r,255-this.g,255-this.b,255-this.a)},clone:function(){return new ColorRGB(this.r,this.g,this.b,this.a)}}});ig.baked=!0;
ig.module("plugins.font.font-info").requires("impact.impact").defines(function(){ig.FontInfo=ig.Class.extend({fonts:[{name:"montserrat",source:"media/fonts/montserrat"},{name:"bebasneue-bold",source:"media/fonts/bebasneue-bold"}],init:function(){this.registerCssFont()},registerCssFont:function(){if(0<this.fonts.length){var a=document.createElement("style");a.type="text/css";for(var b="",c=0;c<this.fonts.length;c++)var d=this.fonts[c],b=b+("@font-face {font-family: '"+d.name+"';src: url('"+d.source+
".eot');src: url('"+d.source+".eot?#iefix') format('embedded-opentype'),url('"+d.source+".woff2') format('woff2'),url('"+d.source+".woff') format('woff'),url('"+d.source+".ttf') format('truetype'),url('"+d.source+".svg#svgFontName') format('svg')}");a.appendChild(document.createTextNode(b));document.head.appendChild(a)}},isValid:function(){for(var a=0;a<this.fonts.length;a++)if(!this._isValidName(this.fonts[a].source))return!1;return!0},_isValidName:function(a){return-1==a.search(/^[a-z0-9-\/]+$/)?
!1:!0}})});ig.baked=!0;
ig.module("plugins.font.font-loader").requires("impact.impact","plugins.font.font-info","impact.loader").defines(function(){ig.FontLoader=ig.Class.extend({fontInfo:new ig.FontInfo,onload:!1,onerror:!1,init:function(a,b){this.onload=a;this.onerror=b},load:function(){this.fontInfo.isValid()?this._loadByLib():console.error("Only lowercased alphanumeric and dash are allowed for font file name!. Please check the font path")},_loadByLib:function(){for(var a=[],b=0;b<this.fontInfo.fonts.length;b++){var c=new FontFaceObserver(this.fontInfo.fonts[b].name);
a.push(c.load())}Promise.all(a).then(this.onload).catch(this.onerror)}});ig.Loader.inject({parentLoad:!1,load:function(){this.parentLoad=this.parent;(new ig.FontLoader(this.onFontLoad.bind(this),this.onFontError.bind(this))).load()},onFontLoad:function(){this.parentLoad()},onFontError:function(){console.error("Font is not loaded");this.parentLoad()}})});ig.baked=!0;
ig.module("plugins.handlers.dom-handler").defines(function(){ig.DomHandler=ig.Class.extend({version:"1.1.2",JQUERYAVAILABLE:!1,init:function(){this.JQUERYAVAILABLE=this._jqueryAvailable()},_jqueryAvailable:function(){return"undefined"!==typeof jQuery},addEvent:function(a,b,c,d){if(this.JQUERYAVAILABLE)a.on(b,c);else a.addEventListener(b,c,d)},create:function(a){return this.JQUERYAVAILABLE?$("<"+a+">"):ig.$new(a)},getElementByClass:function(a){return this.JQUERYAVAILABLE?$("."+a):document.getElementsByClassName(a)},
getElementById:function(a){return this.JQUERYAVAILABLE?0<$(a).length?$(a):null:ig.$(a)},appendChild:function(a,b){this.JQUERYAVAILABLE?a.append(b):a.appendChild(b)},appendToBody:function(a){this.JQUERYAVAILABLE?$("body").append(a):document.body.appendChild(a)},appendToHead:function(a){this.JQUERYAVAILABLE?$("head").append(a):document.head.appendChild(a)},removeChild:function(a,b){this.JQUERYAVAILABLE?a.remove(b):a.removeChild(b)},removeFromBody:function(a){this.JQUERYAVAILABLE?$(a).remove():document.body.removeChild(a)},
removeFromHead:function(a){this.JQUERYAVAILABLE?$(a).remove():document.head.removeChild(a)},text:function(a,b){this.JQUERYAVAILABLE?a.text(b):a.innerText=b},val:function(a,b){this.JQUERYAVAILABLE?a.val(b):a.value=b},getVal:function(a){return this.JQUERYAVAILABLE?a.val():a.value},getAttr:function(a,b){return this.JQUERYAVAILABLE?a.attr(b):a.getAttribute(b)},getText:function(a){return this.JQUERYAVAILABLE?a.text():a.innerText},html:function(a,b){this.JQUERYAVAILABLE?a.html(b):a.innerHTML=b},resize:function(a,
b,c){if(this.JQUERYAVAILABLE)a.width(b.toFixed(2)),a.height(c.toFixed(2));else{var d=a.style.visibility;b="width:"+b.toFixed(2)+"px; height:"+c.toFixed(2)+"px;";this.attr(a,"style",b);a.style.visibility=d}},resizeOffsetLeft:function(a,b,c,d){if(this.JQUERYAVAILABLE)a.width(b.toFixed(2)),a.height(c.toFixed(2)),a.css("left",d);else{var e=a.style.visibility;b="width:"+b.toFixed(2)+"px; height:"+c.toFixed(2)+"px; left: "+d.toFixed(2)+"px;";this.attr(a,"style",b);a.style.visibility=e}},resizeOffset:function(a,
b,c,d,e){if(this.JQUERYAVAILABLE)a.width(b.toFixed(2)),a.height(c.toFixed(2)),a.css("left",d),a.css("top",e);else{var g=a.style.visibility;b="width:"+b.toFixed(2)+"px; height:"+c.toFixed(2)+"px; left: "+d.toFixed(2)+"px; top: "+e.toFixed(2)+"px;";this.attr(a,"style",b);a.style.visibility=g}},css:function(a,b){if(this.JQUERYAVAILABLE)a.css(b);else{var c="",d;for(d in b)c+=d+":"+b[d]+";";this.attr(a,"style",c)}},getOffsets:function(a){return this.JQUERYAVAILABLE?(a=a.offset(),{left:a.left,top:a.top}):
{left:a.offsetLeft,top:a.offsetTop}},attr:function(a,b,c){if("undefined"===typeof c)return this.JQUERYAVAILABLE?a.attr(b):a.getAttribute(b);this.JQUERYAVAILABLE?a.attr(b,c):a.setAttribute(b,c)},show:function(a){a&&"undefined"!==typeof a&&(this.JQUERYAVAILABLE?(a.show(),a.css("visibility","visible")):a&&(a.style?a.style.visibility="visible":a[0]&&(a[0].style.visibility="visible")))},hide:function(a){a&&"undefined"!==typeof a&&(this.JQUERYAVAILABLE?(a.hide(),a.css("visibility","hidden")):a&&(a.style?
a.style.visibility="hidden":a[0]&&(a[0].style.visibility="hidden")))},getQueryVariable:function(a){for(var b=window.location.search.substring(1).split("&"),c=0;c<b.length;c++){var d=b[c].match(/([^=]+?)=(.+)/);if(d&&decodeURIComponent(d[1])==a)return decodeURIComponent(d[2])}},forcedDeviceDetection:function(){var a=this.getQueryVariable("device");if(a)switch(a){case "mobile":console.log("serving mobile version ...");ig.ua.mobile=!0;break;case "desktop":console.log("serving desktop version ...");ig.ua.mobile=
!1;break;default:console.log("serving universal version ...")}else console.log("serving universal version ...")},forcedDeviceRotation:function(){var a=this.getQueryVariable("force-rotate");if(a)switch(a){case "portrait":console.log("force rotate to portrait");window.orientation=0;break;case "landscape":console.log("force rotate to horizontal");window.orientation=90;break;default:alert("wrong command/type in param force-rotate. Defaulting value to portrait"),window.orientation=0}},setZIndex:function(a,
b){this.JQUERYAVAILABLE?a.css("zIndex",b):a&&(a.style?a.style.zIndex=b:a[0]&&(a[0].style.zIndex=b))}})});ig.baked=!0;
ig.module("plugins.audio.howler-player").defines(function(){HowlerPlayer=ig.Class.extend({VERSION:"1.0.2",tagName:"HowlerPlayer",isMuted:!1,soundList:{},init:function(a){for(var b in a){var c=a[b];this.soundList[b]=new Howl({src:["media/audio/"+c.path+".mp3"],loop:!!c.loop,onend:function(){this.seekId=0}})}},play:function(a){if(!this.isMuted&&(a="string"===typeof a?this.soundList[a]:a,!a._loop||!a.playing()))a.playId=a.play()},stop:function(a){a="string"===typeof a?this.soundList[a]:a;a.playing()&&
(a.stop(),a.seekId=0)},pause:function(a){a="string"===typeof a?this.soundList[a]:a;a.pause(a.playId);a.seekId=a.seek(a.playId)},resume:function(a){a="string"===typeof a?this.soundList[a]:a;a.play(a.playId);a.seek(a.seekId,a.playId)},mute:function(a){a||(this.isMuted=!0);for(var b in this.soundList)this.soundList[b].playing()&&this.pause(b)},unmute:function(a){a||(this.isMuted=!1);if(!this.isMuted)for(var b in this.soundList)sound=this.soundList[b],0<sound.seekId&&this.resume(b)},volume:function(a){if("number"!==
typeof a)console.warn("Argument needs to be a number!");else{a=a.limit(0,1);for(var b in this.soundList)this.soundList[b].volume(a)}},getVolume:function(){for(var a in this.soundList)return this.soundList[a].volume()}})});ig.baked=!0;ig.module("plugins.audio.sound-info").defines(function(){SoundInfo=ig.Class.extend({sfx:{logosplash1:{path:"opening/logosplash1"},logosplash2:{path:"opening/logosplash2"}},bgm:{background:{path:"bgm",loop:!0}}})});ig.baked=!0;
ig.module("plugins.audio.sound-handler").requires("plugins.audio.howler-player","plugins.audio.sound-info").defines(function(){ig.SoundHandler=ig.Class.extend({bgmPlayer:null,sfxPlayer:null,soundInfo:new SoundInfo,init:function(){Howler.autoSuspend=!1;this.sfxPlayer=new HowlerPlayer(this.soundInfo.sfx);this.bgmPlayer=new HowlerPlayer(this.soundInfo.bgm);ig.ua.iOS&&this.iOSAudioFix()},unlockWebAudio:function(){},iOSAudioFix:function(){Howler.ctx.onstatechange=function(){"interrupted"===Howler.ctx.state&&
Howler.ctx.resume().catch(function(){console.log("Failed to resume AudioContext, retrying...");Howler.ctx.suspend().then(function(){setTimeout(function(){Howler.ctx.resume()},1E3)})})}},muteSFX:function(a){this.sfxPlayer&&this.sfxPlayer.mute(a)},muteBGM:function(a){this.bgmPlayer&&this.bgmPlayer.mute(a)},unmuteSFX:function(a){this.sfxPlayer&&(ig.game&&(ig.game.sessionData.sfx||ig.game.sessionData.sound))&&this.sfxPlayer.unmute(a)},unmuteBGM:function(a){if(this.bgmPlayer&&ig.game&&(ig.game.sessionData.bgm||
ig.game.sessionData.music))this.bgmPlayer.unmute(a),!a&&Howler._audioUnlocked&&this.bgmPlayer.play("background")},muteAll:function(a){this.muteSFX(a);this.muteBGM(a)},unmuteAll:function(a){this.unmuteSFX(a);this.unmuteBGM(a)},forceMuteAll:function(){this.muteAll(!0)},forceUnMuteAll:function(){this.unmuteAll(!0)},forceLoopBGM:function(){},onSystemPause:function(){},onSystemResume:function(){},getSFX:function(a){return this.sfxPlayer.soundList[a]},getBGM:function(){return this.bgmPlayer.soundList.background},
sfxPlaying:function(a){return this.getSFX(a).playing()},bgmPlaying:function(){return this.getBGM().playing()}})});ig.baked=!0;
ig.module("plugins.handlers.visibility-handler").requires("plugins.audio.sound-handler").defines(function(){ig.VisibilityHandler=ig.Class.extend({version:"1.2.3",config:{muteOnBlur:!0,pauseOnBlur:!0,clearInputStateOnPause:!1,clearInputStateOnResume:!0,allowResumeWithoutFocus:{desktop:!0,mobile:{kaios:!1,"default":!0}},handlerDelay:{desktop:0,mobile:{kaios:0,"default":0}},autoFocusOnResume:{desktop:!0,mobile:{kaios:!1,"default":!0}},autoFocusAfterResume:{desktop:!0,mobile:{kaios:!1,"default":!0}}},
browserPrefixes:["o","ms","moz","webkit"],activeOverlays:[],browserPrefix:null,hiddenPropertyName:null,visibilityEventName:null,visibilityStateName:null,isShowingOverlay:!1,isFocused:!1,isPaused:!1,init:function(){this.initVisibilityHandler();this.initFocusHandler();this.initPageTransitionHandler();ig.visibilityHandler=this},pauseHandler:function(){!0===this.config.clearInputStateOnPause&&this.clearAllInputState();!0===this.config.pauseOnBlur&&("undefined"!==typeof wgl&&null!==wgl&&null!==wgl.system&&
"undefined"!==typeof wgl.system&&"function"===typeof wgl.system.stopRender&&wgl.system.stopRender(),"undefined"!==typeof ig.game&&null!==ig.game&&"undefined"!==typeof ig.game.pauseGame&&ig.game.pauseGame(!0));!0===this.config.muteOnBlur&&"undefined"!==typeof ig.soundHandler&&null!==ig.soundHandler&&"function"===typeof ig.soundHandler.forceMuteAll&&ig.soundHandler.forceMuteAll()},resumeHandler:function(){"undefined"!==typeof ig.game&&null!==ig.game&&("undefined"!==typeof ig.game.resumeGame&&ig.game.resumeGame(!0),
"undefined"!==typeof wgl&&null!==wgl&&null!==wgl.system&&"undefined"!==typeof wgl.system&&"function"===typeof wgl.system.startRender&&wgl.system.startRender());"undefined"!==typeof ig.soundHandler&&null!==ig.soundHandler&&"function"===typeof ig.soundHandler.forceUnMuteAll&&ig.soundHandler.forceUnMuteAll();!0===this.config.clearInputStateOnResume&&this.clearAllInputState()},initVisibilityHandler:function(){this.browserPrefix=this.getBrowserPrefix();this.hiddenPropertyName=this.getHiddenPropertyName(this.browserPrefix);
this.visibilityEventName=this.getVisibilityEventName(this.browserPrefix);this.visibilityStateName=this.getVisibilityStateName(this.browserPrefix);this.visibilityEventName&&document.addEventListener(this.visibilityEventName,this.onChange.bind(this),!0)},initFocusHandler:function(){window.addEventListener("blur",this.onChange.bind(this),!0);document.addEventListener("blur",this.onChange.bind(this),!0);document.addEventListener("focusout",this.onChange.bind(this),!0);window.addEventListener("focus",
this.onChange.bind(this),!0);document.addEventListener("focus",this.onChange.bind(this),!0);document.addEventListener("focusin",this.onChange.bind(this),!0)},initPageTransitionHandler:function(){window.addEventListener("pagehide",this.onChange.bind(this),!0);window.addEventListener("pageshow",this.onChange.bind(this),!0)},getBrowserPrefix:function(){var a=null;this.browserPrefixes.forEach(function(b){if(this.getHiddenPropertyName(b)in document)return a=b}.bind(this));return a},getHiddenPropertyName:function(a){return a?
a+"Hidden":"hidden"},getVisibilityEventName:function(a){return(a?a:"")+"visibilitychange"},getVisibilityStateName:function(a){return a?a+"VisibilityState":"visibilityState"},hasView:function(){return!(document[this.hiddenPropertyName]||"visible"!==document[this.visibilityStateName])},hasFocus:function(){return document.hasFocus()||this.isFocused},addActiveOverlay:function(a){if(null===this.activeOverlays||"undefined"===typeof this.activeOverlays)this.activeOverlays=[];null===a||"undefined"===typeof a||
!0!==this.activeOverlays.includes(a)&&this.activeOverlays.push(a)},removeActiveOverlay:function(a){if(null===this.activeOverlays||"undefined"===typeof this.activeOverlays)this.activeOverlays=[];0>=this.activeOverlays.length||null===a||"undefined"===typeof a||!0===this.activeOverlays.includes(a)&&this.activeOverlays.splice(this.activeOverlays.indexOf(a),1)},onOverlayShow:function(a){null!==a&&"undefined"!==typeof a&&this.addActiveOverlay(a);0<this.activeOverlays.length&&(this.systemPaused(),this.isShowingOverlay=
!0)},onOverlayHide:function(a){null!==a&&"undefined"!==typeof a&&this.removeActiveOverlay(a);0>=this.activeOverlays.length&&(this.isShowingOverlay=!1,this.systemResumed())},systemPaused:function(){if(this.isPaused)return!1;this.pauseHandler();return this.isPaused=!0},systemResumed:function(){if(!this.isPaused||!this.hasView()||this.isShowingOverlay)return!1;if(!this.hasFocus())if(ig.ua.mobile)if(this.isKaiOS()){if(!this.config.allowResumeWithoutFocus.mobile.kaios)return!1}else{if(!this.config.allowResumeWithoutFocus.mobile.default)return!1}else if(!this.config.allowResumeWithoutFocus.desktop)return!1;
this.focusOnResume();this.resumeHandler();this.focusAfterResume();this.isPaused=!1;return!0},clearAllInputState:function(){"undefined"!==typeof ig.input&&null!==ig.input&&("function"===typeof ig.input.clearAllState&&ig.input.clearAllState(),"function"===typeof ig.input.clearPressed&&ig.input.clearPressed(),ig.input.locks={})},isKaiOS:function(){return/KAIOS/.test(navigator.userAgent)||!1},focusOnResume:function(){var a=!1,a=ig.ua.mobile?this.isKaiOS()?this.config.autoFocusOnResume.mobile.kaios:this.config.autoFocusOnResume.mobile.default:
this.config.autoFocusOnResume.desktop;this.focus(a)},focusAfterResume:function(){var a=!1,a=ig.ua.mobile?this.isKaiOS()?this.config.autoFocusAfterResume.mobile.kaios:this.config.autoFocusAfterResume.mobile.default:this.config.autoFocusAfterResume.desktop;this.focus(a)},focus:function(a){window.focus&&a&&(document.activeElement&&"function"===typeof document.activeElement.blur&&document.activeElement.blur(),window.focus())},handleDelayedEvent:function(a){if(!this.hasView()||"pause"===a.type||"pageHide"===
a.type||"blur"===a.type||"focusout"===a.type){if("blur"===a.type||"focusout"===a.type){var b=a.path||a.composedPath&&a.composedPath();if(b&&2<b.length||a.srcElement&&("INPUT"===a.srcElement.tagName||"TEXTAREA"===a.srcElement.tagName))return!1;this.isFocused=!1}return this.systemPaused(a)}if("focus"===a.type||"focusin"===a.type)this.isFocused=!0;return this.systemResumed(a)},startDelayedEventHandler:function(a){ig.ua.mobile?this.isKaiOS()?0<this.config.handlerDelay.mobile.kaios?window.setTimeout(function(a){this.handleDelayedEvent(a)}.bind(this,
a),this.config.handlerDelay.mobile):this.handleDelayedEvent(a):0<this.config.handlerDelay.mobile.default?window.setTimeout(function(a){this.handleDelayedEvent(a)}.bind(this,a),this.config.handlerDelay.mobile):this.handleDelayedEvent(a):0<this.config.handlerDelay.desktop?window.setTimeout(function(a){this.handleDelayedEvent(a)}.bind(this,a),this.config.handlerDelay.desktop):this.handleDelayedEvent(a)},onChange:function(a){this.startDelayedEventHandler(a)}})});ig.baked=!0;
ig.module("plugins.handlers.size-handler").requires("plugins.data.vector","plugins.handlers.visibility-handler").defines(function(){ig.SizeHandler=ig.Class.extend({portraitMode:!1,disableStretchToFitOnMobileFlag:!0,enableStretchToFitOnAntiPortraitModeFlag:!0,enableScalingLimitsOnMobileFlag:!1,minScalingOnMobile:0,maxScalingOnMobile:1,enableStretchToFitOnDesktopFlag:!1,enableScalingLimitsOnDesktopFlag:!1,minScalingOnDesktop:0,maxScalingOnDesktop:1,desktop:{actualSize:new Vector2(window.innerWidth,
window.innerHeight),actualResolution:new Vector2(1920,1080)},mobile:{actualSize:new Vector2(window.innerWidth,window.innerHeight),actualResolution:new Vector2(1920,1080)},windowSize:new Vector2(window.innerWidth,window.innerHeight),scaleRatioMultiplier:new Vector2(1,1),sizeRatio:new Vector2(1,1),scale:1,domHandler:null,dynamicClickableEntityDivs:{},coreDivsToResize:["#canvas","#play","#orientate"],adsToResize:{MobileAdInGamePreroll:{"box-width":_SETTINGS.Ad.Mobile.Preroll.Width+2,"box-height":_SETTINGS.Ad.Mobile.Preroll.Height+
20},MobileAdInGameEnd:{"box-width":_SETTINGS.Ad.Mobile.End.Width+2,"box-height":_SETTINGS.Ad.Mobile.End.Height+20},MobileAdInGamePreroll2:{"box-width":_SETTINGS.Ad.Mobile.Preroll.Width+2,"box-height":_SETTINGS.Ad.Mobile.Preroll.Height+20},MobileAdInGameEnd2:{"box-width":_SETTINGS.Ad.Mobile.End.Width+2,"box-height":_SETTINGS.Ad.Mobile.End.Height+20},MobileAdInGamePreroll3:{"box-width":_SETTINGS.Ad.Mobile.Preroll.Width+2,"box-height":_SETTINGS.Ad.Mobile.Preroll.Height+20},MobileAdInGameEnd3:{"box-width":_SETTINGS.Ad.Mobile.End.Width+
2,"box-height":_SETTINGS.Ad.Mobile.End.Height+20}},init:function(a){this.domHandler=a;if("undefined"===typeof a)throw"undefined Dom Handler for Size Handler";this.sizeCalcs();this.eventListenerSetup();this.samsungFix()},sizeCalcs:function(){this.windowSize=new Vector2(window.innerWidth,window.innerHeight);if(ig.ua.mobile){this.mobile.actualSize=new Vector2(window.innerWidth,window.innerHeight);var a=new Vector2(this.mobile.actualResolution.x,this.mobile.actualResolution.y);this.scaleRatioMultiplier=
new Vector2(this.mobile.actualSize.x/a.x,this.mobile.actualSize.y/a.y);if(this.disableStretchToFitOnMobileFlag){var b=Math.min(this.scaleRatioMultiplier.x,this.scaleRatioMultiplier.y);this.enableScalingLimitsOnMobileFlag&&(b=b.limit(this.minScalingOnMobile,this.maxScalingOnMobile));this.mobile.actualSize.x=a.x*b;this.mobile.actualSize.y=a.y*b;this.scaleRatioMultiplier.x=b;this.scaleRatioMultiplier.y=b}else this.sizeRatio.x=this.scaleRatioMultiplier.x,this.sizeRatio.y=this.scaleRatioMultiplier.y,this.scaleRatioMultiplier.x=
1,this.scaleRatioMultiplier.y=1}else this.desktop.actualSize=new Vector2(window.innerWidth,window.innerHeight),a=new Vector2(this.desktop.actualResolution.x,this.desktop.actualResolution.y),this.scaleRatioMultiplier=new Vector2(this.desktop.actualSize.x/a.x,this.desktop.actualSize.y/a.y),this.enableStretchToFitOnDesktopFlag?(this.sizeRatio.x=this.scaleRatioMultiplier.x,this.sizeRatio.y=this.scaleRatioMultiplier.y,this.scaleRatioMultiplier.x=1,this.scaleRatioMultiplier.y=1):(b=Math.min(this.scaleRatioMultiplier.x,
this.scaleRatioMultiplier.y),this.enableScalingLimitsOnDesktopFlag&&(b=b.limit(this.minScalingOnDesktop,this.maxScalingOnDesktop)),this.desktop.actualSize.x=a.x*b,this.desktop.actualSize.y=a.y*b,this.scaleRatioMultiplier.x=b,this.scaleRatioMultiplier.y=b)},resizeLayers:function(){for(var a=0;a<this.coreDivsToResize.length;a++){var b=ig.domHandler.getElementById(this.coreDivsToResize[a]);if(ig.ua.mobile)if(this.disableStretchToFitOnMobileFlag){var c=Math.floor(ig.sizeHandler.windowSize.x/2-ig.sizeHandler.mobile.actualSize.x/
2),d=Math.floor(ig.sizeHandler.windowSize.y/2-ig.sizeHandler.mobile.actualSize.y/2);0>c&&(c=0);0>d&&(d=0);ig.domHandler.resizeOffset(b,Math.floor(ig.sizeHandler.mobile.actualSize.x),Math.floor(ig.sizeHandler.mobile.actualSize.y),c,d);var e=!1;if(e=this.portraitMode?window.innerHeight<window.innerWidth:window.innerHeight>window.innerWidth)if(this.enableStretchToFitOnAntiPortraitModeFlag)ig.domHandler.resizeOffset(b,Math.floor(window.innerWidth),Math.floor(window.innerHeight),0,0);else{var e=new Vector2(window.innerWidth/
this.mobile.actualResolution.y,window.innerHeight/this.mobile.actualResolution.x),c=Math.min(e.x,e.y),e=this.mobile.actualResolution.y*c,g=this.mobile.actualResolution.x*c,c=Math.floor(ig.sizeHandler.windowSize.x/2-e/2),d=Math.floor(ig.sizeHandler.windowSize.y/2-g/2);0>c&&(c=0);0>d&&(d=0);ig.domHandler.resizeOffset(b,Math.floor(e),Math.floor(g),c,d)}}else ig.domHandler.resize(b,Math.floor(ig.sizeHandler.mobile.actualSize.x),Math.floor(ig.sizeHandler.mobile.actualSize.y));else this.enableStretchToFitOnDesktopFlag?
ig.domHandler.resize(b,Math.floor(ig.sizeHandler.desktop.actualSize.x),Math.floor(ig.sizeHandler.desktop.actualSize.y)):(c=Math.floor(ig.sizeHandler.windowSize.x/2-ig.sizeHandler.desktop.actualSize.x/2),d=Math.floor(ig.sizeHandler.windowSize.y/2-ig.sizeHandler.desktop.actualSize.y/2),0>c&&(c=0),0>d&&(d=0),ig.domHandler.resizeOffset(b,Math.floor(ig.sizeHandler.desktop.actualSize.x),Math.floor(ig.sizeHandler.desktop.actualSize.y),c,d))}for(var j in this.adsToResize)a=ig.domHandler.getElementById("#"+
j),b=ig.domHandler.getElementById("#"+j+"-Box"),e=(window.innerWidth-this.adsToResize[j]["box-width"])/2+"px",c=(window.innerHeight-this.adsToResize[j]["box-height"])/2+"px",a&&ig.domHandler.css(a,{width:window.innerWidth,height:window.innerHeight}),b&&ig.domHandler.css(b,{left:e,top:c});a=ig.domHandler.getElementById("#canvas");b=ig.domHandler.getOffsets(a);a=b.left;b=b.top;e=Math.min(ig.sizeHandler.scaleRatioMultiplier.x,ig.sizeHandler.scaleRatioMultiplier.y);for(j in this.dynamicClickableEntityDivs){c=
ig.domHandler.getElementById("#"+j);if(ig.ua.mobile){var g=this.dynamicClickableEntityDivs[j].entity_pos_x,p=this.dynamicClickableEntityDivs[j].entity_pos_y,f=this.dynamicClickableEntityDivs[j].width,d=this.dynamicClickableEntityDivs[j].height;this.disableStretchToFitOnMobileFlag?(g=Math.floor(a+g*this.scaleRatioMultiplier.x)+"px",p=Math.floor(b+p*this.scaleRatioMultiplier.y)+"px",f=Math.floor(f*this.scaleRatioMultiplier.x)+"px",d=Math.floor(d*this.scaleRatioMultiplier.y)+"px"):(g=Math.floor(g*this.sizeRatio.x)+
"px",p=Math.floor(p*this.sizeRatio.y)+"px",f=Math.floor(f*this.sizeRatio.x)+"px",d=Math.floor(d*this.sizeRatio.y)+"px")}else g=this.dynamicClickableEntityDivs[j].entity_pos_x,p=this.dynamicClickableEntityDivs[j].entity_pos_y,f=this.dynamicClickableEntityDivs[j].width,d=this.dynamicClickableEntityDivs[j].height,this.enableStretchToFitOnDesktopFlag?(g=Math.floor(g*this.sizeRatio.x)+"px",p=Math.floor(p*this.sizeRatio.y)+"px",f=Math.floor(f*this.sizeRatio.x)+"px",d=Math.floor(d*this.sizeRatio.y)+"px"):
(g=Math.floor(a+g*this.scaleRatioMultiplier.x)+"px",p=Math.floor(b+p*this.scaleRatioMultiplier.y)+"px",f=Math.floor(f*this.scaleRatioMultiplier.x)+"px",d=Math.floor(d*this.scaleRatioMultiplier.y)+"px");ig.domHandler.css(c,{"float":"left",position:"absolute",left:g,top:p,width:f,height:d,"z-index":3});this.dynamicClickableEntityDivs[j]["font-size"]&&ig.domHandler.css(c,{"font-size":this.dynamicClickableEntityDivs[j]["font-size"]*e+"px"})}$("#ajaxbar").width(this.windowSize.x);$("#ajaxbar").height(this.windowSize.y)},
resize:function(){this.sizeCalcs();this.resizeLayers()},reorient:function(){console.log("changing orientation ...");if(ig.ua.mobile){var a=!1,a=this.portraitMode?window.innerHeight<window.innerWidth:window.innerHeight>window.innerWidth,b=this.domHandler.getElementById("#orientate"),c=this.domHandler.getElementById("#game");if(a){if(this.domHandler.show(b),this.domHandler.hide(c),null!==ig.visibilityHandler&&"undefined"!==typeof ig.visibilityHandler&&null!==ig.visibilityHandler.onOverlayShow&&"function"===
typeof ig.visibilityHandler.onOverlayShow)if(1<=b.length)ig.visibilityHandler.onOverlayShow(b[0]);else ig.visibilityHandler.onOverlayShow("orientate")}else if(this.domHandler.show(c),this.domHandler.hide(b),null!==ig.visibilityHandler&&"undefined"!==typeof ig.visibilityHandler&&null!==ig.visibilityHandler.onOverlayHide&&"function"===typeof ig.visibilityHandler.onOverlayHide)if(1<=b.length)ig.visibilityHandler.onOverlayHide(b[0]);else ig.visibilityHandler.onOverlayHide("orientate")}ig.ua.mobile?(this.resize(),
this.resizeAds()):this.resize()},resizeAds:function(){for(var a in this.adsToResize){var b=ig.domHandler.getElementById("#"+a),c=ig.domHandler.getElementById("#"+a+"-Box"),d=(window.innerWidth-this.adsToResize[a]["box-width"])/2+"px",e=(window.innerHeight-this.adsToResize[a]["box-height"])/2+"px";b&&ig.domHandler.css(b,{width:window.innerWidth,height:window.innerHeight});c&&ig.domHandler.css(c,{left:d,top:e})}},samsungFix:function(){ig.ua.android&&(!(4.2>parseFloat(navigator.userAgent.slice(navigator.userAgent.indexOf("Android")+
8,navigator.userAgent.indexOf("Android")+11)))&&!(0>navigator.userAgent.indexOf("GT"))&&!(0<navigator.userAgent.indexOf("Chrome"))&&!(0<navigator.userAgent.indexOf("Firefox")))&&(document.addEventListener("touchstart",function(a){a.preventDefault();return!1},!1),document.addEventListener("touchmove",function(a){a.preventDefault();return!1},!1),document.addEventListener("touchend",function(a){a.preventDefault();return!1},!1))},orientationInterval:null,orientationTimeout:null,orientationHandler:function(){this.reorient();
window.scrollTo(0,1)},orientationDelayHandler:function(){null==this.orientationInterval&&(this.orientationInterval=window.setInterval(this.orientationHandler.bind(this),100));null==this.orientationTimeout&&(this.orientationTimeout=window.setTimeout(function(){this.clearAllIntervals()}.bind(this),2E3))},clearAllIntervals:function(){window.clearInterval(this.orientationInterval);this.orientationInterval=null;window.clearTimeout(this.orientationTimeout);this.orientationTimeout=null},eventListenerSetup:function(){ig.ua.iOS?
(window.addEventListener("orientationchange",this.orientationDelayHandler.bind(this)),window.addEventListener("resize",this.orientationDelayHandler.bind(this))):(window.addEventListener("orientationchange",this.orientationHandler.bind(this)),window.addEventListener("resize",this.orientationHandler.bind(this)));document.addEventListener("touchmove",function(a){window.scrollTo(0,1);a.preventDefault()},{passive:!1});this.chromePullDownRefreshFix()},chromePullDownRefreshFix:function(){var a=window.chrome||
navigator.userAgent.match("CriOS"),b="ontouchstart"in document.documentElement;if(a&&b){var c=a=!1,d=0,e=!1;try{CSS.supports("overscroll-behavior-y","contain")&&(a=!0)}catch(g){}try{if(a)return document.body.style.overscrollBehaviorY="contain"}catch(j){}a=document.head||document.body;b=document.createElement("style");b.type="text/css";b.styleSheet?b.styleSheet.cssText="\n      ::-webkit-scrollbar {\n        width: 500x;\n      }\n      ::-webkit-scrollbar-thumb {\n        border-radius: 500px;\n        background-color: rgba(0, 0, 0, 0.2);\n      }\n      body {\n        -webkit-overflow-scrolling: auto!important;\n      }\n    ":
b.appendChild(document.createTextNode("\n      ::-webkit-scrollbar {\n        width: 500px;\n      }\n      ::-webkit-scrollbar-thumb {\n        border-radius: 500px;\n        background-color: rgba(0, 0, 0, 0.2);\n      }\n      body {\n        -webkit-overflow-scrolling: auto!important;\n      }\n    "));a.appendChild(b);try{addEventListener("test",null,{get passive(){c=!0}})}catch(p){}document.addEventListener("touchstart",function(a){1===a.touches.length&&(d=a.touches[0].clientY,e=0===window.pageYOffset)},
!!c&&{passive:!0});document.addEventListener("touchmove",function(a){var b;if(b=e){e=!1;b=a.touches[0].clientY;var c=b-d;b=(d=b,0<c)}if(b)return a.preventDefault()},!!c&&{passive:!1})}}})});ig.baked=!0;
ig.module("plugins.handlers.api-handler").defines(function(){ig.ApiHandler=ig.Class.extend({apiAvailable:{MJSPreroll:function(){ig.ua.mobile&&ig.domHandler.JQUERYAVAILABLE&&_SETTINGS&&_SETTINGS.Ad.Mobile.Preroll.Enabled&&MobileAdInGamePreroll.Initialize()},MJSHeader:function(){ig.ua.mobile&&ig.domHandler.JQUERYAVAILABLE&&_SETTINGS.Ad.Mobile.Header.Enabled&&MobileAdInGameHeader.Initialize()},MJSFooter:function(){ig.ua.mobile&&ig.domHandler.JQUERYAVAILABLE&&_SETTINGS.Ad.Mobile.Footer.Enabled&&MobileAdInGameFooter.Initialize()},
MJSEnd:function(){ig.ua.mobile&&ig.domHandler.JQUERYAVAILABLE&&_SETTINGS.Ad.Mobile.End.Enabled&&MobileAdInGameEnd.Initialize()}},run:function(a,b){if(this.apiAvailable[a])this.apiAvailable[a](b)}})});ig.baked=!0;
ig.module("plugins.io.storage").defines(function(){ig.Storage=ig.Class.extend({staticInstantiate:function(){return!ig.Storage.instance?null:ig.Storage.instance},init:function(){ig.Storage.instance=this},isCapable:function(){return"undefined"!==typeof window.localStorage},isSet:function(a){return null!==this.get(a)},initUnset:function(a,b){null===this.get(a)&&this.set(a,b)},get:function(a){if(!this.isCapable())return null;try{return JSON.parse(localStorage.getItem(a))}catch(b){return window.localStorage.getItem(a)}},
getInt:function(a){return~~this.get(a)},getFloat:function(a){return parseFloat(this.get(a))},getBool:function(a){return!!this.get(a)},key:function(a){return this.isCapable()?window.localStorage.key(a):null},set:function(a,b){if(!this.isCapable())return null;try{window.localStorage.setItem(a,JSON.stringify(b))}catch(c){console.log(c)}},setHighest:function(a,b){b>this.getFloat(a)&&this.set(a,b)},remove:function(a){if(!this.isCapable())return null;window.localStorage.removeItem(a)},clear:function(){if(!this.isCapable())return null;
window.localStorage.clear()}})});ig.baked=!0;
ig.module("plugins.io.mouse").requires("plugins.data.vector").defines(function(){Mouse=ig.Class.extend({pos:new Vector2(0,0),bindings:{click:[ig.KEY.MOUSE1]},init:function(){ig.input.initMouse();for(var a in this.bindings){this[a]=a;for(var b=0;b<this.bindings[a].length;b++)ig.input.bind(this.bindings[a][b],a)}},getLast:function(){return this.pos},getPos:function(){var a=ig.system.scale*((ig.system.canvas.offsetWidth||ig.system.realWidth)/ig.system.realWidth);this.pos.set(ig.input.mouse.x*a/ig.sizeHandler.sizeRatio.x/
ig.sizeHandler.scaleRatioMultiplier.x,ig.input.mouse.y*a/ig.sizeHandler.sizeRatio.y/ig.sizeHandler.scaleRatioMultiplier.y);return this.pos.clone()}})});ig.baked=!0;
ig.module("plugins.io.keyboard").defines(function(){Keyboard=ig.Class.extend({bindings:{PREVENT_PAGE_SCROLLING_OVER_IFRAME:[ig.KEY.UP_ARROW,ig.KEY.DOWN_ARROW,ig.KEY.LEFT_ARROW,ig.KEY.RIGHT_ARROW,ig.KEY.PAGE_UP,ig.KEY.PAGE_DOWN],up:[ig.KEY.W,ig.KEY.UP_ARROW],right:[ig.KEY.D,ig.KEY.RIGHT_ARROW],left:[ig.KEY.A,ig.KEY.LEFT_ARROW],down:[ig.KEY.S,ig.KEY.DOWN_ARROW,ig.KEY.SPACE]},init:function(){for(var a in this.bindings){this[a]=a;for(var b=0;b<this.bindings[a].length;b++)ig.input.bind(this.bindings[a][b],
a)}}})});ig.baked=!0;
ig.module("plugins.io.gamepad-input").defines(function(){ig.PADKEY={BUTTON_0:0,PADBUTTON_1:1,BUTTON_2:2,BUTTON_3:3,BUTTON_LEFT_BUMPER:4,BUTTON_RIGHT_BUMPER:5,BUTTON_LEFT_TRIGGER:6,BUTTON_RIGHT_TRIGGER:7,BUTTON_LEFT_JOYSTICK:10,BUTTON_RIGHT_JOYSTICK:11,BUTTON_DPAD_UP:12,BUTTON_DPAD_DOWN:13,BUTTON_DPAD_LEFT:14,BUTTON_DPAD_RIGHT:15,BUTTON_MENU:16,AXIS_LEFT_JOYSTICK_X:0,AXIS_LEFT_JOYSTICK_Y:1,AXIS_RIGHT_JOYSTICK_X:2,AXIS_RIGHT_JOYSTICK_Y:3};ig.GamepadInput=ig.Class.extend({isInit:!1,isSupported:!1,list:[],
bindings:{},states:{},presses:{},releases:{},downLocks:{},upLocks:{},leftStick:{x:0,y:0},rightStick:{x:0,y:0},start:function(){if(!this.isInit){this.isInit=!0;var a=navigator.getGamepads||navigator.webkitGetGamepads;a&&(!navigator.getGamepads&&navigator.webkitGetGamepads&&(navigator.getGamepads=navigator.webkitGetGamepads),this.list=navigator.getGamepads());this.isSupported=a}},isAvailable:function(){return this.isInit&&this.isSupported},buttonPressed:function(a){return"object"==typeof a?a.pressed:
1==a},buttonDown:function(a){if(a=this.bindings[a])this.states[a]=!0,this.downLocks[a]||(this.presses[a]=!0,this.downLocks[a]=!0)},buttonUp:function(a){if((a=this.bindings[a])&&this.downLocks[a]&&!this.upLocks[a])this.states[a]=!1,this.releases[a]=!0,this.upLocks[a]=!0},clearPressed:function(){for(var a in this.releases)this.states[a]=!1,this.downLocks[a]=!1;this.releases={};this.presses={};this.upLocks={}},bind:function(a,b){this.bindings[a]=b},unbind:function(a){this.releases[this.bindings[a]]=
!0;this.bindings[a]=null},unbindAll:function(){this.bindings={};this.states={};this.presses={};this.releases={};this.downLocks={};this.upLocks={}},state:function(a){return this.states[a]},pressed:function(a){return this.presses[a]},released:function(a){return this.releases[a]},clamp:function(a,b,c){return a<b?b:a>c?c:a},pollGamepads:function(){if(this.isSupported){this.leftStick.x=0;this.leftStick.y=0;this.rightStick.x=0;this.rightStick.y=0;this.list=navigator.getGamepads();for(var a in this.bindings){for(var b=
!1,c=0;c<this.list.length;c++){var d=this.list[c];if(d&&d.buttons&&this.buttonPressed(d.buttons[a])){b=!0;break}}b?this.buttonDown(a):this.buttonUp(a)}for(c=0;c<this.list.length;c++)if((d=this.list[c])&&d.axes){a=d.axes[ig.GAMEPADINPUT.AXIS_LEFT_JOYSTICK_X];var b=d.axes[ig.GAMEPADINPUT.AXIS_LEFT_JOYSTICK_Y],e=d.axes[ig.GAMEPADINPUT.AXIS_RIGHT_JOYSTICK_X],d=d.axes[ig.GAMEPADINPUT.AXIS_RIGHT_JOYSTICK_Y];this.leftStick.x+=isNaN(a)?0:a;this.leftStick.y+=isNaN(b)?0:b;this.rightStick.x+=isNaN(e)?0:e;this.rightStick.y+=
isNaN(d)?0:d}0<this.list.length&&(this.leftStick.x=this.clamp(this.leftStick.x,-1,1),this.leftStick.y=this.clamp(this.leftStick.y,-1,1),this.rightStick.x=this.clamp(this.rightStick.x,-1,1),this.rightStick.y=this.clamp(this.rightStick.y,-1,1))}}})});ig.baked=!0;
ig.module("plugins.io.gamepad").requires("plugins.io.gamepad-input").defines(function(){Gamepad=ig.Class.extend({bindings:{padJump:[ig.PADKEY.BUTTON_0]},init:function(){ig.gamepadInput.start();for(var a in this.bindings)for(var b=0;b<this.bindings[a].length;b++)ig.gamepadInput.bind(this.bindings[a][b],a)},press:function(){},held:function(){},release:function(){}})});ig.baked=!0;
ig.module("plugins.io.multitouch").defines(function(){Multitouch=ig.Class.extend({init:function(){ig.multitouchInput.start()},getTouchesPos:function(){if(ig.ua.mobile){if(0<ig.multitouchInput.touches.length){for(var a=[],b=0;b<ig.multitouchInput.touches.length;b++){var c=ig.multitouchInput.touches[b];a.push({x:c.x,y:c.y})}return a}return null}}})});ig.baked=!0;
ig.module("plugins.io.multitouch-input").defines(function(){ig.MultitouchInput=ig.Class.extend({isStart:!1,touches:[],multitouchCapable:!1,lastEventUp:null,start:function(){this.isStart||(this.isStart=!0,navigator.maxTouchPoints&&1<navigator.maxTouchPoints&&(this.multitouchCapable=!0),ig.ua.touchDevice&&(window.navigator.msPointerEnabled&&(ig.system.canvas.addEventListener("MSPointerDown",this.touchdown.bind(this),!1),ig.system.canvas.addEventListener("MSPointerUp",this.touchup.bind(this),!1),ig.system.canvas.addEventListener("MSPointerMove",
this.touchmove.bind(this),!1),ig.system.canvas.style.msContentZooming="none",ig.system.canvas.style.msTouchAction="none"),ig.system.canvas.addEventListener("touchstart",this.touchdown.bind(this),!1),ig.system.canvas.addEventListener("touchend",this.touchup.bind(this),!1),ig.system.canvas.addEventListener("touchmove",this.touchmove.bind(this),!1)))},touchmove:function(a){if(ig.ua.touchDevice){var b=parseInt(ig.system.canvas.offsetWidth)||ig.system.realWidth,c=parseInt(ig.system.canvas.offsetHeight)||
ig.system.realHeight,b=ig.system.scale*(b/ig.system.realWidth),c=ig.system.scale*(c/ig.system.realHeight);if(a.touches){for(;0<this.touches.length;)this.touches.pop();!this.multitouchCapable&&1<a.touches.length&&(this.multitouchCapable=!0);var d={left:0,top:0};ig.system.canvas.getBoundingClientRect&&(d=ig.system.canvas.getBoundingClientRect());for(var e=0;e<a.touches.length;e++){var g=a.touches[e];g&&this.touches.push({x:(g.clientX-d.left)/b,y:(g.clientY-d.top)/c})}}else this.windowMove(a)}try{ig.soundHandler.unlockWebAudio()}catch(j){}},
touchdown:function(a){var b=parseInt(ig.system.canvas.offsetWidth)||ig.system.realWidth,c=parseInt(ig.system.canvas.offsetHeight)||ig.system.realHeight,b=ig.system.scale*(b/ig.system.realWidth),c=ig.system.scale*(c/ig.system.realHeight);if(window.navigator.msPointerEnabled)this.windowKeyDown(a);else if(ig.ua.touchDevice&&a.touches){for(;0<this.touches.length;)this.touches.pop();!this.multitouchCapable&&1<a.touches.length&&(this.multitouchCapable=!0);var d={left:0,top:0};ig.system.canvas.getBoundingClientRect&&
(d=ig.system.canvas.getBoundingClientRect());for(var e=0;e<a.touches.length;e++){var g=a.touches[e];g&&this.touches.push({x:(g.clientX-d.left)/b,y:(g.clientY-d.top)/c})}}},touchup:function(a){var b=parseInt(ig.system.canvas.offsetWidth)||ig.system.realWidth;parseInt(ig.system.canvas.offsetHeight);b=ig.system.scale*(b/ig.system.realWidth);if(window.navigator.msPointerEnabled)this.windowKeyUp(a);else{this.lastEventUp=a;var c={left:0,top:0};ig.system.canvas.getBoundingClientRect&&(c=ig.system.canvas.getBoundingClientRect());
if(ig.ua.touchDevice){a=(a.changedTouches[0].clientX-c.left)/b;for(b=0;b<this.touches.length;b++)this.touches[b].x>=a-40&&this.touches[b].x<=a+40&&this.touches.splice(b,1)}}if(ig.visibilityHandler)ig.visibilityHandler.onChange("focus");try{ig.soundHandler.unlockWebAudio()}catch(d){}},windowKeyDown:function(a){var b=parseInt(ig.system.canvas.offsetWidth)||ig.system.realWidth,c=parseInt(ig.system.canvas.offsetHeight)||ig.system.realHeight,b=ig.system.scale*(b/ig.system.realWidth),c=ig.system.scale*
(c/ig.system.realHeight);if(window.navigator.msPointerEnabled){var d={left:0,top:0};ig.system.canvas.getBoundingClientRect&&(d=ig.system.canvas.getBoundingClientRect());a=a.changedTouches?a.changedTouches:[a];for(var e=0;e<a.length;++e){for(var g=a[e],j="undefined"!=typeof g.identifier?g.identifier:"undefined"!=typeof g.pointerId?g.pointerId:1,p=(g.clientX-d.left)/b,g=(g.clientY-d.top)/c,f=0;f<this.touches.length;++f)this.touches[f].identifier==j&&this.touches.splice(f,1);this.touches.push({x:p,y:g,
identifier:j})}for(b=0;b<this.touches.length;b++);}},windowKeyUp:function(a){a="undefined"!=typeof a.identifier?a.identifier:"undefined"!=typeof a.pointerId?a.pointerId:1;for(var b=0;b<this.touches.length;++b)this.touches[b].identifier==a&&this.touches.splice(b,1);for(;0<this.touches.length;)this.touches.pop();if(ig.visibilityHandler)ig.visibilityHandler.onChange("focus");try{ig.soundHandler.unlockWebAudio()}catch(c){}},windowMove:function(a){var b=parseInt(ig.system.canvas.offsetWidth)||ig.system.realWidth,
c=parseInt(ig.system.canvas.offsetHeight)||ig.system.realHeight,b=ig.system.scale*(b/ig.system.realWidth),c=ig.system.scale*(c/ig.system.realHeight),d={left:0,top:0};ig.system.canvas.getBoundingClientRect&&(d=ig.system.canvas.getBoundingClientRect());if(window.navigator.msPointerEnabled)for(var e="undefined"!=typeof a.identifier?a.identifier:"undefined"!=typeof a.pointerId?a.pointerId:1,g=0;g<this.touches.length;++g)if(this.touches[g].identifier==e){var j=(a.clientY-d.top)/c;this.touches[g].x=(a.clientX-
d.left)/b;this.touches[g].y=j}try{ig.soundHandler.unlockWebAudio()}catch(p){}},clear:function(){for(var a=0;a<this.released.length;++a)this.released[a]&&(this.released.splice(a,1),a--)},pollMultitouch:function(a){!this.multitouchCapable&&1<a&&(this.multitouchCapable=!0)},spliceFromArray:function(a,b){for(var c=0;c<b.length;c++)for(var d=0;d<a.length;d++)a[d].identifier===b[c].identifier&&(a.splice(d,1),d--)},updateSizeProperties:function(){var a=parseInt(ig.system.canvas.offsetWidth)||ig.system.realWidth,
b=parseInt(ig.system.canvas.offsetHeight)||ig.system.realHeight;this.scaleX=ig.system.scale*(a/ig.system.realWidth);this.scaleY=ig.system.scale*(b/ig.system.realHeight)},upgrade:function(a,b,c){var d={left:0,top:0};ig.system.canvas.getBoundingClientRect&&(d=ig.system.canvas.getBoundingClientRect());for(var e=(c.clientX-d.left)/this.scaleX,d=(c.clientY-d.top)/this.scaleY,g=0;g<a.length;g++)if(void 0!==typeof a[g].identifier&&void 0!==typeof c.identifier&&c.identifier===a[g].identifier){a.splice(g,
1);b.push({identifier:c.identifier,x:e,y:d});break}},updateArray:function(a,b){var c={left:0,top:0};ig.system.canvas.getBoundingClientRect&&(c=ig.system.canvas.getBoundingClientRect());for(var d=(b.clientX-c.left)/this.scaleX,c=(b.clientY-c.top)/this.scaleY,e=0;e<a.length;e++)if(void 0!==typeof a[e].identifier&&void 0!==typeof b.identifier&&b.identifier===a[e].identifier){a[e].x=d;a[e].y=c;break}}})});ig.baked=!0;
ig.module("plugins.io.fake-storage").requires("impact.game").defines(function(){ig.FakeStorage=ig.Class.extend({tempData:{},init:function(){ig.FakeStorage.instance=this},initUnset:function(a,b){null===this.get(a)&&this.set(a,b)},set:function(a,b){this.tempData[a]=JSON.stringify(b)},setItem:function(a,b){this.tempData[a]=JSON.stringify(b)},setHighest:function(a,b){b>this.getFloat(a)&&this.set(a,b)},get:function(a){return"undefined"==typeof this.tempData[a]?null:JSON.parse(this.tempData[a])},getItem:function(a){return"undefined"==
typeof this.tempData[a]?null:JSON.parse(this.tempData[a])},getInt:function(a){return~~this.get(a)},getFloat:function(a){return parseFloat(this.get(a))},getBool:function(a){return!!this.get(a)},isSet:function(a){return null!==this.get(a)},remove:function(a){delete this.tempData[a]},removeItem:function(a){delete this.tempData[a]},clear:function(){this.tempData={}}})});ig.baked=!0;
ig.module("plugins.io.io-manager").requires("plugins.io.storage","plugins.io.mouse","plugins.io.keyboard","plugins.io.gamepad","plugins.io.multitouch","plugins.io.multitouch-input","plugins.io.gamepad-input","plugins.io.fake-storage").defines(function(){IoManager=ig.Class.extend({version:"1.0.0",storage:null,localStorageSupport:!1,mouse:null,keyboard:null,multitouch:null,gamepad:null,init:function(){ig.multitouchInput=new ig.MultitouchInput;ig.gamepadInput=new ig.GamepadInput;this.unbindAll();this.initStorage();
this.initMouse();this.initKeyboard()},unbindAll:function(){ig.input.unbindAll();ig.gamepadInput.unbindAll()},initStorage:function(){try{window.localStorage.setItem("test","test"),window.localStorage.removeItem("test"),this.storage=new ig.Storage}catch(a){console.log("using fake storage"),this.storage=new ig.FakeStorage}},initMouse:function(){this.mouse=new Mouse},initKeyboard:function(){this.keyboard=new Keyboard},initMultitouch:function(){this.multitouch=new Multitouch},initGamepad:function(){this.gamepad=
new Gamepad},press:function(a){return ig.input.pressed(a)||this.gamepad&&this.gamepad.press(a)?!0:!1},held:function(a){return ig.input.state(a)||this.gamepad&&this.gamepad.state(a)?!0:!1},release:function(a){return ig.input.released(a)||this.gamepad&&this.gamepad.released(a)?!0:!1},getClickPos:function(){return this.mouse.getPos()},getLastClickPos:function(){return this.mouse.getLast()},getTouchesPos:function(){return this.multitouch.getTouchesPos()},checkOverlap:function(a,b,c,d,e){return a.x>b+
d||a.x<b||a.y>c+e||a.y<c?!1:!0},clear:function(){ig.multitouchInput.clear()},_supportsLocalStorage:function(){try{return localStorage.setItem("test","test"),localStorage.removeItem("test"),this.localStorageSupport="localStorage"in window&&null!==window.localStorage}catch(a){return this.localStorageSupport}},storageIsSet:function(a){return"function"===typeof this.storage.isSet?this.storage.isSet(a):null},storageGet:function(a){return"function"===typeof this.storage.get?this.storage.get(a):null},storageSet:function(a,
b){return"function"===typeof this.storage.set?this.storage.set(a,b):null},assert:function(a,b,c){if(b!==c)throw"actualValue:"+b+" not equal to testValue:"+c+" at "+a;}})});ig.baked=!0;
ig.module("plugins.secure-ls").requires("plugins.io.fake-storage").defines(function(){(function(){var a=function(){var a=[function(a,b,c){function d(a){return a&&a.__esModule?a:{"default":a}}Object.defineProperty(b,"__esModule",{value:!0});var f=c(1),n=d(f),f=c(2),l=d(f),f=c(8),q=d(f),f=c(9),r=d(f),f=c(10),t=d(f),f=c(11),s=d(f),f=c(16),C=d(f),f=c(17),F=d(f);c=c(18);var A=d(c),L=function(a){if(!(this instanceof L))throw new TypeError("Cannot call a class as a function");a=a||{};this._name="secure-ls";
this.utils=n["default"];this.constants=l["default"];this.Base64=r["default"];this.LZString=t["default"];this.AES=s["default"];this.DES=C["default"];this.RABBIT=F["default"];this.RC4=A["default"];this.enc=q["default"];this.config={isCompression:!0,encodingType:l["default"].EncrytionTypes.BASE64,encryptionSecret:a.encryptionSecret,encryptionNamespace:a.encryptionNamespace};this.config.isCompression="undefined"==typeof a.isCompression||a.isCompression;this.config.encodingType="undefined"!=typeof a.encodingType||
""===a.encodingType?a.encodingType.toLowerCase():l["default"].EncrytionTypes.BASE64;try{window.localStorage.setItem("test","test"),window.localStorage.removeItem("test"),this.ls=localStorage}catch(b){this.ls=new ig.FakeStorage}this.init()};c=[{key:"init",value:function(){var a=this.getMetaData();this.WarningEnum=this.constants.WarningEnum;this.WarningTypes=this.constants.WarningTypes;this.EncrytionTypes=this.constants.EncrytionTypes;this._isBase64=this._isBase64EncryptionType();this._isAES=this._isAESEncryptionType();
this._isDES=this._isDESEncryptionType();this._isRabbit=this._isRabbitEncryptionType();this._isRC4=this._isRC4EncryptionType();this._isCompression=this._isDataCompressionEnabled();this.utils.allKeys=a.keys||this.resetAllKeys()}},{key:"_isBase64EncryptionType",value:function(){return r["default"]&&("undefined"==typeof this.config.encodingType||this.config.encodingType===this.constants.EncrytionTypes.BASE64)}},{key:"_isAESEncryptionType",value:function(){return s["default"]&&this.config.encodingType===
this.constants.EncrytionTypes.AES}},{key:"_isDESEncryptionType",value:function(){return C["default"]&&this.config.encodingType===this.constants.EncrytionTypes.DES}},{key:"_isRabbitEncryptionType",value:function(){return F["default"]&&this.config.encodingType===this.constants.EncrytionTypes.RABBIT}},{key:"_isRC4EncryptionType",value:function(){return A["default"]&&this.config.encodingType===this.constants.EncrytionTypes.RC4}},{key:"_isDataCompressionEnabled",value:function(){return this.config.isCompression}},
{key:"getEncryptionSecret",value:function(a){var b=this.getMetaData();(a=this.utils.getObjectFromKey(b.keys,a))&&(this._isAES||this._isDES||this._isRabbit||this._isRC4)&&("undefined"==typeof this.config.encryptionSecret?(this.utils.encryptionSecret=a.s,this.utils.encryptionSecret||(this.utils.encryptionSecret=this.utils.generateSecretKey(),this.setMetaData())):this.utils.encryptionSecret=this.config.encryptionSecret||a.s||"")}},{key:"get",value:function(a,b){var c="",e="",d=void 0,g=void 0,c=void 0;
if(!this.utils.is(a))return this.utils.warn(this.WarningEnum.KEY_NOT_PROVIDED),e;if(c=this.getDataFromLocalStorage(a),!c)return e;d=c;(this._isCompression||b)&&(d=t["default"].decompressFromUTF16(c));c=d;this._isBase64||b?c=r["default"].decode(d):(this.getEncryptionSecret(a),this._isAES?g=s["default"].decrypt(d.toString(),this.utils.encryptionSecret):this._isDES?g=C["default"].decrypt(d.toString(),this.utils.encryptionSecret):this._isRabbit?g=F["default"].decrypt(d.toString(),this.utils.encryptionSecret):
this._isRC4&&(g=A["default"].decrypt(d.toString(),this.utils.encryptionSecret)),g&&(c=g.toString(q["default"]._Utf8)));try{e=JSON.parse(c)}catch(f){throw Error("Could not parse JSON");}return e}},{key:"getDataFromLocalStorage",value:function(a){return this.ls.getItem(a,!0)}},{key:"getAllKeys",value:function(){var a=this.getMetaData();return this.utils.extractKeyNames(a)||[]}},{key:"set",value:function(a,b){var c="";return this.utils.is(a)?(this.getEncryptionSecret(a),String(a)!==String(this.utils.metaKey)&&
(this.utils.isKeyPresent(a)||(this.utils.addToKeysList(a),this.setMetaData())),c=this.processData(b),void this.setDataToLocalStorage(a,c)):void this.utils.warn(this.WarningEnum.KEY_NOT_PROVIDED)}},{key:"setDataToLocalStorage",value:function(a,b){this.ls.setItem(a,b)}},{key:"remove",value:function(a){return this.utils.is(a)?a===this.utils.metaKey&&this.getAllKeys().length?void this.utils.warn(this.WarningEnum.META_KEY_REMOVE):(this.utils.isKeyPresent(a)&&(this.utils.removeFromKeysList(a),this.setMetaData()),
void this.ls.removeItem(a)):void this.utils.warn(this.WarningEnum.KEY_NOT_PROVIDED)}},{key:"removeAll",value:function(){for(var a=void 0,b=void 0,a=this.getAllKeys(),b=0;b<a.length;b++)this.ls.removeItem(a[b]);this.ls.removeItem(this.utils.metaKey);this.resetAllKeys()}},{key:"clear",value:function(){this.ls.clear();this.resetAllKeys()}},{key:"resetAllKeys",value:function(){return this.utils.allKeys=[],[]}},{key:"processData",value:function(a,b){if(null===a||void 0===a||""===a)return"";var c=void 0,
e=void 0,d=void 0;try{c=JSON.stringify(a)}catch(g){throw Error("Could not stringify data.");}return e=c,this._isBase64||b?e=r["default"].encode(c):(this._isAES?e=s["default"].encrypt(c,this.utils.encryptionSecret):this._isDES?e=C["default"].encrypt(c,this.utils.encryptionSecret):this._isRabbit?e=F["default"].encrypt(c,this.utils.encryptionSecret):this._isRC4&&(e=A["default"].encrypt(c,this.utils.encryptionSecret)),e=e&&e.toString()),d=e,(this._isCompression||b)&&(d=t["default"].compressToUTF16(e)),
d}},{key:"setMetaData",value:function(){var a=this.processData({keys:this.utils.allKeys},!0);this.setDataToLocalStorage(this.getMetaKey(),a)}},{key:"getMetaData",value:function(){return this.get(this.getMetaKey(),!0)||{}}},{key:"getMetaKey",value:function(){return this.utils.metaKey+(this.config.encryptionNamespace?"__"+this.config.encryptionNamespace:"")}}];for(var f=L.prototype,B=0;B<c.length;B++){var K=c[B];K.enumerable=K.enumerable||!1;K.configurable=!0;"value"in K&&(K.writable=!0);Object.defineProperty(f,
K.key,K)}b["default"]=L;a.exports=b["default"]},function(a,b,c){function d(a){return a&&a.__esModule?a:{"default":a}}b=c(2);var f=d(b);b=c(3);var n=d(b);c=c(4);var l=d(c);a.exports={metaKey:"_secure__ls__metadata",encryptionSecret:"",secretPhrase:"s3cr3t$#@135^&*246",allKeys:[],is:function(a){return!!a},warn:function(a){a=a?a:f["default"].WarningEnum.DEFAULT_TEXT;console.warn(f["default"].WarningTypes[a])},generateSecretKey:function(){var a=n["default"].random(16);return(a=(0,l["default"])(this.secretPhrase,
a,{keySize:4}))&&a.toString()},getObjectFromKey:function(a,b){if(!a||!a.length)return{};for(var c=void 0,e={},c=0;c<a.length;c++)if(a[c].k===b){e=a[c];break}return e},extractKeyNames:function(a){return a&&a.keys&&a.keys.length?a.keys.map(function(a){return a.k}):[]},getAllKeys:function(){return this.allKeys},isKeyPresent:function(a){for(var b=!1,c=0;c<this.allKeys.length;c++)if(String(this.allKeys[c].k)===String(a)){b=!0;break}return b},addToKeysList:function(a){this.allKeys.push({k:a,s:this.encryptionSecret})},
removeFromKeysList:function(a){for(var b=void 0,c=-1,b=0;b<this.allKeys.length;b++)if(this.allKeys[b].k===a){c=b;break}return-1!==c&&this.allKeys.splice(c,1),c}}},function(a){var b={KEY_NOT_PROVIDED:"keyNotProvided",META_KEY_REMOVE:"metaKeyRemove",DEFAULT_TEXT:"defaultText"},c={};c[b.KEY_NOT_PROVIDED]="Secure LS: Key not provided. Aborting operation!";c[b.META_KEY_REMOVE]="Secure LS: Meta key can not be removed\nunless all keys created by Secure LS are removed!";c[b.DEFAULT_TEXT]="Unexpected output";
a.exports={WarningEnum:b,WarningTypes:c,EncrytionTypes:{BASE64:"base64",AES:"aes",DES:"des",RABBIT:"rabbit",RC4:"rc4"}}},function(a){a.exports={random:function(a){for(var b,c=[],e=function(a){var b=987654321;return function(){b=36969*(65535&b)+(b>>16)&4294967295;a=18E3*(65535&a)+(a>>16)&4294967295;var c=(b<<16)+a&4294967295;return c/=4294967296,c+=0.5,c*(0.5<Math.random()?1:-1)}},d=0;d<a;d+=4){var l=e(4294967296*(b||Math.random()));b=987654071*l();c.push(4294967296*l()|0)}return new this.Set(c,a)},
Set:function(a,b){a=this.words=a||[];void 0!==b?this.sigBytes=b:this.sigBytes=8*a.length}}},function(a,b,c){b=c(5);c(6);c(7);var d=b.lib;c=d.Base;var f=d.WordArray,d=b.algo,n=d.HMAC,l=d.PBKDF2=c.extend({cfg:c.extend({keySize:4,hasher:d.SHA1,iterations:1}),init:function(a){this.cfg=this.cfg.extend(a)},compute:function(a,b){for(var c=this.cfg,e=n.create(c.hasher,a),d=f.create(),g=f.create([1]),j=d.words,l=g.words,p=c.keySize,c=c.iterations;j.length<p;){var K=e.update(b).finalize(g);e.reset();for(var R=
K.words,D=R.length,J=K,M=1;M<c;M++){J=e.finalize(J);e.reset();for(var Q=J.words,E=0;E<D;E++)R[E]^=Q[E]}d.concat(K);l[0]++}return d.sigBytes=4*p,d}});b.PBKDF2=function(a,b,c){return l.create(c).compute(a,b)};a.exports=b.PBKDF2;!0},function(a){var b;if(!b){var c=Math;if(!(b=Object.create)){var d=function(){};b=function(a){var b;return d.prototype=a,b=new d,d.prototype=null,b}}var f=b;b={};var n=b.lib={},l=n.Base={extend:function(a){var b=f(this);return a&&b.mixIn(a),b.hasOwnProperty("init")&&this.init!==
b.init||(b.init=function(){b.$super.init.apply(this,arguments)}),b.init.prototype=b,b.$super=this,b},create:function(){var a=this.extend();return a.init.apply(a,arguments),a},init:function(){},mixIn:function(a){for(var b in a)a.hasOwnProperty(b)&&(this[b]=a[b]);a.hasOwnProperty("toString")&&(this.toString=a.toString)},clone:function(){return this.init.prototype.extend(this)}},q=n.WordArray=l.extend({init:function(a,b){a=this.words=a||[];void 0!=b?this.sigBytes=b:this.sigBytes=4*a.length},toString:function(a){return(a||
t).stringify(this)},concat:function(a){var b=this.words,c=a.words,e=this.sigBytes;a=a.sigBytes;if(this.clamp(),e%4)for(var d=0;d<a;d++)b[e+d>>>2]|=(c[d>>>2]>>>24-8*(d%4)&255)<<24-8*((e+d)%4);else for(d=0;d<a;d+=4)b[e+d>>>2]=c[d>>>2];return this.sigBytes+=a,this},clamp:function(){var a=this.words,b=this.sigBytes;a[b>>>2]&=4294967295<<32-8*(b%4);a.length=c.ceil(b/4)},clone:function(){var a=l.clone.call(this);return a.words=this.words.slice(0),a},random:function(a){for(var b,e=[],d=function(a){var b=
987654321;return function(){b=36969*(65535&b)+(b>>16)&4294967295;a=18E3*(65535&a)+(a>>16)&4294967295;var e=(b<<16)+a&4294967295;return e/=4294967296,e+=0.5,e*(0.5<c.random()?1:-1)}},g=0;g<a;g+=4){var f=d(4294967296*(b||c.random()));b=987654071*f();e.push(4294967296*f()|0)}return new q.init(e,a)}}),r=b.enc={},t=r.Hex={stringify:function(a){var b=a.words;a=a.sigBytes;for(var c=[],e=0;e<a;e++){var d=b[e>>>2]>>>24-8*(e%4)&255;c.push((d>>>4).toString(16));c.push((15&d).toString(16))}return c.join("")},
parse:function(a){for(var b=a.length,c=[],e=0;e<b;e+=2)c[e>>>3]|=parseInt(a.substr(e,2),16)<<24-4*(e%8);return new q.init(c,b/2)}},s=r.Latin1={stringify:function(a){var b=a.words;a=a.sigBytes;for(var c=[],e=0;e<a;e++)c.push(String.fromCharCode(b[e>>>2]>>>24-8*(e%4)&255));return c.join("")},parse:function(a){for(var b=a.length,c=[],e=0;e<b;e++)c[e>>>2]|=(255&a.charCodeAt(e))<<24-8*(e%4);return new q.init(c,b)}},C=r.Utf8={stringify:function(a){try{return decodeURIComponent(escape(s.stringify(a)))}catch(b){throw Error("Malformed UTF-8 data");
}},parse:function(a){return s.parse(unescape(encodeURIComponent(a)))}},F=n.BufferedBlockAlgorithm=l.extend({reset:function(){this._data=new q.init;this._nDataBytes=0},_append:function(a){"string"==typeof a&&(a=C.parse(a));this._data.concat(a);this._nDataBytes+=a.sigBytes},_process:function(a){var b=this._data,e=b.words,d=b.sigBytes,g=this.blockSize,f=d/(4*g),f=a?c.ceil(f):c.max((0|f)-this._minBufferSize,0);a=f*g;d=c.min(4*a,d);if(a){for(var l=0;l<a;l+=g)this._doProcessBlock(e,l);l=e.splice(0,a);b.sigBytes-=
d}return new q.init(l,d)},clone:function(){var a=l.clone.call(this);return a._data=this._data.clone(),a},_minBufferSize:0}),A=(n.Hasher=F.extend({cfg:l.extend(),init:function(a){this.cfg=this.cfg.extend(a);this.reset()},reset:function(){F.reset.call(this);this._doReset()},update:function(a){return this._append(a),this._process(),this},finalize:function(a){a&&this._append(a);return this._doFinalize()},blockSize:16,_createHelper:function(a){return function(b,c){return(new a.init(c)).finalize(b)}},_createHmacHelper:function(a){return function(b,
c){return(new A.HMAC.init(a,c)).finalize(b)}}}),b.algo={})}a.exports=b;!0},function(a,b,c){b=c(5);c=b.lib;var d=c.WordArray,f=c.Hasher,n=[];c=b.algo.SHA1=f.extend({_doReset:function(){this._hash=new d.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(a,b){for(var c=this._hash.words,e=c[0],d=c[1],g=c[2],f=c[3],j=c[4],p=0;80>p;p++){if(16>p)n[p]=0|a[b+p];else{var B=n[p-3]^n[p-8]^n[p-14]^n[p-16];n[p]=B<<1|B>>>31}B=(e<<5|e>>>27)+j+n[p];B+=20>p?(d&g|~d&f)+1518500249:
40>p?(d^g^f)+1859775393:60>p?(d&g|d&f|g&f)-1894007588:(d^g^f)-899497514;j=f;f=g;g=d<<30|d>>>2;d=e;e=B}c[0]=c[0]+e|0;c[1]=c[1]+d|0;c[2]=c[2]+g|0;c[3]=c[3]+f|0;c[4]=c[4]+j|0},_doFinalize:function(){var a=this._data,b=a.words,c=8*this._nDataBytes,e=8*a.sigBytes;return b[e>>>5]|=128<<24-e%32,b[(e+64>>>9<<4)+14]=Math.floor(c/4294967296),b[(e+64>>>9<<4)+15]=c,a.sigBytes=4*b.length,this._process(),this._hash},clone:function(){var a=f.clone.call(this);return a._hash=this._hash.clone(),a}});b.SHA1=f._createHelper(c);
b.HmacSHA1=f._createHmacHelper(c);a.exports=b.SHA1;!0},function(a,b,c){b=c(5);var d=b.enc.Utf8;b.algo.HMAC=b.lib.Base.extend({init:function(a,b){a=this._hasher=new a.init;"string"==typeof b&&(b=d.parse(b));var c=a.blockSize,e=4*c;b.sigBytes>e&&(b=a.finalize(b));b.clamp();for(var g=this._oKey=b.clone(),j=this._iKey=b.clone(),s=g.words,C=j.words,F=0;F<c;F++)s[F]^=1549556828,C[F]^=909522486;g.sigBytes=j.sigBytes=e;this.reset()},reset:function(){var a=this._hasher;a.reset();a.update(this._iKey)},update:function(a){return this._hasher.update(a),
this},finalize:function(a){var b=this._hasher;a=b.finalize(a);b.reset();return b.finalize(this._oKey.clone().concat(a))}});!0;a.exports=void 0;!0},function(a){var b={Latin1:{stringify:function(a){var b=a.words;a=a.sigBytes;for(var c=[],e=void 0,d=void 0,e=0;e<a;e++)d=b[e>>>2]>>>24-8*(e%4)&255,c.push(String.fromCharCode(d));return c.join("")}},_Utf8:{stringify:function(a){try{return decodeURIComponent(escape(b.Latin1.stringify(a)))}catch(c){throw Error("Malformed UTF-8 data");}}}};a.exports=b},function(a){var b=
{_keyStr:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",encode:function(a){var c="",e=void 0,d=void 0,l=void 0,q=void 0,r=e=void 0,t=void 0,s=0;for(a=b._utf8Encode(a);s<a.length;)e=a.charCodeAt(s++),d=a.charCodeAt(s++),l=a.charCodeAt(s++),q=e>>2,e=(3&e)<<4|d>>4,r=(15&d)<<2|l>>6,t=63&l,isNaN(d)?r=t=64:isNaN(l)&&(t=64),c=c+this._keyStr.charAt(q)+this._keyStr.charAt(e)+this._keyStr.charAt(r)+this._keyStr.charAt(t);return c},decode:function(a){var c="",e=void 0,d=void 0,l=void 0,
q=d=e=void 0,r=void 0,t=0;for(a=a.replace(/[^A-Za-z0-9\+\/\=]/g,"");t<a.length;)e=this._keyStr.indexOf(a.charAt(t++)),d=this._keyStr.indexOf(a.charAt(t++)),q=this._keyStr.indexOf(a.charAt(t++)),r=this._keyStr.indexOf(a.charAt(t++)),e=e<<2|d>>4,d=(15&d)<<4|q>>2,l=(3&q)<<6|r,c+=String.fromCharCode(e),64!==q&&(c+=String.fromCharCode(d)),64!==r&&(c+=String.fromCharCode(l));return b._utf8Decode(c)},_utf8Encode:function(a){a=a.replace(/\r\n/g,"\n");for(var b="",c=0;c<a.length;c++){var e=a.charCodeAt(c);
128>e?b+=String.fromCharCode(e):127<e&&2048>e?(b+=String.fromCharCode(e>>6|192),b+=String.fromCharCode(63&e|128)):(b+=String.fromCharCode(e>>12|224),b+=String.fromCharCode(e>>6&63|128),b+=String.fromCharCode(63&e|128))}return b},_utf8Decode:function(a){for(var b="",c=0,e=void 0,d=void 0,g=void 0,d=0;c<a.length;)e=a.charCodeAt(c),128>e?(b+=String.fromCharCode(e),c++):191<e&&224>e?(d=a.charCodeAt(c+1),b+=String.fromCharCode((31&e)<<6|63&d),c+=2):(d=a.charCodeAt(c+1),g=a.charCodeAt(c+2),b+=String.fromCharCode((15&
e)<<12|(63&d)<<6|63&g),c+=3);return b}};a.exports=b},function(a,b,c){var d,f=function(a,b){if(!l[a]){l[a]={};for(var c=0;c<a.length;c++)l[a][a.charAt(c)]=c}return l[a][b]},n=String.fromCharCode,l={},q={compressToBase64:function(a){if(null==a)return"";a=q._compress(a,6,function(a){return"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(a)});switch(a.length%4){default:case 0:return a;case 1:return a+"===";case 2:return a+"==";case 3:return a+"="}},decompressFromBase64:function(a){return null==
a?"":""==a?null:q._decompress(a.length,32,function(b){return f("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",a.charAt(b))})},compressToUTF16:function(a){return null==a?"":q._compress(a,15,function(a){return n(a+32)})+" "},decompressFromUTF16:function(a){return null==a?"":""==a?null:q._decompress(a.length,16384,function(b){return a.charCodeAt(b)-32})},compressToUint8Array:function(a){a=q.compress(a);for(var b=new Uint8Array(2*a.length),c=0,e=a.length;c<e;c++){var d=a.charCodeAt(c);
b[2*c]=d>>>8;b[2*c+1]=d%256}return b},decompressFromUint8Array:function(a){if(null===a||void 0===a)return q.decompress(a);for(var b=Array(a.length/2),c=0,e=b.length;c<e;c++)b[c]=256*a[2*c]+a[2*c+1];var d=[];return b.forEach(function(a){d.push(n(a))}),q.decompress(d.join(""))},compressToEncodedURIComponent:function(a){return null==a?"":q._compress(a,6,function(a){return"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$".charAt(a)})},decompressFromEncodedURIComponent:function(a){return null==
a?"":""==a?null:(a=a.replace(/ /g,"+"),q._decompress(a.length,32,function(b){return f("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$",a.charAt(b))}))},compress:function(a){return q._compress(a,16,function(a){return n(a)})},_compress:function(a,b,c){if(null==a)return"";var e,d,g,f={},j={},l="",p="",n="",q=2,M=3,Q=2,E=[],y=0,G=0;for(g=0;g<a.length;g+=1)if(l=a.charAt(g),Object.prototype.hasOwnProperty.call(f,l)||(f[l]=M++,j[l]=!0),p=n+l,Object.prototype.hasOwnProperty.call(f,p))n=
p;else{if(Object.prototype.hasOwnProperty.call(j,n)){if(256>n.charCodeAt(0)){for(e=0;e<Q;e++)y<<=1,G==b-1?(G=0,E.push(c(y)),y=0):G++;d=n.charCodeAt(0);for(e=0;8>e;e++)y=y<<1|1&d,G==b-1?(G=0,E.push(c(y)),y=0):G++,d>>=1}else{d=1;for(e=0;e<Q;e++)y=y<<1|d,G==b-1?(G=0,E.push(c(y)),y=0):G++,d=0;d=n.charCodeAt(0);for(e=0;16>e;e++)y=y<<1|1&d,G==b-1?(G=0,E.push(c(y)),y=0):G++,d>>=1}q--;0==q&&(q=Math.pow(2,Q),Q++);delete j[n]}else{d=f[n];for(e=0;e<Q;e++)y=y<<1|1&d,G==b-1?(G=0,E.push(c(y)),y=0):G++,d>>=1}q--;
0==q&&(q=Math.pow(2,Q),Q++);f[p]=M++;n=String(l)}if(""!==n){if(Object.prototype.hasOwnProperty.call(j,n)){if(256>n.charCodeAt(0)){for(e=0;e<Q;e++)y<<=1,G==b-1?(G=0,E.push(c(y)),y=0):G++;d=n.charCodeAt(0);for(e=0;8>e;e++)y=y<<1|1&d,G==b-1?(G=0,E.push(c(y)),y=0):G++,d>>=1}else{d=1;for(e=0;e<Q;e++)y=y<<1|d,G==b-1?(G=0,E.push(c(y)),y=0):G++,d=0;d=n.charCodeAt(0);for(e=0;16>e;e++)y=y<<1|1&d,G==b-1?(G=0,E.push(c(y)),y=0):G++,d>>=1}q--;0==q&&(q=Math.pow(2,Q),Q++);delete j[n]}else{d=f[n];for(e=0;e<Q;e++)y=
y<<1|1&d,G==b-1?(G=0,E.push(c(y)),y=0):G++,d>>=1}q--;0==q&&(Math.pow(2,Q),Q++)}d=2;for(e=0;e<Q;e++)y=y<<1|1&d,G==b-1?(G=0,E.push(c(y)),y=0):G++,d>>=1;for(;;){if(y<<=1,G==b-1){E.push(c(y));break}G++}return E.join("")},decompress:function(a){return null==a?"":""==a?null:q._decompress(a.length,32768,function(b){return a.charCodeAt(b)})},_decompress:function(a,b,c){var e,d,g,f,j,l,p=[],q=4,J=4,M=3;d="";var Q=[],E=c(0),y=b,G=1;for(e=0;3>e;e+=1)p[e]=e;d=0;f=Math.pow(2,2);for(j=1;j!=f;)g=E&y,y>>=1,0==y&&
(y=b,E=c(G++)),d|=(0<g?1:0)*j,j<<=1;switch(d){case 0:d=0;f=Math.pow(2,8);for(j=1;j!=f;)g=E&y,y>>=1,0==y&&(y=b,E=c(G++)),d|=(0<g?1:0)*j,j<<=1;l=n(d);break;case 1:d=0;f=Math.pow(2,16);for(j=1;j!=f;)g=E&y,y>>=1,0==y&&(y=b,E=c(G++)),d|=(0<g?1:0)*j,j<<=1;l=n(d);break;case 2:return""}e=p[3]=l;for(Q.push(l);;){if(G>a)return"";d=0;f=Math.pow(2,M);for(j=1;j!=f;)g=E&y,y>>=1,0==y&&(y=b,E=c(G++)),d|=(0<g?1:0)*j,j<<=1;switch(l=d){case 0:d=0;f=Math.pow(2,8);for(j=1;j!=f;)g=E&y,y>>=1,0==y&&(y=b,E=c(G++)),d|=(0<
g?1:0)*j,j<<=1;p[J++]=n(d);l=J-1;q--;break;case 1:d=0;f=Math.pow(2,16);for(j=1;j!=f;)g=E&y,y>>=1,0==y&&(y=b,E=c(G++)),d|=(0<g?1:0)*j,j<<=1;p[J++]=n(d);l=J-1;q--;break;case 2:return Q.join("")}if(0==q&&(q=Math.pow(2,M),M++),p[l])d=p[l];else{if(l!==J)return null;d=e+e.charAt(0)}Q.push(d);p[J++]=e+d.charAt(0);q--;e=d;0==q&&(q=Math.pow(2,M),M++)}}};d=q;b=function(){return d}.call(b,c,b,a);!(void 0!==b&&(a.exports=b))},function(a,b,c){b=c(5);c(12);c(13);c(14);c(15);c=b.lib.BlockCipher;for(var d=b.algo,
f=[],n=[],l=[],q=[],r=[],t=[],s=[],C=[],F=[],A=[],L=[],B=0;256>B;B++)128>B?L[B]=B<<1:L[B]=B<<1^283;for(var K=0,R=0,B=0;256>B;B++){var D=R^R<<1^R<<2^R<<3^R<<4,D=D>>>8^255&D^99;f[K]=D;n[D]=K;var J=L[K],M=L[J],Q=L[M],E=257*L[D]^16843008*D;l[K]=E<<24|E>>>8;q[K]=E<<16|E>>>16;r[K]=E<<8|E>>>24;t[K]=E;E=16843009*Q^65537*M^257*J^16843008*K;s[D]=E<<24|E>>>8;C[D]=E<<16|E>>>16;F[D]=E<<8|E>>>24;A[D]=E;K?(K=J^L[L[L[Q^J]]],R^=L[L[R]]):K=R=1}!0;var y=[0,1,2,4,8,16,32,64,128,27,54],d=d.AES=c.extend({_doReset:function(){if(!this._nRounds||
this._keyPriorReset!==this._key){for(var a=this._keyPriorReset=this._key,b=a.words,c=a.sigBytes/4,a=4*((this._nRounds=c+6)+1),e=this._keySchedule=[],d=0;d<a;d++)if(d<c)e[d]=b[d];else{var g=e[d-1];d%c?6<c&&4==d%c&&(g=f[g>>>24]<<24|f[g>>>16&255]<<16|f[g>>>8&255]<<8|f[255&g]):(g=g<<8|g>>>24,g=f[g>>>24]<<24|f[g>>>16&255]<<16|f[g>>>8&255]<<8|f[255&g],g^=y[d/c|0]<<24);e[d]=e[d-c]^g}b=this._invKeySchedule=[];for(c=0;c<a;c++)d=a-c,g=c%4?e[d]:e[d-4],4>c||4>=d?b[c]=g:b[c]=s[f[g>>>24]]^C[f[g>>>16&255]]^F[f[g>>>
8&255]]^A[f[255&g]]}},encryptBlock:function(a,b){this._doCryptBlock(a,b,this._keySchedule,l,q,r,t,f)},decryptBlock:function(a,b){var c=a[b+1];a[b+1]=a[b+3];a[b+3]=c;this._doCryptBlock(a,b,this._invKeySchedule,s,C,F,A,n);c=a[b+1];a[b+1]=a[b+3];a[b+3]=c},_doCryptBlock:function(a,b,c,e,d,g,f,j){for(var l=this._nRounds,p=a[b]^c[0],n=a[b+1]^c[1],q=a[b+2]^c[2],r=a[b+3]^c[3],s=4,t=1;t<l;t++)var B=e[p>>>24]^d[n>>>16&255]^g[q>>>8&255]^f[255&r]^c[s++],M=e[n>>>24]^d[q>>>16&255]^g[r>>>8&255]^f[255&p]^c[s++],
A=e[q>>>24]^d[r>>>16&255]^g[p>>>8&255]^f[255&n]^c[s++],r=e[r>>>24]^d[p>>>16&255]^g[n>>>8&255]^f[255&q]^c[s++],p=B,n=M,q=A;B=(j[p>>>24]<<24|j[n>>>16&255]<<16|j[q>>>8&255]<<8|j[255&r])^c[s++];M=(j[n>>>24]<<24|j[q>>>16&255]<<16|j[r>>>8&255]<<8|j[255&p])^c[s++];A=(j[q>>>24]<<24|j[r>>>16&255]<<16|j[p>>>8&255]<<8|j[255&n])^c[s++];r=(j[r>>>24]<<24|j[p>>>16&255]<<16|j[n>>>8&255]<<8|j[255&q])^c[s++];a[b]=B;a[b+1]=M;a[b+2]=A;a[b+3]=r},keySize:8});b.AES=c._createHelper(d);a.exports=b.AES;!0},function(a,b,c){b=
c(5);var d=b.lib.WordArray;b.enc.Base64={stringify:function(a){var b=a.words,c=a.sigBytes,e=this._map;a.clamp();a=[];for(var d=0;d<c;d+=3)for(var g=(b[d>>>2]>>>24-8*(d%4)&255)<<16|(b[d+1>>>2]>>>24-8*((d+1)%4)&255)<<8|b[d+2>>>2]>>>24-8*((d+2)%4)&255,j=0;4>j&&d+0.75*j<c;j++)a.push(e.charAt(g>>>6*(3-j)&63));if(b=e.charAt(64))for(;a.length%4;)a.push(b);return a.join("")},parse:function(a){var b=a.length,c=this._map,e=this._reverseMap;if(!e)for(var e=this._reverseMap=[],g=0;g<c.length;g++)e[c.charCodeAt(g)]=
g;if(c=c.charAt(64))c=a.indexOf(c),-1!==c&&(b=c);for(var c=[],j=g=0;j<b;j++)if(j%4){var s=e[a.charCodeAt(j-1)]<<2*(j%4),C=e[a.charCodeAt(j)]>>>6-2*(j%4);c[g>>>2]|=(s|C)<<24-8*(g%4);g++}return d.create(c,g)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="};a.exports=b.enc.Base64;!0},function(a,b,c){b=c(5);var d=Math,f=function(a,b,c,e,d,g,f){a=a+(b&c|~b&e)+d+f;return(a<<g|a>>>32-g)+b},n=function(a,b,c,e,d,g,f){a=a+(b&e|c&~e)+d+f;return(a<<g|a>>>32-g)+b},l=function(a,b,c,e,
d,g,f){a=a+(b^c^e)+d+f;return(a<<g|a>>>32-g)+b},q=function(a,b,c,e,d,g,f){a=a+(c^(b|~e))+d+f;return(a<<g|a>>>32-g)+b};c=b.lib;var r=c.WordArray,t=c.Hasher;c=b.algo;for(var s=[],C=0;64>C;C++)s[C]=4294967296*d.abs(d.sin(C+1))|0;!0;c=c.MD5=t.extend({_doReset:function(){this._hash=new r.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(a,b){for(var c=0;16>c;c++){var e=b+c,d=a[e];a[e]=16711935&(d<<8|d>>>24)|4278255360&(d<<24|d>>>8)}var c=this._hash.words,e=a[b+0],d=a[b+1],g=
a[b+2],j=a[b+3],p=a[b+4],r=a[b+5],t=a[b+6],E=a[b+7],y=a[b+8],C=a[b+9],Aa=a[b+10],ka=a[b+11],Ba=a[b+12],Y=a[b+13],ia=a[b+14],U=a[b+15],v=c[0],u=c[1],z=c[2],x=c[3],v=f(v,u,z,x,e,7,s[0]),x=f(x,v,u,z,d,12,s[1]),z=f(z,x,v,u,g,17,s[2]),u=f(u,z,x,v,j,22,s[3]),v=f(v,u,z,x,p,7,s[4]),x=f(x,v,u,z,r,12,s[5]),z=f(z,x,v,u,t,17,s[6]),u=f(u,z,x,v,E,22,s[7]),v=f(v,u,z,x,y,7,s[8]),x=f(x,v,u,z,C,12,s[9]),z=f(z,x,v,u,Aa,17,s[10]),u=f(u,z,x,v,ka,22,s[11]),v=f(v,u,z,x,Ba,7,s[12]),x=f(x,v,u,z,Y,12,s[13]),z=f(z,x,v,u,ia,
17,s[14]),u=f(u,z,x,v,U,22,s[15]),v=n(v,u,z,x,d,5,s[16]),x=n(x,v,u,z,t,9,s[17]),z=n(z,x,v,u,ka,14,s[18]),u=n(u,z,x,v,e,20,s[19]),v=n(v,u,z,x,r,5,s[20]),x=n(x,v,u,z,Aa,9,s[21]),z=n(z,x,v,u,U,14,s[22]),u=n(u,z,x,v,p,20,s[23]),v=n(v,u,z,x,C,5,s[24]),x=n(x,v,u,z,ia,9,s[25]),z=n(z,x,v,u,j,14,s[26]),u=n(u,z,x,v,y,20,s[27]),v=n(v,u,z,x,Y,5,s[28]),x=n(x,v,u,z,g,9,s[29]),z=n(z,x,v,u,E,14,s[30]),u=n(u,z,x,v,Ba,20,s[31]),v=l(v,u,z,x,r,4,s[32]),x=l(x,v,u,z,y,11,s[33]),z=l(z,x,v,u,ka,16,s[34]),u=l(u,z,x,v,ia,
23,s[35]),v=l(v,u,z,x,d,4,s[36]),x=l(x,v,u,z,p,11,s[37]),z=l(z,x,v,u,E,16,s[38]),u=l(u,z,x,v,Aa,23,s[39]),v=l(v,u,z,x,Y,4,s[40]),x=l(x,v,u,z,e,11,s[41]),z=l(z,x,v,u,j,16,s[42]),u=l(u,z,x,v,t,23,s[43]),v=l(v,u,z,x,C,4,s[44]),x=l(x,v,u,z,Ba,11,s[45]),z=l(z,x,v,u,U,16,s[46]),u=l(u,z,x,v,g,23,s[47]),v=q(v,u,z,x,e,6,s[48]),x=q(x,v,u,z,E,10,s[49]),z=q(z,x,v,u,ia,15,s[50]),u=q(u,z,x,v,r,21,s[51]),v=q(v,u,z,x,Ba,6,s[52]),x=q(x,v,u,z,j,10,s[53]),z=q(z,x,v,u,Aa,15,s[54]),u=q(u,z,x,v,d,21,s[55]),v=q(v,u,z,x,
y,6,s[56]),x=q(x,v,u,z,U,10,s[57]),z=q(z,x,v,u,t,15,s[58]),u=q(u,z,x,v,Y,21,s[59]),v=q(v,u,z,x,p,6,s[60]),x=q(x,v,u,z,ka,10,s[61]),z=q(z,x,v,u,g,15,s[62]),u=q(u,z,x,v,C,21,s[63]);c[0]=c[0]+v|0;c[1]=c[1]+u|0;c[2]=c[2]+z|0;c[3]=c[3]+x|0},_doFinalize:function(){var a=this._data,b=a.words,c=8*this._nDataBytes,e=8*a.sigBytes;b[e>>>5]|=128<<24-e%32;var g=d.floor(c/4294967296);b[(e+64>>>9<<4)+15]=16711935&(g<<8|g>>>24)|4278255360&(g<<24|g>>>8);b[(e+64>>>9<<4)+14]=16711935&(c<<8|c>>>24)|4278255360&(c<<24|
c>>>8);a.sigBytes=4*(b.length+1);this._process();a=this._hash;b=a.words;for(c=0;4>c;c++)e=b[c],b[c]=16711935&(e<<8|e>>>24)|4278255360&(e<<24|e>>>8);return a},clone:function(){var a=t.clone.call(this);return a._hash=this._hash.clone(),a}});b.MD5=t._createHelper(c);b.HmacMD5=t._createHmacHelper(c);a.exports=b.MD5;!0},function(a,b,c){b=c(5);c(6);c(7);var d=b.lib;c=d.Base;var f=d.WordArray,d=b.algo,n=d.EvpKDF=c.extend({cfg:c.extend({keySize:4,hasher:d.MD5,iterations:1}),init:function(a){this.cfg=this.cfg.extend(a)},
compute:function(a,b){for(var c=this.cfg,e=c.hasher.create(),d=f.create(),g=d.words,j=c.keySize,c=c.iterations;g.length<j;){p&&e.update(p);var p=e.update(a).finalize(b);e.reset();for(var n=1;n<c;n++)p=e.finalize(p),e.reset();d.concat(p)}return d.sigBytes=4*j,d}});b.EvpKDF=function(a,b,c){return n.create(c).compute(a,b)};a.exports=b.EvpKDF;!0},function(a,b,c){c=c(5);if(!c.lib.Cipher){b=c.lib;var d=b.Base,f=b.WordArray,n=b.BufferedBlockAlgorithm,l=c.enc,q=(l.Utf8,l.Base64),r=c.algo.EvpKDF,t=b.Cipher=
n.extend({cfg:d.extend(),createEncryptor:function(a,b){return this.create(this._ENC_XFORM_MODE,a,b)},createDecryptor:function(a,b){return this.create(this._DEC_XFORM_MODE,a,b)},init:function(a,b,c){this.cfg=this.cfg.extend(c);this._xformMode=a;this._key=b;this.reset()},reset:function(){n.reset.call(this);this._doReset()},process:function(a){return this._append(a),this._process()},finalize:function(a){a&&this._append(a);return this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,
_createHelper:function(a){return{encrypt:function(b,c,e){return("string"==typeof c?L:A).encrypt(a,b,c,e)},decrypt:function(b,c,e){return("string"==typeof c?L:A).decrypt(a,b,c,e)}}}}),l=(b.StreamCipher=t.extend({_doFinalize:function(){return this._process(!0)},blockSize:1}),c.mode={}),s,C=function(a,b,c){var e=this._iv;e?this._iv=void 0:e=this._prevBlock;for(var d=0;d<c;d++)a[b+d]^=e[d]};s=(b.BlockCipherMode=d.extend({createEncryptor:function(a,b){return this.Encryptor.create(a,b)},createDecryptor:function(a,
b){return this.Decryptor.create(a,b)},init:function(a,b){this._cipher=a;this._iv=b}})).extend();s=(s.Encryptor=s.extend({processBlock:function(a,b){var c=this._cipher,e=c.blockSize;C.call(this,a,b,e);c.encryptBlock(a,b);this._prevBlock=a.slice(b,b+e)}}),s.Decryptor=s.extend({processBlock:function(a,b){var c=this._cipher,e=c.blockSize,d=a.slice(b,b+e);c.decryptBlock(a,b);C.call(this,a,b,e);this._prevBlock=d}}),s);l=l.CBC=s;s=(c.pad={}).Pkcs7={pad:function(a,b){for(var c=4*b,c=c-a.sigBytes%c,e=c<<24|
c<<16|c<<8|c,d=[],g=0;g<c;g+=4)d.push(e);c=f.create(d,c);a.concat(c)},unpad:function(a){a.sigBytes-=255&a.words[a.sigBytes-1>>>2]}};var F=(b.BlockCipher=t.extend({cfg:t.cfg.extend({mode:l,padding:s}),reset:function(){t.reset.call(this);var a=this.cfg,b=a.iv,a=a.mode;if(this._xformMode==this._ENC_XFORM_MODE)var c=a.createEncryptor;else c=a.createDecryptor,this._minBufferSize=1;this._mode=c.call(a,this,b&&b.words)},_doProcessBlock:function(a,b){this._mode.processBlock(a,b)},_doFinalize:function(){var a=
this.cfg.padding;if(this._xformMode==this._ENC_XFORM_MODE){a.pad(this._data,this.blockSize);var b=this._process(!0)}else b=this._process(!0),a.unpad(b);return b},blockSize:4}),b.CipherParams=d.extend({init:function(a){this.mixIn(a)},toString:function(a){return(a||this.formatter).stringify(this)}})),l=(c.format={}).OpenSSL={stringify:function(a){var b=a.ciphertext;a=a.salt;return(a?f.create([1398893684,1701076831]).concat(a).concat(b):b).toString(q)},parse:function(a){a=q.parse(a);var b=a.words;if(1398893684==
b[0]&&1701076831==b[1]){var c=f.create(b.slice(2,4));b.splice(0,4);a.sigBytes-=16}return F.create({ciphertext:a,salt:c})}},A=b.SerializableCipher=d.extend({cfg:d.extend({format:l}),encrypt:function(a,b,c,e){e=this.cfg.extend(e);var d=a.createEncryptor(c,e);b=d.finalize(b);d=d.cfg;return F.create({ciphertext:b,key:c,iv:d.iv,algorithm:a,mode:d.mode,padding:d.padding,blockSize:a.blockSize,formatter:e.format})},decrypt:function(a,b,c,e){e=this.cfg.extend(e);b=this._parse(b,e.format);return a.createDecryptor(c,
e).finalize(b.ciphertext)},_parse:function(a,b){return"string"==typeof a?b.parse(a,this):a}});c=(c.kdf={}).OpenSSL={execute:function(a,b,c,e){e||(e=f.random(8));a=r.create({keySize:b+c}).compute(a,e);c=f.create(a.words.slice(b),4*c);return a.sigBytes=4*b,F.create({key:a,iv:c,salt:e})}};var L=b.PasswordBasedCipher=A.extend({cfg:A.cfg.extend({kdf:c}),encrypt:function(a,b,c,e){e=this.cfg.extend(e);c=e.kdf.execute(c,a.keySize,a.ivSize);e.iv=c.iv;a=A.encrypt.call(this,a,b,c.key,e);return a.mixIn(c),a},
decrypt:function(a,b,c,e){e=this.cfg.extend(e);b=this._parse(b,e.format);c=e.kdf.execute(c,a.keySize,a.ivSize,b.salt);e.iv=c.iv;return A.decrypt.call(this,a,b,c.key,e)}})}a.exports=void 0;!0},function(a,b,c){b=c(5);c(12);c(13);c(14);c(15);var d=function(a,b){var c=(this._lBlock>>>a^this._rBlock)&b;this._rBlock^=c;this._lBlock^=c<<a},f=function(a,b){var c=(this._rBlock>>>a^this._lBlock)&b;this._lBlock^=c;this._rBlock^=c<<a};c=b.lib;var n=c.WordArray;c=c.BlockCipher;var l=b.algo,q=[57,49,41,33,25,17,
9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],r=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],t=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],s=[{"0":8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,
2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,
2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{"0":1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,
117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,
335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},
{"0":260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,
18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{"0":2151682048,65536:2147487808,
131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,
1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{"0":128,4096:17039360,8192:262144,
12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,
90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{"0":268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,
2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,
4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{"0":1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,
184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{"0":134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,
9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,
29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],C=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],F=l.DES=c.extend({_doReset:function(){for(var a=this._key.words,b=[],c=0;56>c;c++){var e=q[c]-1;b[c]=
a[e>>>5]>>>31-e%32&1}a=this._subKeys=[];for(e=0;16>e;e++){for(var d=a[e]=[],g=t[e],c=0;24>c;c++)d[c/6|0]|=b[(r[c]-1+g)%28]<<31-c%6,d[4+(c/6|0)]|=b[28+(r[c+24]-1+g)%28]<<31-c%6;d[0]=d[0]<<1|d[0]>>>31;for(c=1;7>c;c++)d[c]>>>=4*(c-1)+3;d[7]=d[7]<<5|d[7]>>>27}b=this._invSubKeys=[];for(c=0;16>c;c++)b[c]=a[15-c]},encryptBlock:function(a,b){this._doCryptBlock(a,b,this._subKeys)},decryptBlock:function(a,b){this._doCryptBlock(a,b,this._invSubKeys)},_doCryptBlock:function(a,b,c){this._lBlock=a[b];this._rBlock=
a[b+1];d.call(this,4,252645135);d.call(this,16,65535);f.call(this,2,858993459);f.call(this,8,16711935);d.call(this,1,1431655765);for(var e=0;16>e;e++){for(var g=c[e],j=this._lBlock,l=this._rBlock,n=0,q=0;8>q;q++)n|=s[q][((l^g[q])&C[q])>>>0];this._lBlock=l;this._rBlock=j^n}c=this._lBlock;this._lBlock=this._rBlock;this._rBlock=c;d.call(this,1,1431655765);f.call(this,8,16711935);f.call(this,2,858993459);d.call(this,16,65535);d.call(this,4,252645135);a[b]=this._lBlock;a[b+1]=this._rBlock},keySize:2,ivSize:2,
blockSize:2});b.DES=c._createHelper(F);l=l.TripleDES=c.extend({_doReset:function(){var a=this._key.words;this._des1=F.createEncryptor(n.create(a.slice(0,2)));this._des2=F.createEncryptor(n.create(a.slice(2,4)));this._des3=F.createEncryptor(n.create(a.slice(4,6)))},encryptBlock:function(a,b){this._des1.encryptBlock(a,b);this._des2.decryptBlock(a,b);this._des3.encryptBlock(a,b)},decryptBlock:function(a,b){this._des3.decryptBlock(a,b);this._des2.encryptBlock(a,b);this._des1.decryptBlock(a,b)},keySize:6,
ivSize:2,blockSize:2});b.TripleDES=c._createHelper(l);a.exports=b.TripleDES;!0},function(a,b,c){b=c(5);c(12);c(13);c(14);c(15);var d=function(){for(var a=this._X,b=this._C,c=0;8>c;c++)n[c]=b[c];b[0]=b[0]+1295307597+this._b|0;b[1]=b[1]+3545052371+(b[0]>>>0<n[0]>>>0?1:0)|0;b[2]=b[2]+886263092+(b[1]>>>0<n[1]>>>0?1:0)|0;b[3]=b[3]+1295307597+(b[2]>>>0<n[2]>>>0?1:0)|0;b[4]=b[4]+3545052371+(b[3]>>>0<n[3]>>>0?1:0)|0;b[5]=b[5]+886263092+(b[4]>>>0<n[4]>>>0?1:0)|0;b[6]=b[6]+1295307597+(b[5]>>>0<n[5]>>>0?1:0)|
0;b[7]=b[7]+3545052371+(b[6]>>>0<n[6]>>>0?1:0)|0;this._b=b[7]>>>0<n[7]>>>0?1:0;for(c=0;8>c;c++){var e=a[c]+b[c],d=65535&e,g=e>>>16;l[c]=((d*d>>>17)+d*g>>>15)+g*g^((4294901760&e)*e|0)+((65535&e)*e|0)}a[0]=l[0]+(l[7]<<16|l[7]>>>16)+(l[6]<<16|l[6]>>>16)|0;a[1]=l[1]+(l[0]<<8|l[0]>>>24)+l[7]|0;a[2]=l[2]+(l[1]<<16|l[1]>>>16)+(l[0]<<16|l[0]>>>16)|0;a[3]=l[3]+(l[2]<<8|l[2]>>>24)+l[1]|0;a[4]=l[4]+(l[3]<<16|l[3]>>>16)+(l[2]<<16|l[2]>>>16)|0;a[5]=l[5]+(l[4]<<8|l[4]>>>24)+l[3]|0;a[6]=l[6]+(l[5]<<16|l[5]>>>16)+
(l[4]<<16|l[4]>>>16)|0;a[7]=l[7]+(l[6]<<8|l[6]>>>24)+l[5]|0};c=b.lib.StreamCipher;var f=[],n=[],l=[],q=b.algo.Rabbit=c.extend({_doReset:function(){for(var a=this._key.words,b=this.cfg.iv,c=0;4>c;c++)a[c]=16711935&(a[c]<<8|a[c]>>>24)|4278255360&(a[c]<<24|a[c]>>>8);for(var e=this._X=[a[0],a[3]<<16|a[2]>>>16,a[1],a[0]<<16|a[3]>>>16,a[2],a[1]<<16|a[0]>>>16,a[3],a[2]<<16|a[1]>>>16],a=this._C=[a[2]<<16|a[2]>>>16,4294901760&a[0]|65535&a[1],a[3]<<16|a[3]>>>16,4294901760&a[1]|65535&a[2],a[0]<<16|a[0]>>>16,
4294901760&a[2]|65535&a[3],a[1]<<16|a[1]>>>16,4294901760&a[3]|65535&a[0]],c=this._b=0;4>c;c++)d.call(this);for(c=0;8>c;c++)a[c]^=e[c+4&7];if(b){var c=b.words,b=c[0],c=c[1],b=16711935&(b<<8|b>>>24)|4278255360&(b<<24|b>>>8),c=16711935&(c<<8|c>>>24)|4278255360&(c<<24|c>>>8),e=b>>>16|4294901760&c,g=c<<16|65535&b;a[0]^=b;a[1]^=e;a[2]^=c;a[3]^=g;a[4]^=b;a[5]^=e;a[6]^=c;a[7]^=g;for(c=0;4>c;c++)d.call(this)}},_doProcessBlock:function(a,b){var c=this._X;d.call(this);f[0]=c[0]^c[5]>>>16^c[3]<<16;f[1]=c[2]^
c[7]>>>16^c[5]<<16;f[2]=c[4]^c[1]>>>16^c[7]<<16;f[3]=c[6]^c[3]>>>16^c[1]<<16;for(c=0;4>c;c++)f[c]=16711935&(f[c]<<8|f[c]>>>24)|4278255360&(f[c]<<24|f[c]>>>8),a[b+c]^=f[c]},blockSize:4,ivSize:2});b.Rabbit=c._createHelper(q);a.exports=b.Rabbit;!0},function(a,b,c){b=c(5);c(12);c(13);c(14);c(15);var d=function(){for(var a=this._S,b=this._i,c=this._j,e=0,d=0;4>d;d++){var b=(b+1)%256,c=(c+a[b])%256,g=a[b];a[b]=a[c];a[c]=g;e|=a[(a[b]+a[c])%256]<<24-8*d}return this._i=b,this._j=c,e};c=b.lib.StreamCipher;
var f=b.algo,n=f.RC4=c.extend({_doReset:function(){for(var a=this._key,b=a.words,a=a.sigBytes,c=this._S=[],e=0;256>e;e++)c[e]=e;for(var d=e=0;256>e;e++){var g=e%a,d=(d+c[e]+(b[g>>>2]>>>24-8*(g%4)&255))%256,g=c[e];c[e]=c[d];c[d]=g}this._i=this._j=0},_doProcessBlock:function(a,b){a[b]^=d.call(this)},keySize:8,ivSize:0});b.RC4=c._createHelper(n);f=f.RC4Drop=n.extend({cfg:n.cfg.extend({drop:192}),_doReset:function(){n._doReset.call(this);for(var a=this.cfg.drop;0<a;a--)d.call(this)}});b.RC4Drop=c._createHelper(f);
a.exports=b.RC4;!0}],c=function(e){if(d[e])return d[e].exports;var g=d[e]={exports:{},id:e,loaded:!1};return a[e].call(g.exports,g,g.exports,c),g.loaded=!0,g.exports},d={};return c.m=a,c.c=d,c.p="",c(0)};"object"==typeof exports&&"object"==typeof module?module.exports=a():"function"==typeof define&&define.amd?define("SecureLS",[],a):"object"==typeof exports?exports.SecureLS=a():this.SecureLS=a();!0})();null!==SecureLS&&"undefined"!==typeof SecureLS&&(ig.global.SecureLS=SecureLS,ig.SecureLS=SecureLS)});
ig.baked=!0;
ig.module("plugins.io.storage-manager").requires("impact.game","plugins.io.io-manager","plugins.secure-ls").defines(function(){ig.secure=new SecureLS({encodingType:"aes"});ig.Game.prototype.name="MJS-Game";ig.Game.prototype.version="1.0.0";ig.Game.prototype.sessionData={};ig.Game.prototype.hash=function(a){var b=0,c,d;if(0===a.length)return b;for(c=0;c<a.length;c++)d=a.charCodeAt(c),b=(b<<5)-b+d,b|=0;return b.toString(36)};ig.Game.prototype.initData=function(){return this.sessionData={sfx:1,bgm:1,
level:1,score:0}};ig.Game.prototype.setupStorageManager=function(){"undefined"!==typeof this.name&&"undefined"!==typeof this.version&&(this.io||(this.io=new IoManager),this.storage=this.io.storage,this.storageName=this.hash(this.name+"-v"+this.version).replace("-","s"),this.loadAll())};ig.Game.prototype.loadAll=function(){var a=ig.secure.get(this.storageName);""==a?(this.initData(),this.saveAll()):this.sessionData=JSON.parse(a)};ig.Game.prototype.saveAll=function(){ig.secure.set(this.storageName,
JSON.stringify(this.sessionData))};ig.Game.prototype.load=function(a){return this.sessionData[a]};ig.Game.prototype.save=function(a,b){this.sessionData[a]=b;this.saveAll()}});ig.baked=!0;
ig.module("plugins.splash-loader").requires("impact.loader","impact.animation").defines(function(){ig.SplashLoader=ig.Loader.extend({tapToStartDivId:"tap-to-start",bgImage:new ig.Image("media/graphics/sprites/ui/menu-bg.png"),titleImage:new ig.Image("media/graphics/sprites/ui/title.png"),loadingBarImage:new ig.Image("media/graphics/splash/loading/loading-bg.png"),loadingBarFillImage:new ig.Image("media/graphics/splash/loading/loading-fill.png"),truckImage:new ig.Image("media/graphics/splash/loading/truck.png"),
loadingTimer:null,loadingComplete:!1,tweenValue:0,startTween:!1,tweenSpeed:0.05,init:function(a,b){this.parent(a,b);window.splashloader=this;ig.apiHandler.run("MJSPreroll");this.loadingTimer=new ig.Timer;this.repos()},end:function(){this._endParent=this.parent;this._drawStatus=1;this.startTween=this.loadingComplete=!0;this.draw()},setGame:function(){_SETTINGS.TapToStartAudioUnlock.Enabled?this.tapToStartDiv(function(){this._endParent();("undefined"===typeof ig.game||null==ig.game)&&ig.system.setGame(this.gameClass)}.bind(this)):
(this._endParent(),("undefined"===typeof ig.game||null==ig.game)&&ig.system.setGame(this.gameClass))},tapToStartDiv:function(a){this.desktopCoverDIV=document.getElementById(this.tapToStartDivId);if(!this.desktopCoverDIV){this.desktopCoverDIV=document.createElement("div");this.desktopCoverDIV.id=this.tapToStartDivId;this.desktopCoverDIV.setAttribute("class","play");this.desktopCoverDIV.setAttribute("style","position: absolute; display: block; z-index: 999999; background-color: rgba(23, 32, 53, 0.7); visibility: visible; font-size: 10vmin; text-align: center; vertical-align: middle; -webkit-touch-callout: none; -webkit-user-select: none; -khtml-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none;");
this.desktopCoverDIV.innerHTML="<div style='color:white;background-color: rgba(255, 255, 255, 0.3); border: 2px solid #fff; font-size:20px; border-radius: 5px; position: relative; float: left; top: 50%; left: 50%; transform: translate(-50%, -50%);'><div style='padding:20px 50px; font-family: luckiestguy-regular;'>"+_STRINGS.Splash.TapToStart+"</div></div>";(document.getElementById("play").parentNode||document.getElementById("ajaxbar")).appendChild(this.desktopCoverDIV);try{"undefined"!==typeof ig.sizeHandler?
"undefined"!==typeof ig.sizeHandler.coreDivsToResize&&(ig.sizeHandler.coreDivsToResize.push("#"+this.tapToStartDivId),"function"===typeof ig.sizeHandler.reorient&&ig.sizeHandler.reorient()):"undefined"!==typeof coreDivsToResize&&(coreDivsToResize.push(this.tapToStartDivId),"function"===typeof sizeHandler&&sizeHandler())}catch(b){console.log(b)}this.desktopCoverDIV.addEventListener("click",function(){ig.soundHandler.unlockWebAudio();this.setAttribute("style","visibility: hidden;");"function"===typeof a&&
a()})}},drawCheck:0,draw:function(){this.repos();var a=ig.system.context;this._drawStatus+=(this.status-this._drawStatus)/5;1===this.drawCheck&&console.log("Font should be loaded before loader draw loop");2>this.drawCheck&&this.drawCheck++;this.drawBG();a.save();var b=this.titleImage.width,c=this.titleImage.height,d=ig.system.height/2-0.75*c;this.titleImage.draw(ig.system.width/2-b/2,d,0,0,b,c);if(this.loadingComplete){ig.Timer.step();this.startTween&&1<=this.tweenValue&&(this.startTween=!1,this.setGame());
this.startTween&&1>this.tweenValue&&(this.tweenValue+=this.loadingTimer.delta()*this.tweenSpeed,this.tweenValue=Math.min(this.tweenValue,1));var e=this.loadingBarImage.width,b=this.loadingBarImage.height,g=ig.system.width/2-e/2,c=d+c-b/2+120;this.loadingBarImage.draw(g,c,0,0,e,b);d=this.loadingBarFillImage.width*this.tweenValue;this.loadingBarFillImage.draw(g,c,0,0,d,this.loadingBarFillImage.height);var e=this.truckImage.width,j=this.truckImage.height;this.truckImage.draw(g+d-e,c+0.5*b-0.5*j,0,0,
e,j);a.restore();this.drawVersion()}},drawBG:function(){for(var a=ig.system.height,b=a/this.bgImage.height*this.bgImage.width,c=0;c<ig.system.width;)0<=c+b&&ig.system.context.drawImage(this.bgImage.data,c,0,b,a),c+=b},drawVersion:function(){if("undefined"!==typeof _SETTINGS.Versioning&&null!==_SETTINGS.Versioning&&_SETTINGS.Versioning.DrawVersion){var a=ig.system.context;fontSize=_SETTINGS.Versioning.FontSize;fontFamily=_SETTINGS.Versioning.FontFamily;fillStyle=_SETTINGS.Versioning.FillStyle;a.save();
a.textBaseline="bottom";a.textAlign="left";a.font=fontSize+" "+fontFamily||"10px Arial";a.fillStyle=fillStyle||"#ffffff";a.fillText("v"+_SETTINGS.Versioning.Version+"+build."+_SETTINGS.Versioning.Build,10,ig.system.height-10);a.restore()}},repos:function(){var a=ig.system.width/ig.system.height;this.bgImage.width/this.bgImage.height>a?(this.bgH=this.bgImage.height,this.bgW=this.bgH*a,this.bgX=(this.bgImage.width-this.bgW)/2,this.bgY=0):(this.bgW=this.bgImage.width,this.bgH=this.bgW/a,this.bgX=0,this.bgY=
(this.bgImage.height-this.bgH)/2)}})});ig.baked=!0;
ig.module("plugins.math.sat").defines(function(){ig.SAT=ig.Class.extend({init:function(){},mtvForShapeIntersect:function(a,b){var c=1E4,d=null,e=[],g=[];if(a.isCircle()&&b.isCircle())e.push(a.center.subtract(b.center).normalized());else if(b.isCircle()||a.isCircle()){a.isCircle()&&(e=b,b=a,a=e);for(var j=a.pointList[0],p=b.center.manhattanDistance(j),e=a.getNormalizedAxes(),f=1;f<a.pointList.length;f++){var n=a.pointList[f],l=b.center.manhattanDistance(n);l<p&&(p=l,j=n)}g.push(b.center.subtract(j).normalized())}else e=
a.getNormalizedAxes(),g=b.getNormalizedAxes();for(f=0;f<e.length;f++){var j=e[f],l=a.project(j),q=b.project(j);if(l.overlap(q)){p=l.getOverlap(q);if(l.contains(q)||q.contains(l))n=Math.abs(l.min-q.min),l=Math.abs(l.max-q.max),p=n<l?p+n:p+l;p<c&&(c=p,d=j)}else return null}for(f=0;f<g.length;f++)if(j=g[f],l=a.project(j),q=b.project(j),l.overlap(q)){p=l.getOverlap(q);if(l.contains(q)||q.contains(l))n=Math.abs(l.min-q.min),l=Math.abs(l.max-q.max),p=n<l?p+n:p+l;p<c&&(c=p,d=j)}else return null;return new ig.SAT.MTV(d,
c)},simpleShapeIntersect:function(a,b){var c=[],d=[];if(a.isCircle()&&b.isCircle())c.push(a.center.subtract(b.center).normalized());else if(b.isCircle()||a.isCircle()){a.isCircle()&&(c=b,b=a,a=c);for(var e=a.pointList[0],g=b.center.manhattanDistance(e),c=a.getNormalizedAxes(),j=1;j<a.pointList.length;j++){var p=a.pointList[j],f=b.center.manhattanDistance(p);f<g&&(g=f,e=p)}d.push(b.center.subtract(e).normalized())}else c=a.getNormalizedAxes(),d=b.getNormalizedAxes();for(j=0;j<c.length;j++)if(g=c[j],
e=a.project(g),g=b.project(g),!e.overlap(g))return!1;for(j=0;j<d.length;j++)if(g=d[j],e=a.project(g),g=b.project(g),!g.overlap(e))return!1;return!0}});ig.SAT.Vector2D=ig.Class.extend({x:0,y:0,init:function(a,b){this.x=a;this.y=b},subtract:function(a){return new ig.SAT.Vector2D(this.x-a.x,this.y-a.y)},getNormal:function(){return new ig.SAT.Vector2D(-this.y,this.x)},getNormalizedNormal:function(){return(new ig.SAT.Vector2D(-this.y,this.x)).normalized()},normalized:function(){var a=Math.sqrt(this.x*
this.x+this.y*this.y);return 0==a?new ig.SAT.Vector2D(0,0):new ig.SAT.Vector2D(this.x/a,this.y/a)},distance:function(a){var b=a.x-this.x;a=a.y-this.y;return Math.sqrt(b*b+a*a)},manhattanDistance:function(a){var b=a.x-this.x;a=a.y-this.y;return b*b+a*a},dotProduct:function(a){return this.x*a.x+this.y*a.y},crossProduct:function(a){return this.x*a.y-this.y*a.x},getAngle:function(a){return Math.atan2(this.crossProduct(a),this.dotProduct(a))}});ig.SAT.Shape=ig.Class.extend({pointList:[],center:null,init:function(a){this.pointList=
[];for(var b=0;b<a.length;b++){var c=a[b];this.pointList.push(new ig.SAT.Vector2D(c.x,c.y))}},isCircle:function(){return!1},getAxes:function(){var a=[];if(1>=this.pointList.length)return a;if(2==this.pointList.length){var b=this.pointList[0],c=this.pointList[1],b=b.subtract(c),b=b.getNormal();(0!=b.x||0!=b.y)&&a.push(b);return a}for(var d=0;d<this.pointList.length;d++)b=this.pointList[d],c=this.pointList[d+1==this.pointList.length?0:d+1],b=b.subtract(c),b=b.getNormal(),(0!=b.x||0!=b.y)&&a.push(b);
return a},getNormalizedAxes:function(){for(var a=[],b=0;b<this.pointList.length;b++){var c=this.pointList[b].subtract(this.pointList[b+1==this.pointList.length?0:b+1]).getNormalizedNormal();(0!=c.x||0!=c.y)&&a.push(c)}return a},project:function(a){return new ig.SAT.Projection(this.pointList,a)}});ig.SAT.Circle=ig.SAT.Shape.extend({center:null,radius:0,init:function(a,b){this.center=new ig.SAT.Vector2D(a.x,a.y);this.radius=b},isCircle:function(){return!0},getAxes:function(){return[]},getNormalizedAxes:function(){return[]},
project:function(a){var b=new ig.SAT.Projection([],a);a=this.center.dotProduct(a);b.min=a-this.radius;b.max=a+this.radius;return b}});ig.SAT.Projection=ig.Class.extend({min:null,max:null,init:function(a,b){if(!a||0>=a.length)return this;this.min=a[0].dotProduct(b);this.max=a[0].dotProduct(b);for(var c=1;c<a.length;c++){var d=a[c].dotProduct(b);this.min>d&&(this.min=d);d>this.max&&(this.max=d)}},overlap:function(a){return!(this.max<a.min||a.max<this.min)},getOverlap:function(a){var b=this.max-a.min;
a=a.max-this.min;a<b&&(b=a);return b},contains:function(a){var b=this.max-this.min;if(a.max-a.min<b){var c=this.max-a.min;0>=c&&(c=a.max-this.min);if(c>=b)return!0}return!1}});ig.SAT.MTV=ig.Class.extend({axis:null,overlapAmount:0,init:function(a,b){this.axis=a;this.overlapAmount=b}})});ig.baked=!0;
ig.module("plugins.tween").requires("impact.entity").defines(function(){Array.prototype.indexOf||(Array.prototype.indexOf=function(a){for(var b=0;b<this.length;++b)if(this[b]===a)return b;return-1});ig.Entity.prototype.tweens=[];ig.Entity.prototype._preTweenUpdate=ig.Entity.prototype.update;ig.Entity.prototype.update=function(){this._preTweenUpdate();if(0<this.tweens.length){for(var a=[],b=0;b<this.tweens.length;b++)this.tweens[b].update(),this.tweens[b].complete||a.push(this.tweens[b]);this.tweens=
a}};ig.Entity.prototype.tween=function(a,b,c){a=new ig.Tween(this,a,b,c);this.tweens.push(a);return a};ig.Entity.prototype.pauseTweens=function(){for(var a=0;a<this.tweens.length;a++)this.tweens[a].pause()};ig.Entity.prototype.resumeTweens=function(){for(var a=0;a<this.tweens.length;a++)this.tweens[a].resume()};ig.Entity.prototype.stopTweens=function(a){for(var b=0;b<this.tweens.length;b++)this.tweens[b].stop(a)};ig.Tween=function(a,b,c,d){var e={},g={},j={},p=0,f=!1,n=!1,l=!1;this.duration=c;this.paused=
this.complete=!1;this.easing=ig.Tween.Easing.Linear.EaseNone;this.onComplete=!1;this.loop=this.delay=0;this.loopCount=-1;ig.merge(this,d);this.loopNum=this.loopCount;this.chain=function(a){l=a};this.initEnd=function(a,b,c){if("object"!==typeof b[a])c[a]=b[a];else for(subprop in b[a])c[a]||(c[a]={}),this.initEnd(subprop,b[a],c[a])};this.initStart=function(a,b,c,e){if("object"!==typeof c[a])"undefined"!==typeof b[a]&&(e[a]=c[a]);else for(subprop in c[a])e[a]||(e[a]={}),"undefined"!==typeof b[a]&&this.initStart(subprop,
b[a],c[a],e[a])};this.start=function(){this.paused=this.complete=!1;this.loopNum=this.loopCount;p=0;-1==a.tweens.indexOf(this)&&a.tweens.push(this);n=!0;f=new ig.Timer;for(var c in b)this.initEnd(c,b,g);for(c in g)this.initStart(c,g,a,e),this.initDelta(c,j,a,g)};this.initDelta=function(a,b,c,e){if("object"!==typeof e[a])b[a]=e[a]-c[a];else for(subprop in e[a])b[a]||(b[a]={}),this.initDelta(subprop,b[a],c[a],e[a])};this.propUpdate=function(a,b,c,e,d){if("object"!==typeof c[a])b[a]="undefined"!=typeof c[a]?
c[a]+e[a]*d:b[a];else for(subprop in c[a])this.propUpdate(subprop,b[a],c[a],e[a],d)};this.propSet=function(a,b,c){if("object"!==typeof b[a])c[a]=b[a];else for(subprop in b[a])c[a]||(c[a]={}),this.propSet(subprop,b[a],c[a])};this.update=function(){if(!n)return!1;if(this.delay){if(f.delta()<this.delay)return;this.delay=0;f.reset()}if(this.paused||this.complete)return!1;var b=(f.delta()+p)/this.duration,b=1<b?1:b,c=this.easing(b);for(property in j)this.propUpdate(property,a,e,j,c);if(1<=b){if(0==this.loopNum||
!this.loop){this.complete=!0;if(this.onComplete)this.onComplete();l&&l.start();return!1}if(this.loop==ig.Tween.Loop.Revert){for(property in e)this.propSet(property,e,a);p=0;f.reset();-1!=this.loopNum&&this.loopNum--}else if(this.loop==ig.Tween.Loop.Reverse){b={};c={};ig.merge(b,g);ig.merge(c,e);ig.merge(e,b);ig.merge(g,c);for(property in g)this.initDelta(property,j,a,g);p=0;f.reset();-1!=this.loopNum&&this.loopNum--}}};this.pause=function(){this.paused=!0;f&&f.delta&&(p+=f.delta())};this.resume=function(){this.paused=
!1;f&&f.reset&&f.reset()};this.stop=function(a){a&&(this.loop=this.complete=this.paused=!1,p+=c,this.update());this.complete=!0}};ig.Tween.Loop={Revert:1,Reverse:2};ig.Tween.Easing={Linear:{},Quadratic:{},Cubic:{},Quartic:{},Quintic:{},Sinusoidal:{},Exponential:{},Circular:{},Elastic:{},Back:{},Bounce:{}};ig.Tween.Easing.Linear.EaseNone=function(a){return a};ig.Tween.Easing.Quadratic.EaseIn=function(a){return a*a};ig.Tween.Easing.Quadratic.EaseOut=function(a){return-a*(a-2)};ig.Tween.Easing.Quadratic.EaseInOut=
function(a){return 1>(a*=2)?0.5*a*a:-0.5*(--a*(a-2)-1)};ig.Tween.Easing.Cubic.EaseIn=function(a){return a*a*a};ig.Tween.Easing.Cubic.EaseOut=function(a){return--a*a*a+1};ig.Tween.Easing.Cubic.EaseInOut=function(a){return 1>(a*=2)?0.5*a*a*a:0.5*((a-=2)*a*a+2)};ig.Tween.Easing.Quartic.EaseIn=function(a){return a*a*a*a};ig.Tween.Easing.Quartic.EaseOut=function(a){return-(--a*a*a*a-1)};ig.Tween.Easing.Quartic.EaseInOut=function(a){return 1>(a*=2)?0.5*a*a*a*a:-0.5*((a-=2)*a*a*a-2)};ig.Tween.Easing.Quintic.EaseIn=
function(a){return a*a*a*a*a};ig.Tween.Easing.Quintic.EaseOut=function(a){return(a-=1)*a*a*a*a+1};ig.Tween.Easing.Quintic.EaseInOut=function(a){return 1>(a*=2)?0.5*a*a*a*a*a:0.5*((a-=2)*a*a*a*a+2)};ig.Tween.Easing.Sinusoidal.EaseIn=function(a){return-Math.cos(a*Math.PI/2)+1};ig.Tween.Easing.Sinusoidal.EaseOut=function(a){return Math.sin(a*Math.PI/2)};ig.Tween.Easing.Sinusoidal.EaseInOut=function(a){return-0.5*(Math.cos(Math.PI*a)-1)};ig.Tween.Easing.Exponential.EaseIn=function(a){return 0==a?0:Math.pow(2,
10*(a-1))};ig.Tween.Easing.Exponential.EaseOut=function(a){return 1==a?1:-Math.pow(2,-10*a)+1};ig.Tween.Easing.Exponential.EaseInOut=function(a){return 0==a?0:1==a?1:1>(a*=2)?0.5*Math.pow(2,10*(a-1)):0.5*(-Math.pow(2,-10*(a-1))+2)};ig.Tween.Easing.Circular.EaseIn=function(a){return-(Math.sqrt(1-a*a)-1)};ig.Tween.Easing.Circular.EaseOut=function(a){return Math.sqrt(1- --a*a)};ig.Tween.Easing.Circular.EaseInOut=function(a){return 1>(a/=0.5)?-0.5*(Math.sqrt(1-a*a)-1):0.5*(Math.sqrt(1-(a-=2)*a)+1)};ig.Tween.Easing.Elastic.EaseIn=
function(a){var b,c=0.1,d=0.4;if(0==a)return 0;if(1==a)return 1;d||(d=0.3);!c||1>c?(c=1,b=d/4):b=d/(2*Math.PI)*Math.asin(1/c);return-(c*Math.pow(2,10*(a-=1))*Math.sin((a-b)*2*Math.PI/d))};ig.Tween.Easing.Elastic.EaseOut=function(a){var b,c=0.1,d=0.4;if(0==a)return 0;if(1==a)return 1;d||(d=0.3);!c||1>c?(c=1,b=d/4):b=d/(2*Math.PI)*Math.asin(1/c);return c*Math.pow(2,-10*a)*Math.sin((a-b)*2*Math.PI/d)+1};ig.Tween.Easing.Elastic.EaseInOut=function(a){var b,c=0.1,d=0.4;if(0==a)return 0;if(1==a)return 1;
d||(d=0.3);!c||1>c?(c=1,b=d/4):b=d/(2*Math.PI)*Math.asin(1/c);return 1>(a*=2)?-0.5*c*Math.pow(2,10*(a-=1))*Math.sin((a-b)*2*Math.PI/d):0.5*c*Math.pow(2,-10*(a-=1))*Math.sin((a-b)*2*Math.PI/d)+1};ig.Tween.Easing.Back.EaseIn=function(a){return a*a*(2.70158*a-1.70158)};ig.Tween.Easing.Back.EaseOut=function(a){return(a-=1)*a*(2.70158*a+1.70158)+1};ig.Tween.Easing.Back.EaseInOut=function(a){return 1>(a*=2)?0.5*a*a*(3.5949095*a-2.5949095):0.5*((a-=2)*a*(3.5949095*a+2.5949095)+2)};ig.Tween.Easing.Bounce.EaseIn=
function(a){return 1-ig.Tween.Easing.Bounce.EaseOut(1-a)};ig.Tween.Easing.Bounce.EaseOut=function(a){return(a/=1)<1/2.75?7.5625*a*a:a<2/2.75?7.5625*(a-=1.5/2.75)*a+0.75:a<2.5/2.75?7.5625*(a-=2.25/2.75)*a+0.9375:7.5625*(a-=2.625/2.75)*a+0.984375};ig.Tween.Easing.Bounce.EaseInOut=function(a){return 0.5>a?0.5*ig.Tween.Easing.Bounce.EaseIn(2*a):0.5*ig.Tween.Easing.Bounce.EaseOut(2*a-1)+0.5};ig.Tween.Interpolation={Linear:function(a,b){var c=a.length-1,d=c*b,e=Math.floor(d),g=TWEEN.Interpolation.Utils.Linear;
return 0>b?g(a[0],a[1],d):1<b?g(a[c],a[c-1],c-d):g(a[e],a[e+1>c?c:e+1],d-e)}}});ig.baked=!0;
ig.module("plugins.patches.entity-patch").requires("impact.entity").defines(function(){ig.Entity.inject({handleMovementTrace:function(a){this.standing=!1;a.collision.y&&(0<this.bounciness&&Math.abs(this.vel.y)>this.minBounceVelocity?this.vel.y*=-this.bounciness:(0<this.vel.y&&(this.standing=!0),this.vel.y=0));a.collision.x&&(this.vel.x=0<this.bounciness&&Math.abs(this.vel.x)>this.minBounceVelocity?this.vel.x*-this.bounciness:0);if(a.collision.slope){var b=a.collision.slope;if(0<this.bounciness){var c=
this.vel.x*b.nx+this.vel.y*b.ny;this.vel.x=(this.vel.x-2*b.nx*c)*this.bounciness;this.vel.y=(this.vel.y-2*b.ny*c)*this.bounciness}else c=(this.vel.x*b.x+this.vel.y*b.y)/(b.x*b.x+b.y*b.y),this.vel.x=b.x*c,this.vel.y=b.y*c,b=Math.atan2(b.x,b.y),b>this.slopeStanding.min&&b<this.slopeStanding.max&&(this.standing=!0)}this.pos.x=a.pos.x;this.pos.y=a.pos.y},update:function(){this.parent();null!==this.clickableLayer&&"undefined"!==typeof this.clickableLayer&&null!==this.clickableLayer.update&&"function"===
typeof this.clickableLayer.update&&this.clickableLayer.update(this.pos.x,this.pos.y,this.size.x,this.size.y)}})});ig.baked=!0;
ig.module("plugins.tweens-handler").requires("impact.entity","plugins.tween","plugins.patches.entity-patch").defines(function(){Array.prototype.indexOf||(Array.prototype.indexOf=function(a){for(var c=0;c<this.length;++c)if(this[c]===a)return c;return-1});ig.TweensHandler=ig.Class.extend({_tweens:[],_systemPausedTweens:[],init:function(){},getAll:function(){return this._tweens},removeAll:function(){this._tweens=[]},add:function(a){this._tweens.push(a)},remove:function(a){a=this._tweens.indexOf(a);
-1!==a&&this._tweens.splice(a,1)},onSystemPause:function(){this._tweens.forEach(function(a){a._isPlaying&&(this._systemPausedTweens.push(a),a.pause())}.bind(this))},onSystemResume:function(){this._systemPausedTweens.forEach(function(a){a.resume()});this._systemPausedTweens=[]},update:function(a){this._tweens.forEach(function(c){c.update(a)})},now:function(){return Date.now()}});ig.TweenDef=ig.Class.extend({version:"2.0.0",_ent:null,_valuesStart:{},_valuesEnd:{},_valuesStartRepeat:{},_duration:1E3,
_repeat:0,_yoyo:!1,_isPlaying:!1,_reversed:!1,_delayTime:0,_startTime:null,_pauseTime:null,_easingFunction:ig.Tween.Easing.Linear.EaseNone,_interpolationFunction:ig.Tween.Interpolation.Linear,_chainedTweens:[],_onStartCallback:null,_onStartCallbackFired:!1,_onUpdateCallback:null,_onCompleteCallback:null,_onStopCallback:null,_onPauseCallback:null,_onResumeCallback:null,_currentElapsed:0,init:function(a){this._object=a},to:function(a,c){this._valuesEnd=a;void 0!==c&&(this._duration=c);return this},
start:function(a){if(this._isPlaying)return this;ig.game.tweens.add(this);this._isPlaying=!0;this._onStartCallbackFired=!1;this._startTime=void 0!==a?a:ig.game.tweens.now();this._startTime+=this._delayTime;a=this._valuesEnd;this._valuesEnd={};for(var c in a){if(a[c]instanceof Array){if(0===a[c].length)continue;a[c]=[this._object[c]].concat(a[c])}if(void 0!==this._object[c]){if("object"==typeof a[c]){var d=a[c];this._valuesEnd[c]={};-1!=["position","scaling","rotation"].indexOf(c)?(this._valuesStart[c]=
{},["x","y","z"].forEach(function(a){"undefined"!=typeof d[a]&&(this._valuesStart[c][a]=this._object[c][a],this._valuesEnd[c][a]=d[a])}.bind(this))):(this._valuesEnd[c]=ig.copy(d),this._valuesStart[c]=ig.copy(this._object[c]))}else this._valuesEnd[c]=a[c],this._valuesStart[c]=this._object[c];this._valuesStartRepeat[c]=this._valuesStart[c]||0}}return this},set:function(a){ig.merge(this,a);return this},stop:function(){if(!this._isPlaying)return this;ig.game.tweens.remove(this);this._isPlaying=!1;null!==
this._onStopCallback&&this._onStopCallback.call(this._object,this._object);this.stopChainedTweens();return this},pause:function(){if(!this._isPlaying)return this;ig.game.tweens.remove(this);this._isPlaying=!1;this._pauseTime=ig.game.tweens.now();null!==this._onPauseCallback&&this._onPauseCallback.call(this._object,this._object);return this},resume:function(){if(this._isPlaying||!this._pauseTime)return this;var a=ig.game.tweens.now()-this._pauseTime;this._startTime+=a;ig.game.tweens.add(this);this._isPlaying=
!0;null!==this._onResumeCallback&&this._onResumeCallback.call(this._object,this._object);this._pauseTime=null;return this},end:function(){this.update(this._startTime+this._duration);return this},stopChainedTweens:function(){for(var a=0,c=this._chainedTweens.length;a<c;a++)this._chainedTweens[a].stop()},delay:function(a){this._delayTime=a;return this},repeat:function(a){this._repeat=a;return this},repeatDelay:function(a){this._repeatDelayTime=a;return this},yoyo:function(a){this._yoyo=a;return this},
easing:function(a){this._easingFunction=a;return this},interpolation:function(a){this._interpolationFunction=a;return this},chain:function(){this._chainedTweens=arguments;return this},onStart:function(a){this._onStartCallback=a;return this},onUpdate:function(a){this._onUpdateCallback=a;return this},onComplete:function(a){this._onCompleteCallback=a;return this},onStop:function(a){this._onStopCallback=a;return this},onPause:function(a){this._onPauseCallback=a;return this},onResume:function(a){this._onResumeCallback=
a;return this},propUpdate:function(a,c,d,e,g){"string"===typeof e&&(e="+"===e.charAt(0)||"-"===e.charAt(0)?d+parseFloat(e):parseFloat(e));if("object"!==typeof c[a])c[a]=d+(e-d)*g;else for(var j in e)this.propUpdate(j,c[a],d[j],e[j],g)},update:function(a){if(!(a<this._startTime)){var c,d,e;!1===this._onStartCallbackFired&&(null!==this._onStartCallback&&this._onStartCallback.call(this._object,this._object),this._onStartCallbackFired=!0);d=(a-this._startTime)/this._duration;1<d&&(d=1);this._currentElapsed=
d;e=this._easingFunction(d);for(c in this._valuesEnd){var g=this._valuesStart[c],j=this._valuesEnd[c];j instanceof Array?this._object[c]=this._interpolationFunction(j,e):this.propUpdate(c,this._object,g,j,e)}null!==this._onUpdateCallback&&this._onUpdateCallback.call(this._object,this._object,e);if(1===d)if(0<this._repeat){isFinite(this._repeat)&&this._repeat--;for(c in this._valuesStartRepeat)this._yoyo&&(d=this._valuesStartRepeat[c],this._valuesStartRepeat[c]=this._valuesEnd[c],this._valuesEnd[c]=
d),this._valuesStart[c]=this._valuesStartRepeat[c];this._yoyo&&(this._reversed=!this._reversed);this._startTime=void 0!==this._repeatDelayTime?a+this._repeatDelayTime:a+this._delayTime}else{this._isPlaying=!1;ig.game.tweens.remove(this);null!==this._onCompleteCallback&&this._onCompleteCallback.call(this._object,this._object);a=0;for(c=this._chainedTweens.length;a<c;a++)this._chainedTweens[a].start(this._startTime+this._duration)}}}});var a=[1];ig.Tween.Interpolation={Linear:function(a,c){var d=a.length-
1,e=d*c,g=Math.floor(e),j=ig.Tween.Interpolation.Utils.Linear;return 0>c?j(a[0],a[1],e):1<c?j(a[d],a[d-1],d-e):j(a[g],a[g+1>d?d:g+1],e-g)},Bezier:function(a,c){for(var d=0,e=a.length-1,g=Math.pow,j=ig.Tween.Interpolation.Utils.Bernstein,p=0;p<=e;p++)d+=g(1-c,e-p)*g(c,p)*a[p]*j(e,p);return d},CatmullRom:function(a,c){var d=a.length-1,e=d*c,g=Math.floor(e),j=ig.Tween.Interpolation.Utils.CatmullRom;return a[0]===a[d]?(0>c&&(g=Math.floor(e=d*(1+c))),j(a[(g-1+d)%d],a[g],a[(g+1)%d],a[(g+2)%d],e-g)):0>c?
a[0]-(j(a[0],a[0],a[1],a[1],-e)-a[0]):1<c?a[d]-(j(a[d],a[d],a[d-1],a[d-1],e-d)-a[d]):j(a[g?g-1:0],a[g],a[d<g+1?d:g+1],a[d<g+2?d:g+2],e-g)},Utils:{Linear:function(a,c,d){return(c-a)*d+a},Bernstein:function(a,c){var d=ig.Tween.Interpolation.Utils.Factorial;return d(a)/d(c)/d(a-c)},Factorial:function(b){var c=1;if(a[b])return a[b];for(var d=b;1<d;d--)c*=d;return a[b]=c},CatmullRom:function(a,c,d,e,g){a=0.5*(d-a);e=0.5*(e-c);var j=g*g;return(2*c-2*d+a+e)*g*j+(-3*c+3*d-2*a-e)*j+a*g+c}}}});ig.baked=!0;
ig.module("plugins.url-parameters").defines(function(){ig.UrlParameters=ig.Class.extend({init:function(){switch(getQueryVariable("iphone")){case "true":ig.ua.iPhone=!0,console.log("iPhone mode")}var a=getQueryVariable("webview");if(a)switch(a){case "true":ig.ua.is_uiwebview=!0,console.log("webview mode")}if(a=getQueryVariable("debug"))switch(a){case "true":ig.game.showDebugMenu(),console.log("debug mode")}switch(getQueryVariable("view")){case "stats":ig.game.resetPlayerStats(),ig.game.endGame()}getQueryVariable("ad")}})});
ig.baked=!0;
ig.module("plugins.director").requires("impact.impact").defines(function(){ig.Director=ig.Class.extend({init:function(a,b){this.game=a;this.levels=[];this.currentLevel=0;this.append(b)},loadLevel:function(a){for(var b in ig.sizeHandler.dynamicClickableEntityDivs){var c=ig.domHandler.getElementById("#"+b);ig.domHandler.hide(c)}this.currentLevel=a;this.game.loadLevel(this.levels[a]);return!0},loadLevelWithoutEntities:function(a){this.currentLevel=a;this.game.loadLevelWithoutEntities(this.levels[a]);return!0},
append:function(a){newLevels=[];return"object"===typeof a?(a.constructor===[].constructor?newLevels=a:newLevels[0]=a,this.levels=this.levels.concat(newLevels),!0):!1},nextLevel:function(){return this.currentLevel+1<this.levels.length?this.loadLevel(this.currentLevel+1):!1},previousLevel:function(){return 0<=this.currentLevel-1?this.loadLevel(this.currentLevel-1):!1},jumpTo:function(a){var b=null;for(i=0;i<this.levels.length;i++)this.levels[i]==a&&(b=i);return 0<=b?this.loadLevel(b):!1},firstLevel:function(){return this.loadLevel(0)},
lastLevel:function(){return this.loadLevel(this.levels.length-1)},reloadLevel:function(){return this.loadLevel(this.currentLevel)},checkLevelByClass:function(a){return this.levels[this.currentLevel]==a}})});ig.baked=!0;
ig.module("plugins.impact-storage").requires("impact.game").defines(function(){ig.Storage=ig.Class.extend({staticInstantiate:function(){return!ig.Storage.instance?null:ig.Storage.instance},init:function(){ig.Storage.instance=this},isCapable:function(){return"undefined"!==typeof window.localStorage},isSet:function(a){return null!==this.get(a)},initUnset:function(a,b){null===this.get(a)&&this.set(a,b)},get:function(a){if(!this.isCapable())return null;try{return JSON.parse(localStorage.getItem(a))}catch(b){return window.localStorage.getItem(a)}},
getInt:function(a){return~~this.get(a)},getFloat:function(a){return parseFloat(this.get(a))},getBool:function(a){return!!this.get(a)},key:function(a){return this.isCapable()?window.localStorage.key(a):null},set:function(a,b){if(!this.isCapable())return null;try{window.localStorage.setItem(a,JSON.stringify(b))}catch(c){console.log(c)}},setHighest:function(a,b){b>this.getFloat(a)&&this.set(a,b)},remove:function(a){if(!this.isCapable())return null;window.localStorage.removeItem(a)},clear:function(){if(!this.isCapable())return null;
window.localStorage.clear()}})});ig.baked=!0;
ig.module("plugins.fullscreen").requires("impact.entity","plugins.handlers.size-handler","plugins.director").defines(function(){ig.Fullscreen={version:"1.0.0",enableFullscreenButton:!0,_isEnabled:"notChecked",isEnabled:function(){"notChecked"==this._isEnabled&&(this._isEnabled=document.fullscreenEnabled||document.mozFullScreenEnabled||document.webkitFullscreenEnabled||document.msFullscreenEnabled?!0:!1);return this._isEnabled},isFullscreen:function(){return ig.Fullscreen.isEnabled()&&(document.fullscreenElement||
document.mozFullScreenElement||document.webkitFullscreenElement||document.msFullscreenElement)?!0:!1},toggleFullscreen:function(){ig.Fullscreen.isFullscreen()?ig.Fullscreen.exitFullscreen():ig.Fullscreen.requestFullscreen();ig.sizeHandler.orientationDelayHandler();if(ig&&ig.visibilityHandler)ig.visibilityHandler.onChange("focus");try{ig.soundHandler.unlockWebAudio()}catch(a){}},requestFullscreen:function(){var a=document.documentElement;a.requestFullscreen?a.requestFullscreen():a.webkitRequestFullscreen?
a.webkitRequestFullscreen():a.mozRequestFullScreen?a.mozRequestFullScreen():a.msRequestFullscreen?a.msRequestFullscreen():console.log("no request fullscreen method available")},exitFullscreen:function(){document.exitFullscreen?document.exitFullscreen():document.webkitExitFullscreen?document.webkitExitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.msExitFullscreen?document.msExitFullscreen():console.log("no exit fullscreen method available")},divs:{}};ig.Director.inject({loadLevel:function(a){var b=
ig.Fullscreen.divs,c;for(c in b)b=ig.domHandler.getElementById("#"+c),ig.domHandler.hide(b);return this.parent(a)}});ig.SizeHandler.inject({resize:function(){this.parent();var a=ig.Fullscreen.divs,b;for(b in a){var c=Math.min(ig.sizeHandler.scaleRatioMultiplier.x,ig.sizeHandler.scaleRatioMultiplier.y),d=ig.domHandler.getElementById("#"+b),e=a[b].entity_pos_x,g=a[b].entity_pos_y,j=a[b].width,p=a[b].height,f=ig.domHandler.getElementById("#canvas"),n=ig.domHandler.getOffsets(f);ig.ua.mobile?(f=n.left,
n=n.top,ig.sizeHandler.disableStretchToFitOnMobileFlag?(e=Math.floor(f+e*ig.sizeHandler.scaleRatioMultiplier.x)+"px",g=Math.floor(n+g*ig.sizeHandler.scaleRatioMultiplier.y)+"px",j=Math.floor(j*ig.sizeHandler.scaleRatioMultiplier.x)+"px",p=Math.floor(p*ig.sizeHandler.scaleRatioMultiplier.y)+"px"):(e=Math.floor(e*ig.sizeHandler.sizeRatio.x)+"px",g=Math.floor(g*ig.sizeHandler.sizeRatio.y)+"px",j=Math.floor(j*ig.sizeHandler.sizeRatio.x)+"px",p=Math.floor(p*ig.sizeHandler.sizeRatio.y)+"px")):(f=n.left,
n=n.top,e=Math.floor(f+e*c)+"px",g=Math.floor(n+g*c)+"px",j=Math.floor(j*c)+"px",p=Math.floor(p*c)+"px");ig.domHandler.css(d,{"float":"left",position:"absolute",left:e,top:g,width:j,height:p,"z-index":3});a[b]["font-size"]&&ig.domHandler.css(d,{"font-size":a[b]["font-size"]*c+"px"})}}});ig.FullscreenButton=ig.Entity.extend({enterImage:null,exitImage:null,isReady:!1,zIndex:999999,isShown:!0,identifier:null,prevPos:{x:0,y:0},invisImagePath:"media/graphics/misc/invisible.png",enterImage:new ig.Image("media/graphics/misc/enter-fullscreen.png"),
exitImage:new ig.Image("media/graphics/misc/exit-fullscreen.png"),alpha:1,init:function(a,b,c){this.parent(a,b,c);ig.Fullscreen.isEnabled()&&ig.Fullscreen.enableFullscreenButton?this.enterImage.loaded?this.initSize():this.enterImage.loadCallback=function(){this.initSize()}.bind(this):this.kill()},kill:function(){this.parent();var a=ig.domHandler.getElementById("#"+this.identifier);a&&ig.domHandler.hide(a)},show:function(){this.isShown=!0;this.enable()},hide:function(){this.isShown=!1;this.disable()},
disable:function(){var a=ig.domHandler.getElementById("#"+this.identifier);a&&ig.domHandler.hide(a)},enable:function(){var a=ig.domHandler.getElementById("#"+this.identifier);a&&ig.domHandler.show(a)},updateAlpha:function(a){this.alpha=a},destroy:function(){this.parent();console.log("destroy")},initSize:function(){this.size.x=this.enterImage.width;this.size.y=this.enterImage.height;this.createClickableLayer();this.isReady=!0},createClickableLayer:function(){this.identifier="fullscreen-button-layer";
var a=ig.domHandler.getElementById("#"+this.identifier);a?(ig.domHandler.show(a),ig.domHandler.attr(a,"onclick","ig.Fullscreen.toggleFullscreen()")):this.createClickableOutboundLayer()},update:function(a,b){a=this.pos.x;b=this.pos.y;if(this.isReady&&!(this.prevPos.x===a&&this.prevPos.y===b)){ig.Fullscreen.divs[this.identifier]={};ig.Fullscreen.divs[this.identifier].width=this.size.x;ig.Fullscreen.divs[this.identifier].height=this.size.y;ig.Fullscreen.divs[this.identifier].entity_pos_x=this.pos.x;
ig.Fullscreen.divs[this.identifier].entity_pos_y=this.pos.y;var c=ig.domHandler.getElementById("#"+this.identifier);this.updateCSS(c);this.prevPos.x=a;this.prevPos.y=b}},draw:function(){var a=ig.system.context;a.save();a.globalAlpha=this.alpha;this.isReady&&this.isShown&&(ig.Fullscreen.isFullscreen()?this.exitImage.draw(this.pos.x,this.pos.y):this.enterImage.draw(this.pos.x,this.pos.y));a.restore()},createClickableOutboundLayer:function(){var a=this.identifier,b=this.invisImagePath,c=ig.domHandler.create("div");
ig.domHandler.attr(c,"id",a);ig.domHandler.attr(c,"onclick","ig.Fullscreen.toggleFullscreen()");var d=ig.domHandler.create("a"),e=ig.domHandler.create("img");ig.domHandler.css(e,{width:"100%",height:"100%"});ig.domHandler.attr(e,"src",b);ig.domHandler.addEvent(c,"mousemove",ig.input.mousemove.bind(ig.input),!1);ig.domHandler.appendChild(d,e);ig.domHandler.appendChild(c,d);ig.domHandler.appendToBody(c);ig.Fullscreen.divs[a]={};ig.Fullscreen.divs[a].width=this.size.x;ig.Fullscreen.divs[a].height=this.size.y;
ig.Fullscreen.divs[a].entity_pos_x=this.pos.x;ig.Fullscreen.divs[a].entity_pos_y=this.pos.y;this.updateCSS(c)},updateCSS:function(a){var b=Math.min(ig.sizeHandler.scaleRatioMultiplier.x,ig.sizeHandler.scaleRatioMultiplier.y);if(ig.ua.mobile){var c=ig.domHandler.getElementById("#canvas"),c=ig.domHandler.getOffsets(c),d=c.left,e=c.top;console.log(c.left);ig.sizeHandler.disableStretchToFitOnMobileFlag?(c=Math.floor(d+this.pos.x*ig.sizeHandler.scaleRatioMultiplier.x)+"px",e=Math.floor(e+this.pos.y*ig.sizeHandler.scaleRatioMultiplier.y)+
"px",d=Math.floor(this.size.x*ig.sizeHandler.scaleRatioMultiplier.x)+"px",b=Math.floor(this.size.y*ig.sizeHandler.scaleRatioMultiplier.y)+"px"):(c=Math.floor(this.pos.x*ig.sizeHandler.sizeRatio.x)+"px",e=Math.floor(this.pos.y*ig.sizeHandler.sizeRatio.y)+"px",d=Math.floor(this.size.x*ig.sizeHandler.sizeRatio.x)+"px",b=Math.floor(this.size.y*ig.sizeHandler.sizeRatio.y)+"px")}else c=ig.domHandler.getElementById("#canvas"),c=ig.domHandler.getOffsets(c),d=c.left,e=c.top,ig.sizeHandler.enableStretchToFitOnDesktopFlag?
(c=Math.floor(d+this.pos.x*ig.sizeHandler.sizeRatio.x)+"px",e=Math.floor(e+this.pos.y*ig.sizeHandler.sizeRatio.y)+"px",d=Math.floor(this.size.x*ig.sizeHandler.sizeRatio.x)+"px",b=Math.floor(this.size.y*ig.sizeHandler.sizeRatio.y)+"px"):(c=Math.floor(d+this.pos.x*b)+"px",e=Math.floor(e+this.pos.y*b)+"px",d=Math.floor(this.size.x*b)+"px",b=Math.floor(this.size.y*b)+"px");ig.domHandler.css(a,{"float":"left",position:"absolute",left:c,top:e,width:d,height:b,"z-index":3})}})});ig.baked=!0;
ig.module("plugins.utils.utils").requires("impact.timer","impact.entity").defines(function(){function a(a,b,c,d,f){this._listener=b;this._isOnce=c;this.context=d;this._signal=a;this._priority=f||0}function b(a,b){if("function"!==typeof a)throw Error("listener is a required param of {fn}() and should be a Function.".replace("{fn}",b));}var c=ig.global;ig.utils={};ig.utils.type=function(a){return null==a?a+"":Object.prototype.toString.call(a).slice(8,-1).toLowerCase()};ig.utils.isNumber=function(a){return!isNaN(a)&&
isFinite(a)&&"boolean"!==typeof a};ig.utils.isArray=function(a){return"[object Array]"===Object.prototype.toString.call(a)};ig.utils.toArray=function(a){return a?!0!==ig.utils.isArray(a)?[a]:a:[]};ig.utils.toNotArray=function(a,b){return!0===ig.utils.isArray(a)?a[b||0]:a};ig.utils.arrayCautiousAdd=function(a,b){-1===ig.utils.indexOfValue(a,b)&&a.push(b);return a};ig.utils.arrayCautiousAddMulti=function(a,b){var c,d;b=ig.utils.toArray(b);for(var f=0,n=b.length;f<n;f++)c=b[f],c!==a&&(d=ig.utils.indexOfValue(a,
c),-1===d&&a.push(c));return a};ig.utils.arrayCautiousRemove=function(a,b){var c=ig.utils.indexOfValue(a,b);-1!==c&&a.splice(c,1);return a};ig.utils.arrayCautiousRemoveMulti=function(a,b){var c;b=ig.utils.toArray(b);for(var d=0,f=b.length;d<f;d++)c=b[d],c!==a&&(c=ig.utils.indexOfValue(a,c),-1!==c&&a.splice(c,1));return a};ig.utils.forEach=function(a,b,c){for(var d=0,f=a.length;d<f;d++)b.apply(a[d],c)};ig.utils.indexOfValue=function(a,b){for(var c=0,d=a.length;c<d;c++)if(b===a[c])return c;return-1};
ig.utils.indexOfProperty=function(a,b,c){for(var d=0,f=a.length;d<f;d++)if(c===a[d][b])return d;return-1};ig.utils.indexOfProperties=function(a,b,c){for(var d=0,f=a.length;d<f;d++){for(var n=a[d],l=!1,q=0,r=b.length;q<r;q++)if(c[q]!==n[b[q]]){l=!0;break}if(!0!==l)return d}return-1};ig.utils.throttle=function(a,b,d){var p,f=0;!0!==ig.utils.isNumber(b)&&(b=500);return function(){function n(){f=Date.now();a.apply(l,r)}var l=this,q=Date.now()-f,r=arguments;q>b?(p&&c.clearTimeout(p),n()):!1!==d&&(p&&c.clearTimeout(p),
p=c.setTimeout(n,b-q))}};ig.utils.debounce=function(a,b){var d;!0!==ig.utils.isNumber(b)&&(b=500);return function(){var p=this,f=arguments;d&&c.clearTimeout(d);d=c.setTimeout(function(){a.apply(p,f)},b)}};var d=Math.pow(2,32);ig.utils.getType=function(a,b,c){c=c||"TYPE";var p=a[c],f=c+"_LAST";if(!a[f]||!p)a[f]=1,p=a[c]={};b=b.toUpperCase();c=p[b];if(!c){c=0;for(var n=a[f],l=b.split(" "),q=0,r=l.length;q<r;q++){var t=l[q],s=p[t];if(!s){if(n>=d)throw new TypeError("Bitwise flag out of range / above 32 bits!");
s=p[t]=n;a[f]=2*n}c|=s}p[b]=c}return c};ig.utils.addType=function(a,b,c,d,f){b[c]|=ig.utils.getType(a,d,f)};ig.Timer.minStep=1E3/60/1E3;ig.Timer.overflow=0;ig.Timer.stepped=!1;ig.Timer.step=function(){var a=Date.now();ig.Timer.overflow+=Math.min((a-ig.Timer._last)/1E3,ig.Timer.maxStep);ig.Timer.overflow>=ig.Timer.minStep?(ig.Timer.overflow-=ig.Timer.minStep,ig.Timer.time+=ig.Timer.minStep*ig.Timer.timeScale,ig.Timer.stepped=!0):ig.Timer.stepped=!1;ig.Timer._last=a};ig.utils.pi2=2*Math.PI;ig.utils.pio2=
Math.PI/2;ig.utils.lerp=function(a,b,c){c=ig.utils.clamp(c,0,1);return a+(b-a)*c};ig.utils.clamp=function(a,b,c){return Math.max(b,Math.min(c,a))};ig.utils.distanceBetweenPoints=function(a,b){return Math.sqrt(Math.pow(a.x-b.x,2)+Math.pow(a.y-b.y,2))};ig.utils.distanceBetween=function(a,b,c,d){return Math.sqrt(Math.pow(c-a,2)+Math.pow(d-b,2))};ig.utils.angleBetweenPoints=function(a,b){return Math.atan2(b.y-a.y,b.x-a.x)};ig.utils.angleBetween=function(a,b,c,d){return Math.atan2(d-b,c-a)};ig.utils.toRad=
function(a){return a/180*Math.PI};ig.utils.toDeg=function(a){return 180*(a/Math.PI)};ig.utils.randomBetween=function(a,b){return a+Math.random()*(b-a)};ig.utils.randomBetweenInt=function(a,b){return a+Math.floor(Math.random()*(b-a+1))};ig.utils.containPoint=function(a,b){for(var c,d=0;d<a.length;d++){var f=d+1;f==a.length&&(f=0);var n=a[d],f=a[f],n=(b.y-n.y)*(f.x-n.x)-(b.x-n.x)*(f.y-n.y);if(0==n)break;if(0===d)c=n;else if(0>c*n)return!1}return!0};ig.utils.pick=function(a){var b=Math.floor(Math.random()*
a.length);return a.splice(b,1)[0]};ig.utils.randomIn=function(a){var b=Math.floor(Math.random()*a.length);return a[b]};ig.utils.toMMSS=function(a){var b=Math.floor(a);a=Math.floor(b/60);b%=60;return(10>a?"0"+a:a)+":"+(10>b?"0"+b:b)};ig.utils.rotateAround=function(a,b,c){var d=b*Math.PI/180;b=c*Math.cos(d)+a.x;a=c*Math.sin(d)+a.y;return{x:b,y:a}};ig.utils.getVertices=function(a,b,c,d,f){var n=Math.sin(c);c=Math.cos(c);d/=2;f/=2;return[{x:a-f*n-d*c,y:b-f*c+d*n},{x:a-f*n+d*c,y:b-f*c-d*n},{x:a+f*n+d*
c,y:b+f*c-d*n},{x:a+f*n-d*c,y:b+f*c+d*n}]};ig.utils.distanceFromPointToLine=function(a,b,c){var d=a.x;a=a.y;var f=b.x;b=b.y;var n=c.x;c=c.y;var l=n-f,q=c-b,r=l*l+q*q,t=-1;0!=r&&(t=((d-f)*l+(a-b)*q)/r);0>t||(1<t?(f=n,b=c):(f+=t*l,b+=t*q));d-=f;a-=b;return Math.sqrt(d*d+a*a)};ig.utils.pointInPolygon=function(a,b){if(!b||3>b.length)return!1;for(var c=!1,d=b.length-1,f=0;f<b.length;f++)b[f].y>a.y!==b[d].y>a.y&&a.x<(b[d].x-b[f].x)*(a.y-b[f].y)/(b[d].y-b[f].y)+b[f].x&&(c=!c),d=f;return c};ig.utils.calculatePolygonCenter=
function(a){if(!a||0===a.length)return{x:0,y:0};for(var b=0,c=0,d=0;d<a.length;d++)b+=a[d].x,c+=a[d].y;return{x:b/a.length,y:c/a.length}};ig.utils.getParkingSlotGeometry=function(a){if(!a||4>a.length)return null;for(var b=[],c=0;c<a.length;c++){var d=a[c],f=a[(c+1)%a.length],n=Math.sqrt(Math.pow(f.x-d.x,2)+Math.pow(f.y-d.y,2));b.push({p1:d,p2:f,length:n,index:c})}b.sort(function(a,b){return a.length-b.length});c=b.slice(0,2);n=b.slice(2,4);b={x:(c[0].p1.x+c[0].p2.x)/2,y:(c[0].p1.y+c[0].p2.y)/2};d=
{x:(c[1].p1.x+c[1].p2.x)/2,y:(c[1].p1.y+c[1].p2.y)/2};f=Math.atan2(d.y-b.y,d.x-b.x);a=ig.utils.calculatePolygonCenter(a);n=(n[0].length+n[1].length)/2;return{center:a,width:(c[0].length+c[1].length)/2,length:n,angle:f,frontPoint:d,backPoint:b}};ig.utils.createQuadraticBezierCurve=function(a,b,c,d){for(var f=[],n=1;n<d;n++){var l=n/d,q=1-l;f.push({x:q*q*a.x+2*q*l*b.x+l*l*c.x,y:q*q*a.y+2*q*l*b.y+l*l*c.y})}return f};ig.utils.createCubicBezierCurve=function(a,b,c,d,f){for(var n=[],l=1;l<f;l++){var q=
l/f,r=1-q,t=q*q,s=r*r,C=s*r,F=t*q;n.push({x:C*a.x+3*s*q*b.x+3*r*t*c.x+F*d.x,y:C*a.y+3*s*q*b.y+3*r*t*c.y+F*d.y})}return n};ig.utils.rotatePoint=function(a,b,c,d,f){a-=c;var n=b-d;b=a*Math.cos(f)-n*Math.sin(f);f=a*Math.sin(f)+n*Math.cos(f);return{x:b+c,y:f+d}};ig.rlog={debug:window.console.log.bind(window.console,"%c[DEBUG]","color: #5393e2;"),error:window.console.log.bind(window.console,"%c[ERROR]","color: #ee9698;"),warn:window.console.log.bind(window.console,"%c[WARN]","color: #f9ffb4;"),info:window.console.log.bind(window.console,
"%c[INFO]","color: #d6f6ff;")};CanvasRenderingContext2D.prototype.roundRect=function(a,b,c,d,f){"undefined"===typeof f&&(f=5);this.beginPath();this.moveTo(a+f,b);this.lineTo(a+c-f,b);this.quadraticCurveTo(a+c,b,a+c,b+f);this.lineTo(a+c,b+d-f);this.quadraticCurveTo(a+c,b+d,a+c-f,b+d);this.lineTo(a+f,b+d);this.quadraticCurveTo(a,b+d,a,b+d-f);this.lineTo(a,b+f);this.quadraticCurveTo(a,b,a+f,b);this.closePath();this.fill()};ig.Entity.prototype.delayedCall=function(a,b,c){void 0===c&&(c=!0);a=new ig.Tween(this,
{},a,{onComplete:b});this.tweens.push(a);c&&a.start();return a};a.prototype={active:!0,params:null,execute:function(a){var b;this.active&&this._listener&&(a=this.params?this.params.concat(a):a,b=this._listener.apply(this.context,a),this._isOnce&&this.detach());return b},detach:function(){return this.isBound()?this._signal.remove(this._listener,this.context):null},isBound:function(){return!!this._signal&&!!this._listener},isOnce:function(){return this._isOnce},getListener:function(){return this._listener},
getSignal:function(){return this._signal},_destroy:function(){delete this._signal;delete this._listener;delete this.context},toString:function(){return"[SignalBinding isOnce:"+this._isOnce+", isBound:"+this.isBound()+", active:"+this.active+"]"}};ig.Signal=function(){this._bindings=[];this._prevParams=null;var a=this;this.dispatch=function(){ig.Signal.prototype.dispatch.apply(a,arguments)}};ig.Signal.prototype={VERSION:"1.0.0",memorize:!1,_shouldPropagate:!0,active:!0,_registerListener:function(b,
c,d,p){var f=this._indexOfListener(b,d);if(-1!==f){if(b=this._bindings[f],b.isOnce()!==c)throw Error("You cannot add"+(c?"":"Once")+"() then add"+(!c?"":"Once")+"() the same listener without removing the relationship first.");}else b=new a(this,b,c,d,p),this._addBinding(b);this.memorize&&this._prevParams&&b.execute(this._prevParams);return b},_addBinding:function(a){var b=this._bindings.length;do--b;while(this._bindings[b]&&a._priority<=this._bindings[b]._priority);this._bindings.splice(b+1,0,a)},
_indexOfListener:function(a,b){for(var c=this._bindings.length,d;c--;)if(d=this._bindings[c],d._listener===a&&d.context===b)return c;return-1},has:function(a,b){return-1!==this._indexOfListener(a,b)},add:function(a,c,d){b(a,"add");return this._registerListener(a,!1,c,d)},addOnce:function(a,c,d){b(a,"addOnce");return this._registerListener(a,!0,c,d)},remove:function(a,c){b(a,"remove");var d=this._indexOfListener(a,c);-1!==d&&(this._bindings[d]._destroy(),this._bindings.splice(d,1));return a},removeAll:function(){for(var a=
this._bindings.length;a--;)this._bindings[a]._destroy();this._bindings.length=0},getNumListeners:function(){return this._bindings.length},halt:function(){this._shouldPropagate=!1},dispatch:function(a){if(this.active){var b=Array.prototype.slice.call(arguments),c=this._bindings.length,d;this.memorize&&(this._prevParams=b);if(c){d=this._bindings.slice();this._shouldPropagate=!0;do c--;while(d[c]&&this._shouldPropagate&&!1!==d[c].execute(b))}}},forget:function(){this._prevParams=null},dispose:function(){this.removeAll();
delete this._bindings;delete this._prevParams},toString:function(){return"[Signal active:"+this.active+" numListeners:"+this.getNumListeners()+"]"}}});ig.baked=!0;
ig.module("plugins.utils.pointer").requires("impact.game","impact.entity").defines(function(){ig.Game.inject({update:function(){this.parent();if(ig.input.pressed("click")||ig.input.state("click")||ig.input.released("click")){for(var a=null,b=-Infinity,c=this.entities.length-1;0<=c;c--){var d=this.entities[c];!d.isInvisible&&d.underPointer()&&d.zIndex>b&&(b=d.zIndex,d.isClickable&&(a=d))}a&&(ig.input.pressed("click")&&"function"===typeof a.clicked&&a.clicked(),ig.input.state("click")&&"function"===
typeof a.clicking&&a.clicking(),ig.input.released("click")&&"function"===typeof a.released&&a.released())}}});ig.Entity.inject({isClickable:!1,isInvisible:!1,underPointer:function(){var a=ig.game.io.getClickPos();return this.containPoint({x:a.x+ig.game.screen.x,y:a.y+ig.game.screen.y})},containPoint:function(a){var b=this.pos.x,c=b+this.size.x,d=this.pos.y,e=d+this.size.y;return a.x>=b&&a.x<c&&a.y>=d&&a.y<e}})});ig.baked=!0;
ig.module("plugins.animation-scale").requires("impact.animation").defines(function(){ig.Animation.inject({scale:{x:1,y:1,align:{x:!1,y:!1}},draw:function(a,b){var c=ig.system.scale;ig.system.context.save();var d=0,e=0,d=this.scale.align&&this.scale.align.x&&"left"==this.scale.align.x?a:this.scale.align&&this.scale.align.x&&"right"==this.scale.align.x?a+this.sheet.width:a+this.sheet.width/2,e=this.scale.align&&this.scale.align.y&&"top"==this.scale.align.y?b:this.scale.align&&this.scale.align.y&&"bottom"==
this.scale.align.y?b+this.sheet.height:b+this.sheet.height/2;ig.system.context.translate(d*c,e*c);ig.system.context.scale(this.scale.x,this.scale.y);ig.system.context.translate(-d*c,-e*c);this.parent(a,b);ig.system.context.restore()}})});ig.baked=!0;
ig.module("plugins.utils.entity-extended").requires("impact.entity").defines(function(){ig.EntityExtended=ig.Entity.extend({children:[],init:function(a,b,c){null!=this.idleSheetInfo&&(this.setSpriteSheet("idle"),this.setSize("idle"));this.parent(a,b,c)},setSpriteSheet:function(a){this[a+"Sheet"]=new ig.AnimationSheet(this[a+"SheetInfo"].sheetImage.path,this[a+"SheetInfo"].sheetImage.width/this[a+"SheetInfo"].frameCountX,this[a+"SheetInfo"].sheetImage.height/this[a+"SheetInfo"].frameCountY)},setSize:function(a){this.size.x=
this[a+"SheetInfo"].sheetImage.width/this[a+"SheetInfo"].frameCountX;this.size.y=this[a+"SheetInfo"].sheetImage.height/this[a+"SheetInfo"].frameCountY},hide:function(){this.hidden||(this.disable(),this.hideChildren(),this.hidden=!0,this.visible=!1,this._zIndex=this.zIndex,this.zIndex=-1,ig.game.currentWindow.overlay&&0<ig.game.currentWindow.overlay.zIndex&&ig.game.currentWindow.overlay.hide(),ig.game.sortEntitiesDeferred())},show:function(){this.hidden&&(this.enable(),this.showChildren(),this.hidden=
!1,this.visible=!0,this.zIndex=this._zIndex,ig.game.sortEntitiesDeferred())},hideChildren:function(){if(0<this.children.length)for(var a=0;a<this.children.length;a++){var b=this.children[a];"function"===typeof b.hide&&b.hide()}},showChildren:function(){if(0<this.children.length)for(var a=0;a<this.children.length;a++){var b=this.children[a];"function"===typeof b.show&&b.show()}},disable:function(){this.disabled||(this.disabled=!0)},enable:function(){this.disabled&&(this.disabled=!1)},spawnEntity:function(a,
b,c,d){a=[a,b,c];d=d||{};d._parent=this;d.zIndex||(d.zIndex=this.zIndex+1);a.push(d);d=ig.game.spawnEntity.apply(ig.game,a);this.children.push(d);return d}})});ig.baked=!0;
ig.module("plugins.utils.text-to-canvas").defines(function(){function a(a,b){if(null==a)return{};var c,d,e;if(null==a)e={};else{c={};for(d in a)({}).hasOwnProperty.call(a,d)&&!b.includes(d)&&(c[d]=a[d]);e=c}if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],b.includes(c)||{}.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function b(a){var b;b=Array.isArray(a)?d(a):void 0;if(!b&&(b="undefined"!=typeof Symbol&&null!=a[Symbol.iterator]||null!=a["@@iterator"]?
Array.from(a):void 0,!b)){if(!(a=c(a)))throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");b=a}return b}function c(a,b){if(a){if("string"==typeof a)return d(a,b);var c={}.toString.call(a).slice(8,-1);return"Object"===c&&a.constructor&&(c=a.constructor.name),"Map"===c||"Set"===c?Array.from(a):"Arguments"===c||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(c)?d(a,b):void 0}}function d(a,b){(null==
b||b>a.length)&&(b=a.length);for(var c=0,d=Array(b);c<b;c++)d[c]=a[c];return d}function e(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable}));c.push.apply(c,d)}return c}function g(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?e(Object(c),!0).forEach(function(b){var d,e=c[b];a:if("object"==j(b)&&b){var f=b[Symbol.toPrimitive];if(void 0!==
f){b=f.call(b,"string");if("object"!=j(b))break a;throw new TypeError("@@toPrimitive must return a primitive value.");}b=String(b)}(d="symbol"==j(b)?b:b+"")in a?Object.defineProperty(a,d,{value:e,enumerable:!0,configurable:!0,writable:!0}):a[d]=e}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):e(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function j(a){return j="function"==typeof Symbol&&
"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},j(a)}var p=["ctx","words","justify","format","inferWhitespace"],f=["text"],n=this,l=function(d){var e,l=function(a,b){return Object.assign({},{fontFamily:"Arial",fontSize:14,fontWeight:"400",fontStyle:"",fontVariant:"",fontColor:"black",strokeColor:"black",strokeWidth:0},b,a)},n=function(a){var b=a.fontFamily,c=a.fontSize,d=
a.fontVariant,e=a.fontWeight;return"".concat(a.fontStyle||""," ").concat(d||""," ").concat(e||""," ").concat(null!=c?c:14,"px ").concat(b||"Arial").trim()},C=function(a){return!!a.match(/^\s+$/)},F=function(a,c){if(1>=a.length||1>c.length)return b(a);var d=[];return a.forEach(function(b,e){d.push(b);e<a.length-1&&c.forEach(function(a){return d.push(function(a){var b=g({},a);return a.format&&(b.format=g({},a.format)),b}(a))})}),d},A=function(a){var b=1<arguments.length&&void 0!==arguments[1]?arguments[1]:
"both",c=0;if("left"===b||"both"===b){for(;c<a.length&&C(a[c].text);c++);if(c>=a.length)return{trimmedLeft:a.concat(),trimmedRight:[],trimmedLine:[]}}var d=a.length;if("right"===b||"both"===b){for(d--;0<=d&&C(a[d].text);d--);if(0>=++d)return{trimmedLeft:[],trimmedRight:a.concat(),trimmedLine:[]}}return{trimmedLeft:a.slice(0,c),trimmedRight:a.slice(d),trimmedLine:a.slice(c,d)}},L=function(a){return"".concat(a.text).concat(a.format?JSON.stringify(a.format):"")},B=function(a,b){return"metrics"===a&&
b&&"object"==j(b)?{width:b.width,fontBoundingBoxAscent:b.fontBoundingBoxAscent,fontBoundingBoxDescent:b.fontBoundingBoxDescent}:b},K=function(a){var b=a.ctx,c=a.word,d=a.wordMap,f=a.baseTextFormat;a=L(c);if(c.metrics){var g;d.has(a)||(c.format&&(g=l(c.format,f)),d.set(a,{metrics:c.metrics,format:g}));return c.metrics.width}if(d.has(a))return b=d.get(a).metrics,c.metrics=b,b.width;var j;g=!1;c.format&&(b.save(),g=!0,j=l(c.format,f),b.font=n(j));e||(g||(b.save(),g=!0),b.textBaseline="bottom");f=b.measureText(c.text);
return"number"==typeof f.fontBoundingBoxAscent?e=!0:(e=!1,f.fontBoundingBoxAscent=f.actualBoundingBoxAscent,f.fontBoundingBoxDescent=0),c.metrics=f,d.set(a,{metrics:f,format:j}),g&&b.restore(),f.width},R=function(d){var e=d.ctx,f=d.words,g=d.justify,j=d.format,q=d.inferWhitespace,q=void 0===q||q;d=a(d,p);var r=new Map,B=l(j),D=d.width,J=function(a){var b=1<arguments.length&&void 0!==arguments[1]&&arguments[1],c=0,d=0;return a.every(function(a,f){var g=K({ctx:e,word:a,wordMap:r,baseTextFormat:B});
return!b&&c+g>D?(0===f&&(d=1,c=g),!1):(d++,c+=g,!0)}),{lineWidth:c,splitPoint:d}};e.save();f=function(a){var b=!(1<arguments.length&&void 0!==arguments[1])||arguments[1],c=[[]],d=!1;return a.forEach(function(a,e){var f,g,j;if(a.text.match(/^\n+$/)){for(f=0;f<a.text.length;f++)c.push([]);d=!0}else{if(C(a.text))return null==(f=c.at(-1))||f.push(a),void(d=!0);""!==a.text&&(b&&!d&&0<e&&(null==(g=c.at(-1))||g.push({text:" "})),null==(j=c.at(-1))||j.push(a),d=!1)}}),c}(A(f).trimmedLine,q);if(0>=f.length||
0>=D||0>=d.height||j&&"number"==typeof j.fontSize&&0>=j.fontSize)return{lines:[],textAlign:"center",textBaseline:"middle",width:d.width,height:0};e.font=n(B);var U,v=g?K({ctx:e,word:{text:"\u200a"},wordMap:r,baseTextFormat:B}):0,u=[];a:{var z=f,x="undefined"!=typeof Symbol&&z[Symbol.iterator]||z["@@iterator"];if(!x){if(Array.isArray(z)||(x=c(z))){x&&(z=x);var R=0,j=function(){},j={s:j,n:function(){return R>=z.length?{done:!0}:{done:!1,value:z[R++]}},e:function(a){throw a;},f:j};break a}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}var Ra,Ca=!0,Sa=!1,j={s:function(){x=x.call(z)},n:function(){var a=x.next();return Ca=a.done,a},e:function(a){Sa=!0;Ra=a},f:function(){try{Ca||null==x.return||x.return()}finally{if(Sa)throw Ra;}}}}try{for(j.s();!(U=j.n()).done;){var Z=U.value,ga=J(Z).splitPoint;if(ga>=Z.length)u.push(Z);else{for(var fa=Z.concat();ga<fa.length;){var Ta=A(fa.slice(0,ga),"right").trimmedLine;u.push(Ta);fa=A(fa.slice(ga),"left").trimmedLine;ga=J(fa).splitPoint}u.push(fa)}}}catch(Ua){j.e(Ua)}finally{j.f()}g&&1<u.length&&
u.forEach(function(a,c){if(c<u.length-1){var d;d=a.filter(function(a){return!C(a.text)});if(1>=d.length)d=a.concat();else{var e=d.reduce(function(a,b){var c,d;return a+(null!=(d=null==(c=b.metrics)?void 0:c.width)?d:0)},0),f=(D-e)/v;if(2<d.length){var e=Math.ceil(f/(d.length-1)),g=Array.from({length:e},function(){return{text:"\u200a"}}),j=d.slice(0,d.length-1),e=F(j,g),f=g.slice(0,Math.floor(f)-(j.length-1)*g.length);d=d[d.length-1];d=[].concat(b(e),b(f),[d])}else e=Array.from({length:Math.floor(f)},
function(){return{text:"\u200a"}}),d=F(d,e)}J(d,!0);u[c]=d}});var la,Da,Ea=d.width,g=d.height;U=d.x;var ba=void 0===U?0:U;U=d.y;U=void 0===U?0:U;var Va=d.align,aa=d.vAlign,gb=ba+Ea;d=U+g;var ta=u.map(function(a){return a.reduce(function(a,b){return Math.max(a,b.metrics.fontBoundingBoxAscent+b.metrics.fontBoundingBoxDescent)},0)}),Z=ta.reduce(function(a,b){return a+b},0);d=("top"===aa?(Da="top",la=U):"bottom"===aa?(Da="bottom",la=d-Z):(Da="top",la=U+g/2-Z/2),{lines:u.map(function(a,b){var c=a.reduce(function(a,
b){return a+b.metrics.width},0),d=ta[b],e="right"===Va?gb-c:"left"===Va?ba:ba+Ea/2-c/2,c=a.map(function(a){var b,c=L(a),c=r.get(c).format,f=e,g=a.metrics.fontBoundingBoxAscent+a.metrics.fontBoundingBoxDescent;return b="top"===aa?la:"bottom"===aa?la+d:la+(d-g)/2,e+=a.metrics.width,{word:a,format:c,x:f,y:b,width:a.metrics.width,height:g,isWhitespace:C(a.text)}});return la+=d,c}),textBaseline:Da,textAlign:"left",width:Ea,height:Z});return e.restore(),d},D=function(a){var b,c=[],d=!1;return Array.from(a.trim()).forEach(function(a){var e=
C(a);e&&!d||!e&&d?(d=e,b&&c.push(b),b={text:a}):(b||(b={text:""}),b.text+=a)}),b&&c.push(b),c},J=function(a,b,c){var d=a.textBaseline,e=a.font;a.textBaseline="bottom";c&&(a.font=c);b=a.measureText(b).actualBoundingBoxAscent;return a.textBaseline=d,c&&(a.font=e),b};d.drawText=function(a,b,c){var d,e=l({fontFamily:c.fontFamily,fontSize:c.fontSize,fontStyle:c.fontStyle,fontVariant:c.fontVariant,fontWeight:c.fontWeight,fontColor:c.fontColor,strokeColor:c.strokeColor,strokeWidth:c.strokeWidth}),f=c.width,
g=c.height,j=c.x,j=void 0===j?0:j,p=c.y,p=void 0===p?0:p,q=R({ctx:a,words:Array.isArray(b)?b:D(b),inferWhitespace:Array.isArray(b)?void 0===c.inferWhitespace||c.inferWhitespace:void 0,x:j,y:p,width:c.width,height:c.height,align:c.align,vAlign:c.vAlign,justify:c.justify,format:e}),r=q.lines;b=q.height;var v=q.textBaseline,q=q.textAlign;a.save();a.textAlign=q;a.textBaseline=v;a.font=n(e);a.fillStyle=e.fontColor||"black";a.strokeStyle=e.strokeColor||"black";a.lineJoin="round";var u=null!=(d=e.strokeWidth)?
d:0;if(!1===c.overflow&&(a.beginPath(),a.rect(j,p,f,g),a.clip()),r.forEach(function(b){b.forEach(function(b){var c;if(!b.isWhitespace){b.format&&(a.save(),a.font=n(b.format),b.format.fontColor&&(a.fillStyle=b.format.fontColor),b.format.strokeColor&&(a.strokeStyle=b.format.strokeColor));a.fillText(b.word.text,b.x,b.y);var d="number"==typeof(null==(c=b.format)?void 0:c.strokeWidth)?b.format.strokeWidth:u;0<d&&(a.lineWidth=d,a.strokeText(b.word.text,b.x,b.y));b.format&&a.restore()}})}),c.debug)e=j+f,
r=p+g,d="right"===c.align?e:"left"===c.align?j:j+f/2,v=p,"bottom"===c.vAlign?v=r:"middle"===c.vAlign&&(v=p+g/2),a.lineWidth=3,a.strokeStyle="#f44336",a.strokeRect(j,p,f,g),a.lineWidth=1,(!c.align||"center"===c.align)&&(a.strokeStyle="#f44336",a.beginPath(),a.moveTo(d,p),a.lineTo(d,r),a.stroke()),(!c.vAlign||"middle"===c.vAlign)&&(a.strokeStyle="#f44336",a.beginPath(),a.moveTo(j,v),a.lineTo(e,v),a.stroke());return a.restore(),{height:b}};d.getTextFormat=l;d.getTextHeight=function(a){return J(a.ctx,
a.text,a.style)};d.getTextStyle=n;d.getWordHeight=function(a){var b=a.word;return J(a.ctx,b.text,b.format&&n(b.format))};d.specToJson=function(a){return JSON.stringify(a,B)};d.splitText=function(b){var c=b.text;b=a(b,f);c=D(c);return R(g(g({},b),{},{words:c,inferWhitespace:!1})).lines.map(function(a){return a.map(function(a){return a.word.text}).join("")})};d.splitWords=R;d.textToWords=D;d.wordsToJson=function(a){return JSON.stringify(a,B)};Object.defineProperty(d,Symbol.toStringTag,{value:"Module"})};
"object"==("undefined"==typeof exports?"undefined":j(exports))&&"undefined"!=typeof module?l(exports):"function"==typeof define&&define.amd?define(["exports"],l):l((n="undefined"!=typeof globalThis?globalThis:n||self).textToCanvas={});!0;ig.textToCanvas=window.textToCanvas;ig.TextToCanvasMixin={defaultTextConfig:{fontSize:14,fontFamily:"Arial",fontColor:"#000",text:"",width:100,height:50,align:"center",vAlign:"middle",strokeColor:"#000",strokeWidth:0,justify:!1,inferWhitespace:!0,overflow:!0,debug:!1},
textOffset:{x:0,y:0},initText:function(a){ig.textToCanvas?(this.textConfig=ig.copy(this.defaultTextConfig),this.entityTextConfig&&ig.merge(this.textConfig,this.entityTextConfig),a&&ig.merge(this.textConfig,a),this.entityTextOffset&&ig.merge(this.textOffset,this.entityTextOffset),!this.textConfig.width&&this.size&&(this.textConfig.width=0.9*this.size.x),!this.textConfig.height&&this.size&&(this.textConfig.height=0.9*this.size.y),this._originalFontSize=this.textConfig.fontSize):console.error("TextToCanvas plugin is required for TextToCanvasMixin")},
setText:function(a){void 0!==a&&(this.textConfig.text=a)},updateTextConfig:function(a){ig.merge(this.textConfig,a);a.fontSize&&(this._originalFontSize=this.textConfig.fontSize)},drawText:function(){if(this.textConfig.text&&ig.textToCanvas){var a=ig.system.context,b=this.pos.y-ig.game.screen.y,b={x:ig.system.getDrawPos(this.pos.x-ig.game.screen.x+this.textOffset.x),y:ig.system.getDrawPos(b+this.textOffset.y),width:this.textConfig.width,height:this.textConfig.height},c;for(c in this.textConfig)"width"!==
c&&"height"!==c&&(b[c]=this.textConfig[c]);this.textConfig.fontFamily&&(b.fontFamily=this.textConfig.fontFamily);a.save();ig.textToCanvas.drawText(a,this.textConfig.text,b);a.restore()}}}});ig.baked=!0;ig.module("plugins.utils.colors").defines(function(){GameColors={RED:"red",BLUE:"blue",GREEN:"green",YELLOW:"yellow",PURPLE:"purple",ORANGE:"orange"};GameColors.PALETTE=[GameColors.RED,GameColors.BLUE,GameColors.GREEN,GameColors.YELLOW,GameColors.PURPLE,GameColors.ORANGE]});this.START_BRANDING_SPLASH;
ig.baked=!0;
ig.module("plugins.branding.splash").requires("impact.impact","impact.entity").defines(function(){ig.BrandingSplash=ig.Class.extend({init:function(){ig.game.spawnEntity(EntityBranding,0,0);console.log("spawn branding")}});EntityBranding=ig.Entity.extend({autoUpdateScale:!1,gravityFactor:0,size:{x:32,y:32},splash:new ig.Image("branding/splash1.png"),init:function(a,b,c){this.parent(a,b,c);320>=ig.system.width?(this.size.x=320,this.size.y=200):(this.size.x=480,this.size.y=240);this.pos.x=(ig.system.width-
this.size.x)/2;this.pos.y=-this.size.y-200;this.endPosY=(ig.system.height-this.size.y)/2;a=this.tween({pos:{y:this.endPosY}},0.5,{easing:ig.Tween.Easing.Bounce.EaseIn});b=this.tween({},2.5,{onComplete:function(){ig.game.director.loadLevel(ig.game.director.currentLevel)}});a.chain(b);a.start();this.currentAnim=this.anims.idle},createClickableLayer:function(){console.log("Build clickable layer");this.checkClickableLayer("branding-splash",_SETTINGS.Branding.Logo.Link,_SETTINGS.Branding.Logo.NewWindow)},
doesClickableLayerExist:function(a){for(k in dynamicClickableEntityDivs)if(k==a)return!0;return!1},checkClickableLayer:function(a,b,c){"undefined"==typeof wm&&(this.doesClickableLayerExist(a)?(ig.game.showOverlay([a]),$("#"+a).find("[href]").attr("href",b)):this.createClickableOutboundLayer(a,b,"media/graphics/misc/invisible.png",c))},createClickableOutboundLayer:function(a,b,c,d){var e=ig.$new("div");e.id=a;document.body.appendChild(e);e=$("#"+e.id);e.css("float","left");e.css("position","absolute");
if(ig.ua.mobile){var g=window.innerHeight/mobileHeight,j=window.innerWidth/mobileWidth;e.css("left",this.pos.x*j);e.css("top",this.pos.y*g);e.css("width",this.size.x*j);e.css("height",this.size.y*g)}else g=w/2-destW/2,j=h/2-destH/2,console.log(g,j),e.css("left",g+this.pos.x*multiplier),e.css("top",j+this.pos.y*multiplier),e.css("width",this.size.x*multiplier),e.css("height",this.size.y*multiplier);d?e.html("<a target='_blank' href='"+b+"'><img style='width:100%;height:100%' src='"+c+"'></a>"):e.html("<a href='"+
b+"'><img style='width:100%;height:100%' src='"+c+"'></a>");dynamicClickableEntityDivs[a]={};dynamicClickableEntityDivs[a].width=this.size.x*multiplier;dynamicClickableEntityDivs[a].height=this.size.y*multiplier;dynamicClickableEntityDivs[a].entity_pos_x=this.pos.x;dynamicClickableEntityDivs[a].entity_pos_y=this.pos.y},draw:function(){ig.system.context.fillStyle="#ffffff";ig.system.context.fillRect(0,0,ig.system.width,ig.system.height);ig.system.context.fillStyle="#000";ig.system.context.font="12px Arial";
ig.system.context.textAlign="left";320>=ig.system.width?ig.system.context.fillText("powered by MarketJS.com",ig.system.width-150,ig.system.height-15):ig.system.context.fillText("powered by MarketJS.com",ig.system.width-160,ig.system.height-15);this.parent();this.splash&&ig.system.context.drawImage(this.splash.data,0,0,this.splash.data.width,this.splash.data.height,this.pos.x,this.pos.y,this.size.x,this.size.y)}})});this.END_BRANDING_SPLASH;ig.baked=!0;
ig.module("game.entities.buttons.button").requires("impact.entity","plugins.data.vector").defines(function(){EntityButton=ig.Entity.extend({collides:ig.Entity.COLLIDES.NEVER,type:ig.Entity.TYPE.A,size:new Vector2(48,48),fillColor:null,zIndex:95E3,init:function(a,b,c){this.parent(a,b,c);!ig.global.wm&&!isNaN(c.zIndex)&&(this.zIndex=c.zIndex);a=Math.floor(256*Math.random());b=Math.floor(256*Math.random());c=Math.floor(256*Math.random());this.fillColor="rgba("+a+","+c+","+b+",1)"},clicked:function(){console.warn("no implementation on clicked()")},
clicking:function(){console.warn("no implementation on clicking()")},released:function(){console.warn("no implementation on released()")}})});ig.baked=!0;
ig.module("plugins.clickable-div-layer").requires("plugins.data.vector").defines(function(){ClickableDivLayer=ig.Class.extend({version:"1.0.0",pos:new Vector2(0,0),size:new Vector2(0,0),identifier:null,invisImagePath:"media/graphics/misc/invisible.png",init:function(a){this.pos=new Vector2(a.pos.x,a.pos.y);this.size=new Vector2(a.size.x,a.size.y);var b="more-games",c="https://www.marketjs.com",d=!1;a.div_layer_name&&(b=a.div_layer_name);a.link&&(c=a.link);a.newWindow&&(d=a.newWindow);this.createClickableLayer(b,
c,d)},createClickableLayer:function(a,b,c){this.identifier=a;var d=ig.domHandler.getElementById("#"+a);d?(ig.domHandler.show(d),ig.domHandler.attr(d,"href",b)):this.createClickableOutboundLayer(a,b,this.invisImagePath,c)},update:function(a,b,c,d){this.pos.x===a&&this.pos.y===b&&this.size.x===c&&this.size.y===d||(this.pos.x=a,this.pos.y=b,this.size.x=c,this.size.y=d,ig.sizeHandler.dynamicClickableEntityDivs[this.identifier]={},ig.sizeHandler.dynamicClickableEntityDivs[this.identifier].width=this.size.x,
ig.sizeHandler.dynamicClickableEntityDivs[this.identifier].height=this.size.y,ig.sizeHandler.dynamicClickableEntityDivs[this.identifier].entity_pos_x=this.pos.x,ig.sizeHandler.dynamicClickableEntityDivs[this.identifier].entity_pos_y=this.pos.y,a=ig.domHandler.getElementById("#"+this.identifier),this.updateCSS(a))},createClickableOutboundLayer:function(a,b,c,d){var e=ig.domHandler.create("div");ig.domHandler.attr(e,"id",a);var g=ig.domHandler.create("a");ig.domHandler.addEvent(e,"mousedown",function(a){a.preventDefault()});
d?(ig.domHandler.attr(g,"href",b),ig.domHandler.attr(g,"target","_blank")):ig.domHandler.attr(g,"href",b);b=ig.domHandler.create("img");ig.domHandler.css(b,{width:"100%",height:"100%"});ig.domHandler.attr(b,"src",c);ig.domHandler.addEvent(e,"mousemove",ig.input.mousemove.bind(ig.input),!1);ig.domHandler.appendChild(g,b);ig.domHandler.appendChild(e,g);ig.domHandler.appendToBody(e);ig.sizeHandler.dynamicClickableEntityDivs[a]={};ig.sizeHandler.dynamicClickableEntityDivs[a].width=this.size.x;ig.sizeHandler.dynamicClickableEntityDivs[a].height=
this.size.y;ig.sizeHandler.dynamicClickableEntityDivs[a].entity_pos_x=this.pos.x;ig.sizeHandler.dynamicClickableEntityDivs[a].entity_pos_y=this.pos.y;this.updateCSS(e)},updateCSS:function(a){var b=Math.min(ig.sizeHandler.scaleRatioMultiplier.x,ig.sizeHandler.scaleRatioMultiplier.y);if(ig.ua.mobile){var c=ig.domHandler.getElementById("#canvas"),d=ig.domHandler.getOffsets(c),c=d.left,d=d.top;if(ig.sizeHandler.disableStretchToFitOnMobileFlag)var c=Math.floor(c+this.pos.x*ig.sizeHandler.scaleRatioMultiplier.x)+
"px",d=Math.floor(d+this.pos.y*ig.sizeHandler.scaleRatioMultiplier.y)+"px",e=Math.floor(this.size.x*ig.sizeHandler.scaleRatioMultiplier.x)+"px",b=Math.floor(this.size.y*ig.sizeHandler.scaleRatioMultiplier.y)+"px";else c=Math.floor(this.pos.x*ig.sizeHandler.sizeRatio.x)+"px",d=Math.floor(this.pos.y*ig.sizeHandler.sizeRatio.y)+"px",e=Math.floor(this.size.x*ig.sizeHandler.sizeRatio.x)+"px",b=Math.floor(this.size.y*ig.sizeHandler.sizeRatio.y)+"px"}else c=ig.domHandler.getElementById("#canvas"),d=ig.domHandler.getOffsets(c),
c=d.left,d=d.top,ig.sizeHandler.enableStretchToFitOnDesktopFlag?(c=Math.floor(c+this.pos.x*ig.sizeHandler.sizeRatio.x)+"px",d=Math.floor(d+this.pos.y*ig.sizeHandler.sizeRatio.y)+"px",e=Math.floor(this.size.x*ig.sizeHandler.sizeRatio.x)+"px",b=Math.floor(this.size.y*ig.sizeHandler.sizeRatio.y)+"px"):(c=Math.floor(c+this.pos.x*b)+"px",d=Math.floor(d+this.pos.y*b)+"px",e=Math.floor(this.size.x*b)+"px",b=Math.floor(this.size.y*b)+"px");ig.domHandler.css(a,{"float":"left",position:"absolute",left:c,top:d,
width:e,height:b,"z-index":3})}})});ig.baked=!0;
ig.module("game.entities.buttons.button-branding-logo").requires("game.entities.buttons.button","plugins.clickable-div-layer").defines(function(){EntityButtonBrandingLogo=EntityButton.extend({type:ig.Entity.TYPE.A,gravityFactor:0,logo:new ig.AnimationSheet("branding/logo.png",_SETTINGS.Branding.Logo.Width,_SETTINGS.Branding.Logo.Height),zIndex:10001,size:{x:64,y:66},clickableLayer:null,link:null,newWindow:!1,div_layer_name:"branding-logo",name:"brandinglogo",init:function(a,b,c){this.parent(a,b,c);
if(!ig.global.wm){if("undefined"==typeof wm)if(_SETTINGS.Branding.Logo.Enabled)this.size.x=_SETTINGS.Branding.Logo.Width,this.size.y=_SETTINGS.Branding.Logo.Height,this.anims.idle=new ig.Animation(this.logo,0,[0],!0),this.currentAnim=this.anims.idle,c&&c.centralize&&(this.pos.x=ig.system.width/2-this.size.x/2,console.log("centralize true ... centering branded logo ...")),_SETTINGS.Branding.Logo.LinkEnabled&&(this.link=_SETTINGS.Branding.Logo.Link,this.newWindow=_SETTINGS.Branding.Logo.NewWindow,this.clickableLayer=
new ClickableDivLayer(this));else{this.kill();return}this.div_layer_name=c.div_layer_name?c.div_layer_name:"branding-logo"}},show:function(){var a=ig.domHandler.getElementById("#"+this.div_layer_name);ig.domHandler.show(a)},hide:function(){var a=ig.domHandler.getElementById("#"+this.div_layer_name);ig.domHandler.hide(a)},clicked:function(){},clicking:function(){},released:function(){}})});ig.baked=!0;
ig.module("game.entities.branding-logo-placeholder").requires("impact.entity","game.entities.buttons.button-branding-logo").defines(function(){EntityBrandingLogoPlaceholder=ig.Entity.extend({gravityFactor:0,size:{x:32,y:32},_wmDrawBox:!0,_wmBoxColor:"rgba(0, 0, 255, 0.7)",init:function(a,b,c){this.parent(a,b,c);if(c)switch(console.log("settings found ... using that div layer name"),a=c.div_layer_name,console.log("settings.centralize:",c.centralize),c.centralize){case "true":console.log("centralize true");
centralize=!0;break;case "false":console.log("centralize false");centralize=!1;break;default:console.log("default ... centralize false"),centralize=!1}else a="branding-logo",centralize=!1;if("undefined"==typeof wm){if(_SETTINGS.Branding.Logo.Enabled)try{ig.game.spawnEntity(EntityButtonBrandingLogo,this.pos.x,this.pos.y,{div_layer_name:a,centralize:centralize})}catch(d){console.log(d)}this.kill()}}})});ig.baked=!0;
ig.module("game.entities.text").requires("plugins.utils.entity-extended","plugins.utils.text-to-canvas").defines(function(){EntityText=ig.EntityExtended.extend({size:{x:1,y:1},alpha:1,visualScale:1,gravityFactor:0,collides:ig.Entity.COLLIDES.NEVER,useOffscreenCanvas:!0,offCanvas:null,offCanvasCTX:null,imageBitmap:null,needsRedraw:!0,_anchorTargetEntity:null,_targetAnchorFactor:{x:0,y:0},_selfAnchorFactor:{x:0,y:0},_anchorPixelOffset:{x:0,y:0},_isAnchored:!1,entityTextConfig:{text:"Text Entity",fontSize:16,
fontFamily:"Arial",fontColor:"#FFFFFF",width:100,height:30,align:"left",vAlign:"top",strokeColor:"#000000",strokeWidth:0,justify:!1,inferWhitespace:!0,overflow:!0,debug:!1,shadowEnabled:!1,shadowOffsetX:2,shadowOffsetY:2,shadowBlur:3,shadowColor:"rgba(0,0,0,0.5)",overflowAllowance:5},entityTextOffset:{x:0,y:0},isInvisible:!0,init:function(a,b,c){this.parent(a,b,c);!ig.textToCanvas||!ig.TextToCanvasMixin?console.error("TextToCanvas plugin and mixin are required for EntityText"):(c&&"undefined"!==typeof c.useOffscreenCanvas&&
(this.useOffscreenCanvas=c.useOffscreenCanvas),this.initText(c?c.textConfig:null),this.textConfig.overflowAllowance=Number(this.textConfig.overflowAllowance)||0,c&&c.textOffset&&ig.merge(this.textOffset,c.textOffset),c&&"undefined"!==typeof c.alpha&&(this.alpha=c.alpha),c&&"undefined"!==typeof c.visualScale&&(this.visualScale=c.visualScale),this.useOffscreenCanvas&&this.initOffscreenCanvas())},initOffscreenCanvas:function(){if(this.useOffscreenCanvas){var a="undefined"!==typeof OffscreenCanvas&&"undefined"!==
typeof createImageBitmap&&"undefined"!==typeof ImageBitmap,b=2*this.textConfig.overflowAllowance,c=this.textConfig.width+b,b=this.textConfig.height+b;if(!a&&"undefined"!==typeof document)this.offCanvas=document.createElement("canvas");else if(a)this.offCanvas=new OffscreenCanvas(c,b);else{this.useOffscreenCanvas=!1;return}this.offCanvas?(this.offCanvas.width=c,this.offCanvas.height=b,this.offCanvasCTX=this.offCanvas.getContext("2d"),void 0!==this.offCanvasCTX.textRendering&&(this.offCanvasCTX.textRendering=
"optimizeSpeed")):this.useOffscreenCanvas=!1}},redrawOffscreenCanvas:function(){if(this.useOffscreenCanvas&&this.offCanvasCTX){var a=2*this.textConfig.overflowAllowance,b=this.textConfig.width+a,a=this.textConfig.height+a;if(this.offCanvas.width!==b||this.offCanvas.height!==a)this.offCanvas.width=b,this.offCanvas.height=a;this.offCanvasCTX.clearRect(0,0,this.offCanvas.width,this.offCanvas.height);this.drawTextToOffscreen();this.createImageBitmap();this.needsRedraw=!1}},_applyShadowStyles:function(a){this.textConfig.shadowEnabled&&
(a.shadowOffsetX=this.textConfig.shadowOffsetX||0,a.shadowOffsetY=this.textConfig.shadowOffsetY||0,a.shadowBlur=this.textConfig.shadowBlur||0,a.shadowColor=this.textConfig.shadowColor||"rgba(0,0,0,0)")},_clearShadowStyles:function(a){this.textConfig.shadowEnabled&&(a.shadowOffsetX=0,a.shadowOffsetY=0,a.shadowBlur=0,a.shadowColor="rgba(0,0,0,0)")},drawTextToOffscreen:function(){if(this.textConfig&&this.textConfig.text&&ig.textToCanvas&&this.offCanvasCTX){var a={x:this.textConfig.overflowAllowance,
y:this.textConfig.overflowAllowance,width:this.textConfig.width,height:this.textConfig.height},b;for(b in this.textConfig)this.textConfig.hasOwnProperty(b)&&!a.hasOwnProperty(b)&&(a[b]=this.textConfig[b]);this.offCanvasCTX.save();this._applyShadowStyles(this.offCanvasCTX);ig.textToCanvas.drawText(this.offCanvasCTX,this.textConfig.text,a);this._clearShadowStyles(this.offCanvasCTX);this.offCanvasCTX.restore()}},createImageBitmap:function(){var a=this;a.useOffscreenCanvas&&a.offCanvas&&(a.imageBitmap&&
"function"===typeof a.imageBitmap.close&&(a.imageBitmap.close(),a.imageBitmap=null),"undefined"!==typeof createImageBitmap?createImageBitmap(a.offCanvas).then(function(b){a.imageBitmap=b}).catch(function(b){console.error("EntityText: Error creating ImageBitmap.",b);a.imageBitmap=a.offCanvas}):a.imageBitmap=a.offCanvas)},_parseAnchorSpec:function(a,b){b=b||{xFactor:0,yFactor:0};if("string"===typeof a){var c=a.toLowerCase().replace(/\s+/g,""),d={xFactor:0.5,yFactor:0.5};-1!==c.indexOf("left")?d.xFactor=
0:-1!==c.indexOf("right")&&(d.xFactor=1);-1!==c.indexOf("top")?d.yFactor=0:-1!==c.indexOf("bottom")&&(d.yFactor=1);if("top"===c||"bottom"===c)d.xFactor=0.5;if("left"===c||"right"===c)d.yFactor=0.5;if("top-left"===c||"left-top"===c)d.xFactor=0,d.yFactor=0;if("top-center"===c||"center-top"===c)d.xFactor=0.5,d.yFactor=0;if("top-right"===c||"right-top"===c)d.xFactor=1,d.yFactor=0;if("left-middle"===c||"middle-left"===c)d.xFactor=0,d.yFactor=0.5;if("center-middle"===c||"middle-center"===c||"center"===
c||"middle"===c)d.xFactor=0.5,d.yFactor=0.5;if("right-middle"===c||"middle-right"===c)d.xFactor=1,d.yFactor=0.5;if("bottom-left"===c||"left-bottom"===c)d.xFactor=0,d.yFactor=1;if("bottom-center"===c||"center-bottom"===c)d.xFactor=0.5,d.yFactor=1;if("bottom-right"===c||"right-bottom"===c)d.xFactor=1,d.yFactor=1;return d}return a&&"number"===typeof a.x&&"number"===typeof a.y?{xFactor:(Math.max(-1,Math.min(1,a.x))+1)/2,yFactor:(Math.max(-1,Math.min(1,a.y))+1)/2}:b},anchorTo:function(a,b){a?(this._anchorTargetEntity=
a,b=b||{},this._targetAnchorFactor=this._parseAnchorSpec(b.targetAnchor,{xFactor:0.5,yFactor:0.5}),this._selfAnchorFactor=this._parseAnchorSpec(b.selfAnchor,{xFactor:0.5,yFactor:0.5}),this._anchorPixelOffset=b.offset&&"number"===typeof b.offset.x&&"number"===typeof b.offset.y?b.offset:{x:0,y:0},this._isAnchored=!0,this._updateAnchorPosition()):(console.error("EntityText.anchorTo: Target entity is null or undefined."),this.unanchor())},unanchor:function(){this._isAnchored=!1;this._anchorTargetEntity=
null},_updateAnchorPosition:function(){if(!this._isAnchored||!this._anchorTargetEntity||this._anchorTargetEntity._killed)this._anchorTargetEntity&&this._anchorTargetEntity._killed&&this.unanchor();else{var a=this._anchorTargetEntity.pos.y+this._anchorTargetEntity.size.y*this._targetAnchorFactor.yFactor,b=this.textConfig.height*this._selfAnchorFactor.yFactor;this.pos.x=this._anchorTargetEntity.pos.x+this._anchorTargetEntity.size.x*this._targetAnchorFactor.xFactor-this.textConfig.width*this._selfAnchorFactor.xFactor+
this._anchorPixelOffset.x;this.pos.y=a-b+this._anchorPixelOffset.y}},update:function(){this._isAnchored&&this._updateAnchorPosition();this.parent()},draw:function(){if(!(0>=this.alpha||!this.textConfig||!this.textConfig.text||0===this.visualScale)){var a=ig.system.context;if(this.useOffscreenCanvas)if(this.needsRedraw&&this.redrawOffscreenCanvas(),!this.imageBitmap&&this.offCanvas&&(this.imageBitmap=this.offCanvas),this.imageBitmap){a.save();a.globalAlpha=this.alpha;var b,c;this._isAnchored&&this._anchorTargetEntity?
(b=this.textConfig.width*this._selfAnchorFactor.xFactor,c=this.textConfig.height*this._selfAnchorFactor.yFactor):(b=this.textConfig.width/2,c=this.textConfig.height/2);var d=ig.system.getDrawPos(this.pos.x+b-ig.game.screen.x),e=ig.system.getDrawPos(this.pos.y+c-ig.game.screen.y);a.translate(d,e);a.scale(this.visualScale,this.visualScale);a.drawImage(this.imageBitmap,-b-this.textConfig.overflowAllowance,-c-this.textConfig.overflowAllowance);a.restore()}else this.drawDirectly(a);else this.drawDirectly(a)}},
drawDirectly:function(a){a.save();a.globalAlpha=this.alpha;this._applyShadowStyles(a);var b={},c;for(c in this.textConfig)this.textConfig.hasOwnProperty(c)&&(b[c]=this.textConfig[c]);b.x=ig.system.getDrawPos(this.pos.x-ig.game.screen.x+this.textOffset.x);b.y=ig.system.getDrawPos(this.pos.y-ig.game.screen.y+this.textOffset.y);ig.textToCanvas.drawText(a,this.textConfig.text,b);this._clearShadowStyles(a);a.restore()},setPosition:function(a,b){this.pos.x=a;this.pos.y=b},setTextContent:function(a){"number"===
typeof a&&(a=a.toString());"string"===typeof a&&this.textConfig.text!==a&&(this.textConfig.text=a,this.needsRedraw=!0)},configureText:function(a){var b=!1,c=this.textConfig.fontSize,d;for(d in a)if(a.hasOwnProperty(d)&&this.textConfig[d]!==a[d]){b=!0;break}!b&&(a.width&&this.textConfig.width!==a.width)&&(b=!0);!b&&(a.height&&this.textConfig.height!==a.height)&&(b=!0);b&&(ig.merge(this.textConfig,a),a.fontSize&&a.fontSize!==c&&(this._originalFontSize=this.textConfig.fontSize),this.needsRedraw=!0)},
setTextScale:function(a){this.visualScale!==a&&(this.visualScale=a)},updateAlpha:function(a){a=Math.max(0,Math.min(1,a));this.alpha!==a&&(this.alpha=a)},kill:function(){this.imageBitmap&&"function"===typeof this.imageBitmap.close&&(this.imageBitmap.close(),this.imageBitmap=null);this.offCanvasCTX=this.offCanvas=null;this.unanchor();this.parent()}});ig.TextToCanvasMixin?EntityText.inject(ig.TextToCanvasMixin):console.error("TextToCanvasMixin not found for EntityText injection.")});ig.baked=!0;
ig.module("plugins.utils.buttons.button-base").requires("plugins.utils.entity-extended","game.entities.text").defines(function(){EntityButtonBase=ig.EntityExtended.extend({idleSheetInfo:{sheetImage:new ig.Image("media/graphics/sprites/ui/btn-blank.png"),frameCountX:1,frameCountY:1},_zIndex:1,onClickCallback:function(){},buttonScale:1,isClickable:!0,STATES:{NORMAL:0,CLICKED:1},state:-1,alpha:1,hidden:!1,buttonTextConfig:{fontSize:32,fontFamily:"Arial",fontColor:"#000000",text:"Button",align:"center",
vAlign:"middle",strokeColor:"#000000",strokeWidth:0,justify:!1},buttonTextOffset:{x:0,y:0},hasText:!0,textEntity:null,growTween:null,endGrowOnClick:!0,singleClickOnly:!1,hasBeenClicked:!1,parentEntity:null,useOffscreenCanvas:!0,offCanvas:null,offCanvasCTX:null,imageBitmap:null,needsRedraw:!0,init:function(a,b,c){this.zIndex=c.zIndex||ig.game.LAYERS.UI;c&&void 0!==c.useOffscreenCanvas&&(this.useOffscreenCanvas=c.useOffscreenCanvas);c&&c.buttonTextConfig&&ig.merge(this.buttonTextConfig,c.buttonTextConfig);
c&&c.buttonTextOffset&&ig.merge(this.buttonTextOffset,c.buttonTextOffset);this.parent(a,b,c);this.idleSheetInfo&&this.idleSheetInfo.sheetImage?(this.idleSheet=new ig.AnimationSheet(this.idleSheetInfo.sheetImage.path,this.size.x,this.size.y),this.currentAnim=this.idle=new ig.Animation(this.idleSheet,1,[0],!0)):console.warn("EntityButtonBase: idleSheetInfo or sheetImage not provided. Button background may not render.");this.state=this.STATES.NORMAL;this.hasText&&(a=ig.copy(this.buttonTextConfig),a.width=
a.width||this.size.x,a.height=a.height||this.size.y,(this.textEntity=ig.game.spawnEntity(EntityText,this.pos.x,this.pos.y,{textConfig:a,alpha:this.alpha,zIndex:this.zIndex+1}))&&this.textEntity.anchorTo(this,{targetAnchor:{x:0,y:0},selfAnchor:"center-middle",offset:this.buttonTextOffset}));this.onClick=new ig.Signal;this.onClickCallback&&"function"===typeof this.onClickCallback&&this.onClick.add(this.onClickCallback,this);this.useOffscreenCanvas&&this.initOffscreenCanvas();ig.game.sortEntitiesDeferred()},
initOffscreenCanvas:function(){if(this.useOffscreenCanvas){var a="undefined"!==typeof OffscreenCanvas&&"undefined"!==typeof createImageBitmap&&"undefined"!==typeof ImageBitmap;if(!a&&"undefined"!==typeof document)this.offCanvas=document.createElement("canvas");else if(a)this.offCanvas=new OffscreenCanvas(this.size.x,this.size.y);else{this.useOffscreenCanvas=!1;return}this.offCanvas?(this.offCanvas.width=this.size.x,this.offCanvas.height=this.size.y,this.offCanvasCTX=this.offCanvas.getContext("2d"),
this.redrawOffscreenCanvas()):this.useOffscreenCanvas=!1}},redrawOffscreenCanvas:function(){this.useOffscreenCanvas&&this.offCanvasCTX&&(this.offCanvasCTX.clearRect(0,0,this.offCanvas.width,this.offCanvas.height),this.drawButtonToOffscreen(),this.createImageBitmapFromButton(),this.needsRedraw=!1)},drawButtonToOffscreen:function(){if(this.currentAnim&&this.currentAnim.sheet&&this.currentAnim.sheet.image&&this.offCanvasCTX){var a=this.currentAnim.tile,b=this.currentAnim.sheet,c=b.width,b=b.height;this.offCanvasCTX.drawImage(this.currentAnim.sheet.image.data,
a%this.idleSheetInfo.frameCountX*c,a%this.idleSheetInfo.frameCountY*b,c,b,0,0,this.size.x,this.size.y)}},createImageBitmapFromButton:function(){var a=this;a.useOffscreenCanvas&&a.offCanvas&&(a.imageBitmap&&"function"===typeof a.imageBitmap.close&&(a.imageBitmap.close(),a.imageBitmap=null),"undefined"!==typeof createImageBitmap?createImageBitmap(a.offCanvas).then(function(b){a.imageBitmap=b}).catch(function(b){console.error("EntityButtonBase: Error creating ImageBitmap for button background.",b);a.imageBitmap=
a.offCanvas}):a.imageBitmap=a.offCanvas)},updateButtonLabelText:function(a){this.textEntity?this.textEntity.setTextContent(a):this.hasText&&(console.warn("EntityButtonBase: Attempted to update text, but textEntity is missing."),this.buttonTextConfig.text=a)},updateButtonLabelConfig:function(a){this.textEntity?this.textEntity.configureText(a):this.hasText&&(console.warn("EntityButtonBase: Attempted to configure text, but textEntity is missing."),ig.merge(this.buttonTextConfig,a))},setButtonLabelScale:function(a){this.textEntity&&
this.textEntity.setTextScale(a)},clicked:function(){if(!(this.disabled||this.singleClickOnly&&this.hasBeenClicked))if(!this.parentEntity||!this.parentEntity.isTweening)this.growTween&&(this.endGrowOnClick?(this.growTween.stop(),this.growTween=null):this.growTween.pause()),this.state=this.STATES.CLICKED,this.buttonScale=0.98,this.updateButtonVisualScale()},released:function(){if(this.state===this.STATES.CLICKED&&!(this.disabled||this.singleClickOnly&&this.hasBeenClicked))if(!this.parentEntity||!this.parentEntity.isTweening){if(this.growTween){var a=
this;this.delayedCall(0.5,function(){a.growTween&&a.growTween.resume()})}this.state=this.STATES.NORMAL;this.buttonScale=1;this.updateButtonVisualScale();a=this;this.tween({},0.01,{onComplete:function(){a.onClick&&a.onClick.dispatch(a);a.singleClickOnly&&(a.hasBeenClicked=!0,a.disable())}}).start()}},releasedOutside:function(){this.state=this.STATES.NORMAL;this.buttonScale=1;this.updateButtonVisualScale()},hide:function(){this.hidden||(this.disable(),this.hidden=!0,this.alpha=0,this.textEntity&&this.textEntity.updateAlpha(0))},
show:function(){this.hidden&&(this.enable(),this.hidden=!1,this.alpha=1,this.textEntity&&this.textEntity.updateAlpha(1))},disable:function(){this.disabled||(this.disabled=!0,this.isClickable=!1,this.growTween&&this.growTween.pause())},enable:function(){this.disabled&&(this.disabled=!1,this.isClickable=!0,this.growTween&&this.growTween.resume())},grow:function(a,b){var c=a?Infinity:0,d={scale:1},e=this;this.growTween=(new ig.TweenDef(d)).easing(ig.Tween.Easing.Back.EaseOut).to({scale:1.075},b||200).repeat(c).yoyo(!0).onUpdate(function(){e.buttonScale=
d.scale;e.updateButtonVisualScale()}).start()},updateButtonVisualScale:function(){this.textEntity&&this.textEntity.setTextScale(this.buttonScale);!this.useOffscreenCanvas&&this.currentAnim&&(this.currentAnim.scale.x=this.buttonScale,this.currentAnim.scale.y=this.buttonScale)},updateAlpha:function(a){this.alpha=a;this.textEntity&&this.textEntity.updateAlpha(a);!this.useOffscreenCanvas&&this.currentAnim&&(this.currentAnim.alpha=a)},onDrop:function(){this.releasedOutside();this.parent()},draw:function(){if(!this.hidden&&
this.currentAnim&&!(0>=this.alpha)){var a=ig.system.context;if(this.useOffscreenCanvas&&this.imageBitmap)if(this.needsRedraw&&this.redrawOffscreenCanvas(),!this.imageBitmap&&this.offCanvas&&(this.imageBitmap=this.offCanvas),this.imageBitmap){a.save();a.globalAlpha=this.alpha;var b=this.pos.y-ig.game.screen.y,c=ig.system.getDrawPos(this.pos.x-ig.game.screen.x),b=ig.system.getDrawPos(b);a.translate(c+this.size.x/2,b+this.size.y/2);a.scale(this.buttonScale,this.buttonScale);a.translate(-this.size.x/
2,-this.size.y/2);a.drawImage(this.imageBitmap,0,0);a.restore()}else this.currentAnim&&this.currentAnim.draw(this.pos.x-ig.game.screen.x,this.pos.y-ig.game.screen.y);else this.currentAnim&&(this.currentAnim.alpha=this.alpha,this.currentAnim.draw(this.pos.x-ig.game.screen.x,this.pos.y-ig.game.screen.y))}},update:function(){this.parent();ig.input.released("click")&&(this.state===this.STATES.CLICKED&&!this.underPointer())&&this.releasedOutside();this.growTween&&(this.underPointer()?this.growTween._isPlaying&&
(this.growTween.pause(),this.buttonScale=1,this.updateButtonVisualScale()):!this.growTween._isPlaying&&!this.disabled&&this.growTween.resume());this.textEntity&&this.textEntity.alpha!==this.alpha&&this.textEntity.updateAlpha(this.alpha)},kill:function(){this.imageBitmap&&"function"===typeof this.imageBitmap.close&&(this.imageBitmap.close(),this.imageBitmap=null);this.offCanvasCTX=this.offCanvas=null;this.textEntity&&(this.textEntity.kill(),this.textEntity=null);this.parent()}})});ig.baked=!0;
ig.module("game.entities.buttons.button-more-games").requires("plugins.utils.buttons.button-base","plugins.clickable-div-layer").defines(function(){EntityButtonMoreGames=EntityButtonBase.extend({type:ig.Entity.TYPE.A,idleSheetInfo:{sheetImage:new ig.Image("media/graphics/sprites/ui/btn-more-games.png"),frameCountX:1,frameCountY:1},buttonTextConfig:{fontSize:60,fontFamily:"bebasneue-bold",fontColor:"#fff",text:_STRINGS.Game.Buttons.MoreGames,align:"left",vAlign:"bottom"},hasText:!0,buttonTextOffset:{x:0,
y:-8},clickableLayer:null,link:null,newWindow:!1,div_layer_name:"more-games",name:"moregames",init:function(a,b,c){this.parent(a,b,c);ig.global.wm||(this.div_layer_name=c.div_layer_name?c.div_layer_name:"more-games",_SETTINGS.MoreGames.Enabled?(this.currentAnim=this.idle=new ig.Animation(this.idleSheet,1,[0],!0),_SETTINGS.MoreGames.Link&&(this.link=_SETTINGS.MoreGames.Link),_SETTINGS.MoreGames.NewWindow&&(this.newWindow=_SETTINGS.MoreGames.NewWindow),this.clickableLayer=new ClickableDivLayer(this),
this.buttonTextOffset.x=0.25*this.size.x,this.textEntity.anchorTo(this,{targetAnchor:{x:0,y:0},offset:this.buttonTextOffset})):this.kill())},show:function(){var a=ig.domHandler.getElementById("#"+this.div_layer_name);a&&ig.domHandler.show(a)},hide:function(){var a=ig.domHandler.getElementById("#"+this.div_layer_name);a&&ig.domHandler.hide(a)},disable:function(){this.parent();this.hide()},enable:function(){this.parent();this.show()}})});ig.baked=!0;
ig.module("game.entities.path-head").requires("impact.entity","plugins.math.sat").defines(function(){EntityPathHead=ig.Entity.extend({collides:ig.Entity.COLLIDES.NEVER,type:ig.Entity.TYPE.NONE,checkAgainst:ig.Entity.TYPE.NONE,_radius:0,satShape:null,init:function(a,b,c){this.parent(a,b,c);c&&"undefined"!==typeof c.radius?this._radius=c.radius:(console.warn("EntityPathHead initialized without a radius."),this._radius=10);this.updateSATShape(this.pos)},updateSATShape:function(a){this.pos.x=a.x;this.pos.y=
a.y;this.satShape=new ig.SAT.Circle(new ig.SAT.Vector2D(this.pos.x,this.pos.y),this._radius)},getRadius:function(){return this._radius},getSATShape:function(){return this.satShape},update:function(){},draw:function(){}})});ig.baked=!0;
ig.module("game.levels.level1").defines(function(){Level1MapData={id:"level1",title:"The Dockyard",image:"map-1",truckSpawnAreas:"top10 topRight right1 right2 right3 right4 right5".split(" "),buildings:[{name:"building1",type:1,vertices:[{x:555.01,y:-1.19},{x:1385.15,y:-2.37},{x:1382.78,y:246.3},{x:556.2,y:246.3}]},{name:"building2",type:1,vertices:[{x:1087.49,y:974.82},{x:1884.42,y:977.2},{x:1890.35,y:1078.81},{x:1086.3,y:1078.81}]},{name:"forklift-building1",type:1,vertices:[{x:477.92,y:310.71},
{x:741.2,y:308.34},{x:738.83,y:426.56},{x:474.37,y:425.37}]},{name:"forklift-building2",type:1,vertices:[{x:1774.13,y:1022.26},{x:1692.3,y:1020.7},{x:1688.75,y:823.43},{x:1773.84,y:824.61}]},{name:"box-building2",type:1,vertices:[{x:1158.64,y:901.3},{x:1248.77,y:901.3},{x:1246.4,y:974.45},{x:1158.64,y:974.45}]},{name:"crane",type:1,vertices:[{x:559.75,y:431.67},{x:642.77,y:450.65},{x:596.52,y:923.46},{x:412.7,y:879.58}]},{name:"containers",type:1,vertices:[{x:657,y:716.29},{x:902.48,y:721.04},{x:903.67,
y:937.69},{x:653.44,y:936.5}]},{name:"building-container",type:1,vertices:[{x:889.44,y:966.52},{x:964.15,y:966.52},{x:962.96,y:1078.81},{x:887.07,y:1077.63}]},{name:"bush1",type:1,vertices:[{x:1417.17,y:0},{x:1555.92,y:0.82},{x:1556.52,y:57.74},{x:1416.22,y:57.5}]},{name:"bush2",type:1,vertices:[{x:1848.84,y:887.07},{x:1918.81,y:885.88},{x:1918.81,y:983.94},{x:1851.22,y:981.57}]},{name:"parking-slot-1-building1",type:3,collidesWith:[4],vertices:[{x:814.73,y:251.65},{x:915.77,y:252.11},{x:915.53,y:484.79},
{x:815.56,y:486.21}],color:GameColors.RED,colorIndicator:{anchor:{x:0.5,y:1},anchorOffset:{x:0,y:0}},swapPoints:!0},{name:"parking-slot-2-building1",type:3,collidesWith:[4],vertices:[{x:1027.6,y:252.24},{x:1127.45,y:252.11},{x:1126.62,y:485.38},{x:1026.85,y:485.62}],color:GameColors.BLUE,colorIndicator:{anchor:{x:0.5,y:1},anchorOffset:{x:0,y:0}},swapPoints:!0},{name:"parking-slot-1-building2",type:3,collidesWith:[4],vertices:[{x:1324.08,y:741.63},{x:1424.72,y:741.5},{x:1424.68,y:970.82},{x:1326.49,
y:970.26}],color:GameColors.GREEN,colorIndicator:{anchor:{x:0.5,y:0},anchorOffset:{x:0,y:0}}},{name:"parking-slot-2-building2",type:3,collidesWith:[4],vertices:[{x:1536.75,y:741.63},{x:1635.02,y:741.5},{x:1636.57,y:972.4},{x:1536.79,y:972.63}],color:GameColors.YELLOW,colorIndicator:{anchor:{x:0.5,y:0},anchorOffset:{x:0,y:0}},swapPoints:!0}]}});ig.baked=!0;
ig.module("game.levels.level2").defines(function(){Level2MapData={id:"level2",title:"The Port",image:"map-2",truckSpawnAreas:"left5 bot5 right5 top6 top7 top8".split(" "),buildings:[{name:"building1",type:1,vertices:[{x:-0,y:942.8},{x:617.86,y:942.8},{x:617.86,y:1082.37},{x:0,y:1080}]},{name:"building2",type:1,vertices:[{x:694.95,y:473.18},{x:1099.35,y:296.48},{x:1214.38,y:554.64},{x:808.8,y:730.15}]},{name:"building-3",type:1,vertices:[{x:1723.53,y:20.95},{x:1920,y:22.14},{x:1920.4,y:558.59},{x:1722.74,
y:556.22}]},{name:"side-containers",type:1,vertices:[{x:26.09,y:405.58},{x:249.04,y:394.91},{x:247.86,y:498.9},{x:27.28,y:507.2}]},{name:"middle-container",type:1,vertices:[{x:113.85,y:412.7},{x:154.17,y:413.89},{x:154.17,y:516.69},{x:113.85,y:516.69}]},{name:"small-box-building1",type:1,vertices:[{x:539.59,y:923.83},{x:594.14,y:923.83},{x:595.33,y:964.97},{x:540.78,y:966.15}]},{name:"stacked-box-building1",type:1,vertices:[{x:626.16,y:984.31},{x:702.06,y:984.31},{x:704.43,y:1062.21},{x:628.54,y:1062.21}]},
{name:"forklift-building-2",type:1,vertices:[{x:626.16,y:543.15},{x:689.02,y:514.69},{x:763.73,y:688.65},{x:700.88,y:720.67}]},{name:"containers-building-2",type:1,vertices:[{x:1127.81,y:324.94},{x:1168.13,y:309.52},{x:1253.51,y:503.64},{x:1212.01,y:517.88}]},{name:"boxes-building-3",type:1,vertices:[{x:1563.04,y:3.56},{x:1759.9,y:3.56},{x:1758.72,y:99.25},{x:1565.41,y:102.8}]},{name:"crane-top",type:1,vertices:[{x:304.78,y:310.71},{x:383.05,y:263.27},{x:436.42,y:344.73},{x:358.15,y:392.17}]},{name:"water-top-1",
type:1,vertices:[{x:0,y:0},{x:645.14,y:-1.19},{x:394.32,y:253.6},{x:243.11,y:354.22},{x:0,y:362.52}]},{name:"water-bot-1",type:1,vertices:[{x:1244.5,y:972.45},{x:1320.99,y:910.07},{x:1416.4,y:884.34},{x:1594.08,y:870.05},{x:1795.94,y:831.24},{x:1921.19,y:768.47},{x:1920,y:1080},{x:1223.87,y:1081.19}]},{name:"parking-slot-1-building1",type:3,collidesWith:[4],vertices:[{x:125.71,y:707.52},{x:226.27,y:707.76},{x:224.61,y:940.3},{x:125.94,y:940.06}],color:GameColors.RED,colorIndicator:{anchor:{x:0.5,
y:0},anchorOffset:{x:0,y:0}}},{name:"parking-slot-2-building1",type:3,collidesWith:[4],vertices:[{x:337.27,y:707.99},{x:437.37,y:708.23},{x:437.13,y:942.2},{x:337.04,y:941.48}],color:GameColors.BLUE,colorIndicator:{anchor:{x:0.5,y:0},anchorOffset:{x:0,y:0}}},{name:"parking-slot-1-building2",type:3,collidesWith:[4],vertices:[{x:748.79,y:173.74},{x:840.58,y:134.84},{x:932.25,y:349.24},{x:842.24,y:388.25}],color:GameColors.GREEN,colorIndicator:{anchor:{x:0.5,y:0},anchorOffset:{x:0,y:0}}},{name:"parking-slot-2-building2",
type:3,collidesWith:[4],vertices:[{x:961.66,y:662.33},{x:1053.45,y:621.66},{x:1140.38,y:823.01},{x:1047.4,y:863.81}],color:GameColors.YELLOW,colorIndicator:{anchor:{x:0.5,y:0},anchorOffset:{x:0,y:0}}},{name:"parking-slot-1-building3",type:3,collidesWith:[4],vertices:[{x:1489.39,y:235.6},{x:1489.87,y:134.64},{x:1723.85,y:134.98},{x:1722.78,y:234.09}],color:GameColors.PURPLE,colorIndicator:{anchor:{x:0,y:0.5},anchorOffset:{x:0,y:0}}},{name:"parking-slot-2-building3",type:3,collidesWith:[4],vertices:[{x:1489.79,
y:446.3},{x:1489.47,y:346.13},{x:1722.66,y:346.08},{x:1723.18,y:445.97}],color:GameColors.ORANGE,colorIndicator:{anchor:{x:0,y:0.5},anchorOffset:{x:0,y:0}}}]}});ig.baked=!0;
ig.module("game.levels.level3").defines(function(){Level3MapData={id:"level3",title:"The Bridge",image:"map-3",truckSpawnAreas:"topLeft top1 top10 topRight left1 botLeft right1 botRight bot1 bot10".split(" "),buildings:[{name:"building1",type:1,vertices:[{x:608.38,y:0},{x:1361.43,y:0},{x:1374.48,y:475.18},{x:609.56,y:488.23}]},{name:"building2",type:1,vertices:[{x:684.27,y:801.68},{x:1317.55,y:801.68},{x:1321.11,y:1076.44},{x:677.16,y:1080}]},{name:"middle-river",type:1,vertices:[{x:309.52,y:442.35},
{x:659.37,y:442.35},{x:1437.33,y:474},{x:1600.4,y:436.64},{x:1596.24,y:809.61},{x:1404.72,y:773.03},{x:682.79,y:750},{x:311.16,y:810.82}]},{name:"left-river",type:1,vertices:[{x:-0,y:291.74},{x:107.92,y:396.1},{x:109.1,y:825.03},{x:1.19,y:852.3}]},{name:"right-river",type:1,vertices:[{x:1799.04,y:366.45},{x:1917.63,y:300.04},{x:1918.81,y:806.05},{x:1801.41,y:804.87}]},{name:"bush-bottom",type:1,vertices:[{x:354.59,y:764.92},{x:431.67,y:750.69},{x:448.28,y:816.73},{x:409.14,y:849.93},{x:355.78,y:826.99}]},
{name:"crane",type:1,vertices:[{x:602.45,y:730.53},{x:686.65,y:761.36},{x:610.75,y:832.14},{x:547.89,y:776}]},{name:"box-bottom",type:1,vertices:[{x:1316.37,y:787.45},{x:1414.8,y:787.45},{x:1417.17,y:866.53},{x:1317.55,y:868.5}]},{name:"parking-slot-1-building1",type:3,collidesWith:[4],vertices:[{x:370.99,y:49.02},{x:604.82,y:49.41},{x:604.82,y:149.05},{x:370.8,y:149.44}],color:GameColors.RED,colorIndicator:{anchor:{x:0.5,y:0},anchorOffset:{x:0,y:0}}},{name:"parking-slot-2-building1",type:3,collidesWith:[4],
vertices:[{x:1364,y:48.23},{x:1598.62,y:48.62},{x:1597.83,y:149.05},{x:1364.6,y:149.04}],color:GameColors.BLUE,colorIndicator:{anchor:{x:0.5,y:0},anchorOffset:{x:0,y:0}},swapPoints:!0},{name:"parking-slot-1-building2",type:3,collidesWith:[4],vertices:[{x:443.73,y:956.64},{x:675.97,y:957.83},{x:675.58,y:1056.68},{x:443.53,y:1057.46}],color:GameColors.GREEN,colorIndicator:{anchor:{x:0.5,y:0},anchorOffset:{x:0,y:0}}},{name:"parking-slot-2-building2",type:3,collidesWith:[4],vertices:[{x:1325.46,y:957.23},
{x:1559.48,y:956.64},{x:1558.49,y:1056.68},{x:1325.86,y:1056.27}],color:GameColors.YELLOW,colorIndicator:{anchor:{x:0.5,y:0},anchorOffset:{x:0,y:0}}}]}});ig.baked=!0;
ig.module("game.data.level-data").requires("impact.impact","game.levels.level1","game.levels.level2","game.levels.level3").defines(function(){LevelData=ig.Class.extend({statics:{COLLISION_TYPES:{NONE:0,BUILDING:1,BOUNDARY:2,PARKING_SLOT:3,TRUCK:4}},levels:{},mapImageList:{"map-1":new ig.Image("media/graphics/sprites/maps/map-1.png"),"map-2":new ig.Image("media/graphics/sprites/maps/map-2.png"),"map-3":new ig.Image("media/graphics/sprites/maps/map-3.png")},init:function(){this.levels.level1=Level1MapData;
this.levels.level2=Level2MapData;this.levels.level3=Level3MapData;LevelData.COLLISION_TYPES=this.statics.COLLISION_TYPES;console.log("LevelData manager initialized with level IDs:",Object.keys(this.levels))},getLevelData:function(a){return!this.levels[a]?(console.error("LevelData Error: No data found for levelId:",a),null):this.levels[a]},getMapImage:function(a){var b=this.getLevelData(a);if(b&&b.image&&ig.game&&this.mapImageList&&this.mapImageList[b.image])return this.mapImageList[b.image];console.warn("LevelData Warning: Could not retrieve map image for levelId:",
a,". Image key:",b?b.image:"N/A",". Check if image is preloaded in this.mapImageList.");return null}})});ig.baked=!0;
ig.module("game.entities.warning-signal").requires("plugins.utils.entity-extended","plugins.tweens-handler").defines(function(){EntityWarningSignal=ig.EntityExtended.extend({size:{x:50,y:50},zIndex:1,idleSheetInfo:{sheetImage:new ig.Image("media/graphics/sprites/ui/warning.png"),frameCountX:1,frameCountY:1},_blinkAlpha:0,isBlinking:!1,blinkCount:6,blinkDuration:1,direction:-1,_activeTween:null,init:function(a,b,c){this.parent(a,b,c);this.zIndex=ig.game.LAYERS.FOREGROUND;this.ctx=ig.system.context;
this.blinkCount=c.blinkCount||this.blinkCount;this.blinkDuration=c.blinkDuration||this.blinkDuration;this.anims.idle=new ig.Animation(this.idleSheet,1,[0],!0);this.currentAnim=this.anims.idle;switch(c.direction){case 0:this.pos.x=10;this.pos.y-=this.size.y/2;break;case 1:this.pos.x-=this.size.x/2;this.pos.y=10;break;case 2:this.pos.x=ig.system.width-this.size.x-10;this.pos.y-=this.size.y/2;break;case 3:this.pos.x-=this.size.x/2,this.pos.y=ig.system.height-this.size.y-10}this._activeTween=null;!1!==
c.autoStart&&this.resetBlinking()},startBlinkingAnimation:function(){this._activeTween&&(ig.game.tweens.remove(this._activeTween),this._activeTween=null);this.isBlinking=!0;this._blinkAlpha=0;this._activeTween=(new ig.TweenDef(this)).to({_blinkAlpha:1},1E3*(this.blinkDuration/2)).yoyo(!0).repeat(2*this.blinkCount-1).onComplete(function(){this.isBlinking=!1;this._blinkAlpha=0;this._activeTween=null;if("function"===typeof this.onBlinkingComplete)this.onBlinkingComplete()}.bind(this));this._activeTween.start()},
pauseBlinking:function(){this._activeTween&&this._activeTween.pause()},resumeBlinking:function(){this._activeTween&&this._activeTween.resume()},update:function(){ig.game.isPaused||(this.parent(),ig.currentCtrl&&(ig.currentCtrl.isGameOver&&!this._killed)&&this.kill())},draw:function(){0.11>this._blinkAlpha||(this.ctx.save(),this.ctx.globalAlpha=this._blinkAlpha,this.parent(),this.ctx.restore())},onBlinkingComplete:function(){},resetBlinking:function(a){a&&(this.blinkCount=a.blinkCount||this.blinkCount,
this.blinkDuration=a.blinkDuration||this.blinkDuration,a.size&&(this.size=a.size));this.startBlinkingAnimation()},kill:function(){this._activeTween&&(ig.game.tweens.remove(this._activeTween),this._activeTween=null);this.parent()}})});ig.baked=!0;
ig.module("game.entities.ui.canvas-element").requires("impact.entity").defines(function(){EntityCanvasElement=ig.Entity.extend({name:"EntityCanvasElement",size:new Vector2(100,100),posOffset:new Vector2(0,0),customFillStyle:null,customStrokeStyle:null,imageBitmap:null,alpha:1,rotate:0,scale:1,clipOnce:!1,isHide:!1,element:{type:null,settings:{}},pivot:"center-center",pivotPoint:new Vector2(0,0),_cachedTextProperties:null,_lastTextSettings:null,onInit:null,onReDraw:null,onClip:null,onUpdate:null,onDraw:null,
onTextResize:null,init:function(a,b,c){this.initSettings(c);if(this.onInit)this.onInit(this,c);this.pos=new Vector2(a,b);this.ctx=ig.system.context;this.createOffscreenCanvas();this.drawElementOnce(this.element);this.setPivot();this._size=new Vector2(this.size.x,this.size.y);if(this.onPostInit)this.onPostInit(this,c);ig.game.sortEntitiesDeferred()},createOffscreenCanvas:function(){void 0==window.OffscreenCanvas?(this.offCanvas=document.createElement("canvas"),this.offCanvas.width=this.size.x,this.offCanvas.height=
this.size.y):this.offCanvas=new OffscreenCanvas(this.size.x,this.size.y);this.offCanvasCTX=this.offCanvas.getContext("2d")},update:function(){this.parent();if(!0!=this.isHide&&this.onUpdate)this.onUpdate(this)},draw:function(){!0==this.isHide||0==this.alpha||(this.ctx.save(),this.ctx.globalAlpha=this.alpha,this.ctx.translate(this.posOffset.x,this.posOffset.y),this.drawTransform(this.pos.x,this.pos.y),this.drawCanvasElement(this.imageBitmap),this.ctx.restore(),null!=this.onDraw&&(this.ctx.save(),this.onDraw(this),
this.ctx.restore()),ig.game.debug&&(this.ctx.save(),this.ctx.strokeStyle="yellow",this.ctx.lineWidth=5,this.drawTransform(this.pos.x,this.pos.y),this.ctx.strokeRect(this.pivotPoint.x,this.pivotPoint.y,this.size.x,this.size.y),this.ctx.restore()))},initSettings:function(a){if(a){a.element&&Object.assign(this.element,a.element);this.element.settings&&this.element.settings.image&&(this.size=new Vector2(this.element.settings.image.width,this.element.settings.image.height));for(var b="size scale alpha rotate zIndex onReDraw onClip onUpdate isHide onDraw pivot posOffset onInit onPostInit onTextResize".split(" "),
c=0;c<b.length;c++){var d=b[c];void 0!==a[d]&&(this[d]=a[d])}}},setPivot:function(a){this.pivot=a||this.pivot;a={"top-left":[0,0],"top-center":[-this.size.x/2,0],"top-right":[-this.size.x,0],"center-left":[0,-this.size.y/2],"center-center":[-this.size.x/2,-this.size.y/2],"center-right":[-this.size.x,-this.size.y/2],"bot-left":[0,-this.size.y],"bot-center":[-this.size.x/2,-this.size.y],"bot-right":[-this.size.x,-this.size.y]};a=a[this.pivot]||a["center-center"];this.pivotPoint=new Vector2(a[0],a[1])},
reDraw:function(a,b){b=void 0!=b?b:!1;void 0!=this.offCanvasCTX.reset&&this.offCanvasCTX.reset();this.clearDraw();Object.assign(this.element.settings,a);"text"===this.element.type&&(this._lastTextSettings=this._cachedTextProperties=null);this.size=a.size||this.size;this.offCanvas.width=this.size.x;this.offCanvas.height=this.size.y;this.drawElementOnce(this.element);this._size=this.size;this.setPivot();if(this.onReDraw)this.onReDraw(this);ig.game.sortEntitiesDeferred();this._canvasGroup&&b&&this._canvasGroup.reDrawElements()},
drawCanvasElement:function(a){a&&this.ctx.drawImage(a,0,0,this._size.x,this._size.y,this.pivotPoint.x,this.pivotPoint.y,this.size.x,this.size.y)},drawTransform:function(a,b){this.ctx.translate(Math.floor(a),Math.floor(b));this.ctx.scale(this.scale,this.scale);this.ctx.rotate(this.rotate*(Math.PI/180))},drawElementOnce:function(a){this.clipOnce=!1;this.offCanvasCTX.save();this.offCanvasCTX.globalAlpha=a.settings.alpha||1;var b=a.settings.rotate||0;this.offCanvasCTX.scale(a.settings.scaleX||1,a.settings.scaleY||
1);this.offCanvasCTX.rotate(b*(Math.PI/180));(b={image:this.drawImage,text:this.drawText,box:this.drawBox,arc:this.drawArc,ellipse:this.drawEllipse}[a.type])&&b.call(this,a.settings,this.size);this.offCanvasCTX.restore();this.cleanupAndTransferBitmap()},cleanupAndTransferBitmap:function(){this.imageBitmap&&void 0!=this.imageBitmap.close&&this.imageBitmap.close();this.imageBitmap=void 0!=this.offCanvas.transferToImageBitmap?this.offCanvas.transferToImageBitmap():void 0!=this.offCanvas.createImageBitmap?
this.offCanvas.createImageBitmap():this.offCanvas},clearDraw:function(){this.offCanvasCTX.clearRect(0,0,this.size.x,this.size.y)},drawImage:function(a){if(null!=a.image){var b={image:null,filter:null,sx:0,sy:0,dx:0,dy:0,sWidth:a.image.width,sHeight:a.image.height,dWidth:a.image.width,dHeight:a.image.height};Object.assign(b,a);if(a=null!=b.filter)this.offCanvasCTX.save(),this.offCanvasCTX.filter=b.filter;this.offCanvasCTX.drawImage(b.image.data,b.sx,b.sy,b.sWidth,b.sHeight,b.dx,b.dy,b.dWidth,b.dHeight);
a&&this.offCanvasCTX.restore()}},drawBox:function(a,b,c){c=void 0!=c?!0:!1;var d=a.lineWidth||0;b={fillStyle:null,strokeStyle:null,x:d/2,y:d/2,width:b.x-d,height:b.y-d,borderRadius:a.borderRadius,radii:[a.borderRadius||0,a.borderRadius||0,a.borderRadius||0,a.borderRadius||0],lineWidth:0,lineJoin:"miter"};Object.assign(b,a);this.offCanvasCTX.save();this.offCanvasCTX.beginPath();this.setFillStrokeStyle(b,this.offCanvasCTX);!0==c?(null!=b.fillStyle&&this.offCanvasCTX.fillRect(b.x,b.y,b.width,b.height),
null!=b.strokeStyle&&this.offCanvasCTX.strokeRect(b.x,b.y,b.width,b.height)):(void 0==this.offCanvasCTX.roundRect?this.offCanvasCTX.rect(b.x,b.y,b.width,b.height):this.offCanvasCTX.roundRect(b.x,b.y,b.width,b.height,b.radii),null!=b.fillStyle&&this.offCanvasCTX.fill(),null!=b.strokeStyle&&this.offCanvasCTX.stroke());this.offCanvasCTX.closePath();this.onClip&&!1==this.clipOnce&&(this.clipOnce=!0,this.offCanvasCTX.clip(),this.onClip(this));this.offCanvasCTX.restore()},drawEllipse:function(a,b){var c=
a.lineWidth||10,d=a.radius||b.x/2,e=c/2,c={fillStyle:null,strokeStyle:null,x:d,y:d||b.y/2,radX:d-e||b.x/2-e,radY:d-e||b.y/2-e,rotation:0,startAngle:0,endAngle:360,antiClockwise:!1,lineWidth:c};Object.assign(c,a);this.offCanvasCTX.save();this.offCanvasCTX.beginPath();this.setFillStrokeStyle(c,this.offCanvasCTX);this.offCanvasCTX.ellipse(c.x,c.y,c.radX,c.radY,c.rotation,c.startAngle*(Math.PI/180),c.endAngle*(Math.PI/180),c.antiClockwise);null!=c.fillStyle&&this.offCanvasCTX.fill();null!=c.strokeStyle&&
this.offCanvasCTX.stroke();null!=this.onClip&&!1==this.clipOnce&&(this.clipOnce=!0,this.offCanvasCTX.clip(),this.onClip(this));this.offCanvasCTX.closePath();this.offCanvasCTX.restore()},drawArc:function(a,b){var c=a.lineWidth||10,d=b.x/2,c={fillStyle:null,strokeStyle:null,x:a.radius||d,y:a.radius||d,radius:d-c/2,startAngle:0,endAngle:180,antiClockwise:!1,lineWidth:c,lineCap:"round"};Object.assign(c,a);this.offCanvasCTX.save();this.offCanvasCTX.beginPath();this.setFillStrokeStyle(c,this.offCanvasCTX);
this.offCanvasCTX.arc(c.x,c.y,c.radius,c.startAngle*(Math.PI/180),c.endAngle*(Math.PI/180),c.antiClockwise);null!=c.fillStyle&&this.offCanvasCTX.fill();null!=c.strokeStyle&&this.offCanvasCTX.stroke();null!=this.onClip&&!1==this.clipOnce&&(this.clipOnce=!0,this.offCanvasCTX.clip(),this.onClip(this));this.offCanvasCTX.closePath();this.offCanvasCTX.restore()},drawText:function(a){var b={fillStyle:null,strokeStyle:null,textShadow:null,maxWidth:null,textAlign:"center",textBaseline:"top",text:"text",fontWeight:"normal",
fontFamily:"arial",fontSize:60,offPosX:0,offPosY:0,shadowPosX:5,shadowPosY:5,textShadowAlpha:1,lineWidth:0,adjustFontHeight:0,isResizeByText:!1,isWrapWord:!1};Object.assign(b,a);a=this._lastTextSettings&&this._cachedTextProperties&&b.text===this._lastTextSettings.text&&b.fontSize===this._lastTextSettings.fontSize&&b.fontFamily===this._lastTextSettings.fontFamily&&b.fontWeight===this._lastTextSettings.fontWeight&&b.isWrapWord===this._lastTextSettings.isWrapWord;this.offCanvasCTX.save();this.offCanvasCTX.textRendering=
"optimizeSpeed";this.setFillStrokeStyle(b,this.offCanvasCTX);var c=b.fontWeight+" "+b.fontSize+"px "+b.fontFamily;this.offCanvasCTX.font=c;this.offCanvasCTX.textBaseline=b.textBaseline;this.offCanvasCTX.textAlign=b.textAlign;a?this.textProperties=this._cachedTextProperties:(this._cachedTextProperties=this.textProperties=b.isWrapWord?this.getTextPropertiesOnWordWrap(b,this.offCanvasCTX):this.getTextProperties(b,this.offCanvasCTX),this._lastTextSettings={text:b.text,fontSize:b.fontSize,fontFamily:b.fontFamily,
fontWeight:b.fontWeight,isWrapWord:b.isWrapWord});if(b.isResizeByText&&null==b.maxWidth&&(b.isWrapWord?this.size.y=Math.round(this.textProperties.textLinesHeight):this.size=new Vector2(Math.round(this.textProperties.highestWidth),Math.round(this.textProperties.textLinesHeight)),this.offCanvas.width=this.size.x,this.offCanvas.height=this.size.y,b.isResizeByText=!1,b.maxWidth=null,this._size=this.size,this.setPivot(),this.setFillStrokeStyle(b,this.offCanvasCTX),this.offCanvasCTX.font=c,this.offCanvasCTX.textBaseline=
b.textBaseline,this.offCanvasCTX.textAlign=b.textAlign,this.onTextResize))this.onTextResize(this);this.textPos=new Vector2(0,0);this.setTextAlign(b);b.textShadow&&(this.offCanvasCTX.save(),this.offCanvasCTX.globalAlpha=b.textShadowAlpha,this.offCanvasCTX.fillStyle=b.textShadow,b.strokeStyle&&(this.offCanvasCTX.strokeStyle=b.textShadow),a=new Vector2(this.textPos.x+b.shadowPosX,this.textPos.y+b.shadowPosY),b.isWrapWord?this.wordWrap(a,this.offCanvasCTX,null!=b.strokeStyle):(this.drawFillStrokeText(this.offCanvasCTX,
a,!1,b.maxWidth),b.strokeStyle&&this.drawFillStrokeText(this.offCanvasCTX,a,!0,b.maxWidth)),this.offCanvasCTX.restore());b.isWrapWord?this.wordWrap(this.textPos,this.offCanvasCTX,null!=b.strokeStyle):(this.drawFillStrokeText(this.offCanvasCTX,this.textPos,!1,b.maxWidth),b.strokeStyle&&this.drawFillStrokeText(this.offCanvasCTX,this.textPos,!0,b.maxWidth));this.offCanvasCTX.restore()},setTextAlign:function(a){var b={center:[this.size.x/2+a.offPosX,a.offPosY],left:[0+a.offPosX,a.offPosY],right:[this.size.x+
a.offPosX,a.offPosY]};a=b[a.textAlign]||b.center;this.textPos=new Vector2(a[0],a[1])},textProperties:null,getTextProperties:function(a,b){for(var c=a.text.split("<br>"),d=b.measureText(c[0]),d=d.fontBoundingBoxAscent+d.fontBoundingBoxDescent+(ig.ua.iOS||ig.ua.is_mac?0:a.adjustFontHeight),e=d*c.length,g=Array(c.length),j=0,p=0;p<c.length;p++){var f=b.measureText(c[p]).width;g[p]=f;f>j&&(j=f)}return{widthPerLine:g,highestWidth:j,textLines:c,fontHeight:d,textLinesHeight:e}},getTextPropertiesOnWordWrap:function(a,
b){var c=a.text,d=this.size.x,e=b.measureText(c),e=e.fontBoundingBoxAscent+e.fontBoundingBoxDescent+(ig.ua.iOS||ig.ua.is_mac?0:a.adjustFontHeight),c=c.split(" "),g=[],j=0;if(0<c.length){for(var p=1;0<c.length&&p<=c.length;){var f=c.slice(0,p).join(" ");b.measureText(f).width>d?(1==p&&(p=2),f=c.slice(0,p-1).join(" "),g.push(f),j++,c=c.splice(p-1),p=1):p++}0<c.length&&(j++,g.push(c.join(" ")))}return{widthPerLine:[],highestWidth:d,textLines:g,fontHeight:e,textLinesHeight:e*j}},wordWrap:function(a,b,
c){c=void 0!=c?!0:!1;for(var d=this.textProperties.textLines,e=this.textProperties.fontHeight,g=a.x,j=0;j<d.length;j++){var p=a.y+e*j;b.fillText(d[j],g,p);c&&b.strokeText(d[j],g,p)}},drawFillStrokeText:function(a,b,c,d){c=void 0!=c?c:!1;var e=this.textProperties.textLines,g=this.textProperties.fontHeight,j=b.x;c=null==d?c?function(b,c,d){a.strokeText(b,c,d)}:function(b,c,d){a.fillText(b,c,d)}:c?function(b,c,e){a.strokeText(b,c,e,d)}:function(b,c,e){a.fillText(b,c,e,d)};for(var p=0;p<e.length;p++)c(e[p],
j,b.y+g*p)},setFillStrokeStyle:function(a,b){null!=a.fillStyle&&(b.fillStyle=this.customFillStyle||a.fillStyle);null!=a.strokeStyle&&(b.strokeStyle=this.customStrokeStyle||a.strokeStyle,a.lineWidth&&(b.lineWidth=a.lineWidth),a.lineCap&&(b.lineCap=a.lineCap),a.lineJoin&&(b.lineJoint=a.lineJoin))}})});ig.baked=!0;
ig.module("game.entities.ui.drag-point").requires("impact.entity").defines(function(){EntityDragPoint=ig.Entity.extend({collides:ig.Entity.COLLIDES.NEVER,type:ig.Entity.TYPE.NONE,zIndex:1E17,size:new Vector2(100,100),clickOnce:!1,enterOnce:!1,isClicked:!1,editPos:new Vector2(!1,!1),anchor:{fromObj:null,midX:null,midY:null,offsetX:0,offsetY:0,corner:"top-left",objects:[]},init:function(a,b,c){this.ctx=ig.system.context;this.pos=new Vector2(a,b);void 0==window.OffscreenCanvas?(this.offCanvas=document.createElement("canvas"),
this.offCanvas.width=this.size.x,this.offCanvas.height=this.size.y):this.offCanvas=new OffscreenCanvas(this.size.x,this.size.y);this.offCanvasCTX=this.offCanvas.getContext("2d");if(c){if(c.anchor)for(a=0;a<Object.entries(c.anchor).length;a++)b=Object.entries(c.anchor)[a][0],this.anchor[b]=c.anchor[b];this.editPos=c.editPos||this.editPos}this.pointerEvent=this.editPos.x||this.editPos.y;this.pivotPoint=new Vector2(-this.size.x/2,-this.size.y/2);this.drawEllipse({fillStyle:"cyan"},this.size);this._size=
this.size;this.scale=0.5;this.alpha=0.75;this.isHide=!1;this.setPosition();ig.game.sortEntitiesDeferred()},draw:function(){if(this.editPos.x||this.editPos.y)this.ctx.save(),this.ctx.globalAlpha=this.alpha,this.drawTransform(this.pos.x,this.pos.y),this.drawCanvasElement(this.offCanvas),this.ctx.restore()},update:function(){this.pointerEvent&&this.checkPointer()},repos:function(){this.setPosition()},checkPointer:function(){if(!(void 0==ig.game.io.mouse.getPos||!0==this.isHide||0==this.alpha||!1==this.pointerEvent)){var a=
ig.game.io.mouse.getPos(),b=Math.sqrt(Math.pow(a.x-this.pos.x,2)+Math.pow(a.y-this.pos.y,2)),c=this.size.x>this.size.y?this.size.x:this.size.y;if((this.isPressed=ig.input.state("click"))&&this.clickOnce&&this.onClicking)this.onClicking(this);if(!1==this.isPressed&&!0==this.clickOnce&&(this.clickOnce=!1,this.onReleasedOutside))this.onReleasedOutside(this);b>2*c||(b=a.y>this.pos.y+this.pivotPoint.y&&a.y<this.pos.y+this.pivotPoint.y+this.size.y,a.x>this.pos.x+this.pivotPoint.x&&a.x<this.pos.x+this.pivotPoint.x+
this.size.x&&b&&(this.isPressed&&!1==this.clickOnce)&&(this.clickOnce=!0))}},onReleased:function(){this.reDraw({fillStyle:"cyan"});if(this.anchor.fromObj&&void 0!=this.anchor.fromObj.onReleased)this.anchor.fromObj.onReleased();this.isClicked=!1},onReleasedOutside:function(){this.reDraw({fillStyle:"cyan"});if(this.anchor.fromObj&&void 0!=this.anchor.fromObj.onReleasedOutside)this.anchor.fromObj.onReleasedOutside();this.isClicked=!1},onClicked:function(){this.reDraw({fillStyle:"red"});if(this.anchor.fromObj&&
void 0!=this.anchor.fromObj.onClicked)this.anchor.fromObj.onClicked()},onClicking:function(){if(!this.isClicked){if(this.anchor.fromObj&&void 0!=this.anchor.fromObj.onClicked)this.anchor.fromObj.onClicked();this.isClicked=!0}this.onDraggingPosition();this.reDraw({fillStyle:"red"})},reDraw:function(a){this.clearDraw();this.drawEllipse(a,this.size);ig.game.sortEntitiesDeferred()},clearDraw:function(){this.offCanvasCTX.clearRect(0,0,this.size.x,this.size.y)},drawCanvasElement:function(a){this.ctx.save();
a&&this.ctx.drawImage(a,0,0,this._size.x,this._size.y,this.pivotPoint.x,this.pivotPoint.y,this.size.x,this.size.y);this.ctx.restore()},drawTransform:function(a,b){this.ctx.translate(Math.floor(a),Math.floor(b));this.ctx.scale(this.scale,this.scale)},drawEllipse:function(a,b){for(var c={fillStyle:null,x:a.radius||b.x/2,y:a.radius||b.y/2,radX:a.radius||b.x/2,radY:a.radius||b.y/2,rotation:0,startAngle:0,endAngle:360,antiClockwise:!1},d=0;d<Object.entries(a).length;d++){var e=Object.entries(a)[d][0];
c[e]=a[e]}this.offCanvasCTX.save();this.offCanvasCTX.beginPath();null!=c.fillStyle&&(this.offCanvasCTX.fillStyle=c.fillStyle);this.offCanvasCTX.ellipse(c.x,c.y,c.radX,c.radY,c.rotation,c.startAngle*(Math.PI/180),c.endAngle*(Math.PI/180),c.antiClockwise);null!=c.fillStyle&&this.offCanvasCTX.fill();this.offCanvasCTX.closePath();this.offCanvasCTX.restore()},setPosition:function(){for(var a=0;a<this.anchor.objects.length;a++)this.anchor.objects[a].pos=this.pos;if(null!=this.anchor.fromObj){var a=this.anchor.fromObj.size,
b=this.anchor.fromObj.pos;this.pos.x=null!=this.anchor.midX?b.x+a.x*this.anchor.midX:b.x+this.anchor.offsetX;this.pos.y=null!=this.anchor.midY?b.y+a.y*this.anchor.midY:b.y+this.anchor.offsetY}else switch(b=a=null,null!=this.anchor.midX&&(a=ig.system.width*this.anchor.midX),null!=this.anchor.midY&&(b=ig.system.height*this.anchor.midY),this.anchor.corner){case "top-left":this.pos.x=a||this.anchor.offsetX;this.pos.y=b||this.anchor.offsetY;break;case "top-right":this.pos.x=a||ig.system.width-this.anchor.offsetX;
this.pos.y=b||this.anchor.offsetY;break;case "bottom-right":this.pos.x=a||ig.system.width-this.anchor.offsetX;this.pos.y=b||ig.system.height-this.anchor.offsetY;break;case "bottom-left":this.pos.x=a||this.anchor.offsetX,this.pos.y=b||ig.system.height-this.anchor.offsetY}},onDraggingPosition:function(){!0==this.editPos.x&&(this.pos.x=ig.game.io.mouse.getPos().x);!0==this.editPos.y&&(this.pos.y=ig.game.io.mouse.getPos().y);for(var a=0;a<this.anchor.objects.length;a++)this.anchor.objects[a].pos=this.pos;
this.anchor.fromObj&&ig.currentCtrl.currentVehicle===this.anchor.fromObj&&(this.anchor.fromObj.isDragging=!0,this.anchor.fromObj.addCoordinate(this.pos,!0));switch(this.anchor.corner){case "top-left":!0==this.editPos.x&&(this.anchor.offsetX=this.pos.x);!0==this.editPos.y&&(this.anchor.offsetY=this.pos.y);break;case "top-right":!0==this.editPos.x&&(this.anchor.offsetX=ig.system.width-this.pos.x);!0==this.editPos.y&&(this.anchor.offsetY=this.pos.y);break;case "bottom-right":!0==this.editPos.x&&(this.anchor.offsetX=
ig.system.width-this.pos.x);!0==this.editPos.y&&(this.anchor.offsetY=ig.system.height-this.pos.y);break;case "bottom-left":!0==this.editPos.x&&(this.anchor.offsetX=this.pos.x),!0==this.editPos.y&&(this.anchor.offsetY=ig.system.height-this.pos.y)}null!=this.anchor.midX&&(this.anchor.midX=this.pos.x/ig.system.width);null!=this.anchor.midY&&(this.anchor.midY=this.pos.y/ig.system.height);if(null!=this.anchor.fromObj){var a=this.anchor.fromObj.size,b=this.anchor.fromObj.pos;!0==this.editPos.x&&(null!=
this.anchor.midX?this.anchor.midX=(this.pos.x-b.x)/a.x:this.anchor.offsetX=-1*(b.x-this.pos.x));!0==this.editPos.y&&(null!=this.anchor.midY?this.anchor.midY=(this.pos.y-b.y)/a.y:this.anchor.offsetY=-1*(this.anchor.fromObj.pos.y-this.pos.y))}}})});ig.baked=!0;
ig.module("game.entities.spawn-grid").requires("impact.entity","game.entities.ui.canvas-element","game.entities.ui.drag-point").defines(function(){EntitySpawnGrid=ig.Entity.extend({GRID_CELL_WIDTH:160,GRID_CELL_HEIGHT:120,HALF_CELL_WIDTH:80,HALF_CELL_HEIGHT:60,zIndex:1,topText:"topLeft top1 top2 top3 top4 top5 top6 top7 top8 top9 top10 topRight".split(" "),botText:"botLeft bot1 bot2 bot3 bot4 bot5 bot6 bot7 bot8 bot9 bot10 botRight".split(" "),leftText:"left1 left2 left3 left4 left5 left6 left7".split(" "),
rightText:"right1 right2 right3 right4 right5 right6 right7".split(" "),topTextObj:[],botTextObj:[],leftTextObj:[],rightTextObj:[],allTextObj:[],gridLinesCache:null,isHideDraw:!1,init:function(a,b,c){this.parent(a,b,c);this.isHideDraw=c.isHideDraw||!1;this.ctx=ig.system.context;this.cacheGridLines();this.setupText();ig.game.sortEntitiesDeferred()},cacheGridLines:function(){var a=ig.system.width,b=ig.system.height,c=Math.ceil(a/this.GRID_CELL_WIDTH),d=Math.ceil(b/this.GRID_CELL_HEIGHT);this.gridLinesCache=
{vertical:Array(c),horizontal:Array(d)};for(var e=0,g=this.GRID_CELL_WIDTH;g<a;)this.gridLinesCache.vertical[e++]=g,g+=this.GRID_CELL_WIDTH;a=0;for(g=this.GRID_CELL_HEIGHT;g<b;)this.gridLinesCache.horizontal[a++]=g,g+=this.GRID_CELL_HEIGHT;e<c&&(this.gridLinesCache.vertical.length=e);a<d&&(this.gridLinesCache.horizontal.length=a)},draw:function(){if(!0!=this.isHideDraw){var a=ig.system.width,b=ig.system.height;this.ctx.save();this.ctx.globalAlpha=0.5;this.ctx.fillStyle="black";this.ctx.fillRect(0,
0,a,b);this.ctx.restore();this.drawGrid()}},drawGrid:function(){if(this.gridLinesCache&&!(0===this.gridLinesCache.vertical.length&&0===this.gridLinesCache.horizontal.length)){this.ctx.save();this.ctx.strokeStyle="white";this.ctx.lineWidth=5;this.ctx.beginPath();for(var a=this.gridLinesCache.vertical,b=this.gridLinesCache.horizontal,c=ig.system.height,d=ig.system.width,e=a.length,g=0;g<e;g++){var j=a[g];this.ctx.moveTo(j,0);this.ctx.lineTo(j,c)}a=b.length;for(g=0;g<a;g++)c=b[g],this.ctx.moveTo(0,c),
this.ctx.lineTo(d,c);this.ctx.stroke();this.ctx.closePath();this.ctx.restore()}},createTextEntity:function(a,b,c,d){b=ig.game.spawnEntity(EntityCanvasElement,0,0,{isHide:this.isHideDraw,zIndex:this.zIndex+1,pivot:b,element:{type:"text",settings:{fillStyle:"cyan",text:a,fontFamily:"arial",fontWeight:"bold",fontSize:30,isResizeByText:!0}}});b.pos={x:c,y:d};b.name=a;return b},setupText:function(){var a=this.topText.length,b=this.botText.length,c=this.leftText.length,d=this.rightText.length;this.topTextObj=
Array(a);this.botTextObj=Array(b);this.leftTextObj=Array(c);this.rightTextObj=Array(d);this.allTextObj=Array(a+b+c+d);for(var e=0,g=0,j=10,p=0;p<a;p++){var f=this.topText[p],f=this.createTextEntity(f,"top-center",g+this.HALF_CELL_WIDTH,j);this.topTextObj[p]=f;this.allTextObj[e++]=f;g+=this.GRID_CELL_WIDTH}g=0;j=ig.system.height-10;for(p=0;p<b;p++)f=this.botText[p],f=this.createTextEntity(f,"bot-center",g+this.HALF_CELL_WIDTH,j),this.botTextObj[p]=f,this.allTextObj[e++]=f,g+=this.GRID_CELL_WIDTH;g=
10;j=this.GRID_CELL_HEIGHT;for(p=0;p<c;p++)f=this.leftText[p],f=this.createTextEntity(f,"center-left",g,j+this.HALF_CELL_HEIGHT),this.leftTextObj[p]=f,this.allTextObj[e++]=f,j+=this.GRID_CELL_HEIGHT;g=ig.system.width-10;j=this.GRID_CELL_HEIGHT;for(p=0;p<d;p++)f=this.rightText[p],f=this.createTextEntity(f,"center-right",g,j+this.HALF_CELL_HEIGHT),this.rightTextObj[p]=f,this.allTextObj[e++]=f,j+=this.GRID_CELL_HEIGHT}})});ig.baked=!0;
ig.module("plugins.path-smoother").defines(function(){PathSmoother=ig.Class.extend({chaikin:function(a,b,c){void 0===b&&(b=1);void 0===c&&(c=0.25);if(3>a.length)return a;for(var d=0;d<b;d++)a=this._chaikinIteration(a,c);return a},_chaikinIteration:function(a,b){var c=[];0<a.length&&c.push(a[0]);for(var d=0;d<a.length-1;d++){var e=a[d],g=a[d+1],j={x:b*e.x+(1-b)*g.x,y:b*e.y+(1-b)*g.y};c.push({x:(1-b)*e.x+b*g.x,y:(1-b)*e.y+b*g.y});c.push(j)}1<a.length&&c.push(a[a.length-1]);return c}})});ig.baked=!0;
ig.module("game.entities.objects.parking-slot").requires("plugins.utils.entity-extended").defines(function(){EntityParkingSlot=ig.EntityExtended.extend({zIndex:2,name:null,vertices:[],shape:null,color:null,isOccupied:!1,type:3,checkAgainst:ig.Entity.TYPE.NONE,collides:ig.Entity.COLLIDES.NEVER,isClickable:!0,colorIndicator:{radius:10},SNAP_LENGTH_SCALE:1,SNAP_WIDTH_SCALE:1,frontPoint:null,backPoint:null,showOrientationPoints:!0,frontPointColor:"#00FF00",backPointColor:"#FF0000",orientationPointsRadius:5,
init:function(a,b,c){this.worldShape=null;this.parent(a,b,c);this.name=c.name;this.color=c.color;c.colorIndicator&&(this.colorIndicator.radius=c.colorIndicator.radius||this.colorIndicator.radius);if((a=c.vertices)&&0<a.length){b=a[0].x;for(var d=a[0].y,e=a[0].x,g=a[0].y,j=1;j<a.length;j++)b=Math.min(b,a[j].x),d=Math.min(d,a[j].y),e=Math.max(e,a[j].x),g=Math.max(g,a[j].y);this.pos={x:b,y:d};this.size={x:e-b,y:g-d};var p=this.pos;this.vertices=a.map(function(a){return{x:a.x-p.x,y:a.y-p.y}});ig.SAT&&
"function"===typeof ig.SAT.Shape&&"function"===typeof ig.SAT.Vector2D?this.shape=new ig.SAT.Shape(this.vertices.map(function(a){return new ig.SAT.Vector2D(a.x,a.y)})):(console.error("EntityParkingSlot: ig.SAT.Shape or ig.SAT.Vector2D is not available for slot: "+this.name),this.shape={pointList:[]});this.shape&&this.shape.pointList&&0<this.shape.pointList.length?(b=this.shape.pointList.map(function(a){return new ig.SAT.Vector2D(a.x+this.pos.x,a.y+this.pos.y)}.bind(this)),this.worldShape=new ig.SAT.Shape(b)):
(this.worldShape=new ig.SAT.Shape([]),console.error("EntityParkingSlot: Could not create worldShape for slot: "+this.name+" due to missing local shape."))}else console.error("EntityParkingSlot: No vertices provided for slot: "+this.name),this.size={x:0,y:0},this.vertices=[],this.shape={pointList:[]},this.worldShape=new ig.SAT.Shape([]),console.error("EntityParkingSlot: worldShape set to empty due to no vertices for slot: "+this.name);"undefined"!==typeof LevelData&&(LevelData.COLLISION_TYPES&&void 0!==
LevelData.COLLISION_TYPES.PARKING_SLOT)&&(this.type=LevelData.COLLISION_TYPES.PARKING_SLOT);a&&0<a.length&&ig.utils&&ig.utils.getParkingSlotGeometry?(a=ig.utils.getParkingSlotGeometry(a))?(this.frontPoint=a.frontPoint,this.backPoint=a.backPoint,this.angle=a.angle):(console.error("EntityParkingSlot: Could not calculate geometry for "+this.name),this.angle=0):this.angle=0;c.swapPoints&&(a=this.frontPoint,this.frontPoint=this.backPoint,this.backPoint=a);c.frontPoint&&(this.frontPoint=c.frontPoint);c.backPoint&&
(this.backPoint=c.backPoint);this.frontPoint&&this.backPoint&&(this.angle=Math.atan2(this.frontPoint.y-this.backPoint.y,this.frontPoint.x-this.backPoint.x))},setOccupied:function(a){this.isOccupied=a},isCurrentlyOccupied:function(){return this.isOccupied},getIndicatorPosition:function(){return this.frontPoint},getFrontPoint:function(){return this.frontPoint},getBackPoint:function(){return this.backPoint},getSlotAngle:function(){return void 0!==this.angle?this.angle:0},_isPointInSlotSnapRect:function(a,
b){var c=a.slotLength*this.SNAP_LENGTH_SCALE,d=a.slotWidth*this.SNAP_WIDTH_SCALE,e=b.x-a.center.x,g=b.y-a.center.y,j=Math.cos(-a.orientationAngle),p=Math.sin(-a.orientationAngle),f=e*p+g*j;return Math.abs(e*j-g*p)<=c/2&&Math.abs(f)<=d/2},draw:function(){this.parent();var a=ig.system.context;if(a&&this.shape&&this.shape.pointList&&0!==this.shape.pointList.length){var b=this.pos.x-ig.game.screen.x,c=this.pos.y-ig.game.screen.y,d=this.getIndicatorPosition(),e=d.x-ig.game.screen.x,d=d.y-ig.game.screen.y,
g=this.colorIndicator.radius,j="rgba(128, 128, 128, 1)";if("undefined"!==typeof GameColors&&void 0!==this.color)switch(this.color){case GameColors.RED:j="rgba(255, 0, 0, 1)";break;case GameColors.BLUE:j="rgba(0, 0, 255, 1)";break;case GameColors.GREEN:j="rgba(0, 255, 0, 1)";break;case GameColors.YELLOW:j="rgba(255, 255, 0, 1)";break;case GameColors.PURPLE:j="rgba(128, 0, 128, 1)";break;case GameColors.ORANGE:j="rgba(255, 165, 0, 1)"}a.save();a.fillStyle=j;a.beginPath();a.arc(e,d,g,0,2*Math.PI);a.fill();
a.strokeStyle="rgba(255, 255, 255, 0.5)";a.lineWidth=2;a.stroke();a.restore();ig.game.debugMode&&(this.isOccupied&&(e=b+this.size.x,d=c+this.size.y,a.save(),a.strokeStyle="red",a.lineWidth=3,a.beginPath(),a.moveTo(b,c),a.lineTo(e,d),a.moveTo(e,c),a.lineTo(b,d),a.stroke(),a.restore()),this.drawParkingSlotSnapAreas(),this.showOrientationPoints&&(this.frontPoint&&this.backPoint)&&(a=ig.system.context,b=this.frontPoint.x-ig.game.screen.x,c=this.frontPoint.y-ig.game.screen.y,e=this.backPoint.x-ig.game.screen.x,
d=this.backPoint.y-ig.game.screen.y,a.beginPath(),a.moveTo(e,d),a.lineTo(b,c),a.strokeStyle="#FFFFFF",a.lineWidth=1,a.setLineDash([5,5]),a.stroke(),a.setLineDash([]),a.beginPath(),a.arc(e,d,this.orientationPointsRadius,0,2*Math.PI),a.fillStyle=this.backPointColor,a.fill(),a.strokeStyle="#333333",a.lineWidth=1,a.stroke(),a.beginPath(),a.arc(b,c,this.orientationPointsRadius,0,2*Math.PI),a.fillStyle=this.frontPointColor,a.fill(),a.strokeStyle="#333333",a.lineWidth=1,a.stroke()))}},drawParkingSlotSnapAreas:function(){var a=
ig.system.context;a.save();a.strokeStyle="rgba(255, 0, 255, 0.3)";a.lineWidth=1;a.setLineDash&&a.setLineDash([3,3]);var b=this.slotData.slotLength*this.SNAP_LENGTH_SCALE,c=this.slotData.slotWidth*this.SNAP_WIDTH_SCALE;a.save();a.translate(this.slotData.center.x-ig.game.screen.x,this.slotData.center.y-ig.game.screen.y);a.rotate(this.slotData.orientationAngle);a.beginPath();a.rect(-b/2,-c/2,b,c);a.stroke();a.restore();a.setLineDash&&a.setLineDash([]);a.restore()}})});ig.baked=!0;
ig.module("game.entities.objects.truck").requires("plugins.utils.entity-extended","game.entities.spawn-grid","game.entities.path-head","plugins.path-smoother","plugins.utils.colors","plugins.math.sat","game.entities.objects.parking-slot").defines(function(){EntityTruck=ig.global.EntityTruck=ig.EntityExtended.extend({zIndex:10,type:ig.Entity.TYPE.A,checkAgainst:ig.Entity.TYPE.NONE,collides:ig.Entity.COLLIDES.NEVER,speed:50,rotationSpeed:0.1,pivotOffsetRatio:0.05,angleOffset:Math.PI/2,spawnPoint:null,
isSpawning:!0,parkingSpeed:50,parkingRotationSpeed:0.08,parkingThreshold:5,parkingWaitTime:3,parkingWaitTimeList:[2,3,4,5],parkingSlots:[],isParking:!1,parkingTargetSlotInfo:null,isParked:!1,parkingWaitTimer:0,intendedParkingSlotName:null,buildingCollisionResponseSpeed:0.02,minCollisionDuration:0.2,isInBuildingCollisionResponse:!1,buildingCollisionResponseProgress:0,collisionCooldown:0,needsCollisionRestore:!1,_originalCollidesWith:null,attackBoxPercentage:0.2,attackVertices:[],attackShape:null,minimumDragDistance:80,
optimizationRadius:50,isClicked:!1,isDragging:!1,initialDragPos:{x:0,y:0},currentDragPos:{x:0,y:0},clickTime:0,trajectory:[],trajectoryComplete:!1,trajectorySnapped:!1,minTrajectoryDistance:0,isCurrentPathInvalid:!1,pathHead:null,pathHeadRadius:0,truckState:"none",currentWaypointStartTime:null,waypointTimeoutDuration:3,idleSheetInfo:{sheetImage:new ig.Image("media/graphics/sprites/vehicles/truck.png"),frameCountX:8,frameCountY:1},currentAnim:null,color:null,colorIndicatorRadius:10,timerCircleSize:50,
timerCircleColor:"rgba(0, 200, 255, 0.7)",timerCircleStroke:"rgba(255, 255, 255, 0.8)",timerStrokeWidth:3,circleColor:"#00FFFF",circleRadius:8,circleAlpha:0.8,dashLineAlpha:0.8,solidLineAlpha:0,alphaLerpSpeed:0.05,size:{x:1,y:1},halfW:0,angle:0,targetAngle:0,direction:0,pivotOffset:{x:0,y:0},angleChangedThisFrame:!1,finalDirectionX:0,finalDirectionY:0,finalAngleForMovement:null,vertices:[],shape:null,useCustomVertices:!0,customVertices:{truck1n:[{x:-33.14,y:-94.56},{x:30.02,y:-94.56},{x:31.42,y:98.77},
{x:-31.74,y:97.37}],truck1w:[{x:-37.7,y:-98.77},{x:36.69,y:-98.77},{x:38.09,y:101.57},{x:-39.1,y:101.57}],truck2n:[{x:-31.49,y:-92.69},{x:27.7,y:-92.1},{x:28.89,y:94.47},{x:-32.09,y:95.65}],truck2w:[{x:-31.49,y:-92.69},{x:27.7,y:-92.1},{x:28.89,y:94.47},{x:-32.09,y:95.65}],truck3n:[{x:-29.24,y:-93.28},{x:24.61,y:-93.28},{x:29.95,y:97.43},{x:-30.43,y:96.84}],truck3w:[{x:-29.24,y:-93.28},{x:24.61,y:-93.28},{x:29.95,y:97.43},{x:-30.43,y:96.84}],truck4n:[{x:-29.96,y:-109.88},{x:26.27,y:-110.48},{x:27.45,
y:112.26},{x:-29.96,y:112.26}],truck4w:[{x:-29.96,y:-109.88},{x:26.27,y:-110.48},{x:27.45,y:112.26},{x:-29.96,y:112.26}]},truckType:null,truckTypes:["truck1n","truck2n","truck3n","truck4n"],currentTarget:null,debugMode:!1,satInstance:null,pathSmoother:null,currentWaypointIndex:0,init:function(a,b,c){this.parent(a,b,c);this.ctx=ig.system.context;this.initTruck();ig.game.sortEntitiesDeferred()},initTruck:function(){var a=Math.floor(Math.random()*this.truckTypes.length);this.truckType=this.truckTypes[a];
this.initAnimations();this.onTruckTypeChanged(this.truckType);this.minTrajectoryDistance=0;this.halfW=this.size.x/2;this.satInstance=new ig.SAT;this.pathSmoother=new PathSmoother;this.updatePathHeadRadius();this.spawnPoint&&this.setupSpawnPositionAndDirection();this.fetchParkingSlots();this.updatePivotOffset();this.updateRotatedVertices();this.parkingWaitTime=this.parkingWaitTimeList[a]},initAnimations:function(){this.anims.truck1w=new ig.Animation(this.idleSheet,1,[0],!0);this.anims.truck1n=new ig.Animation(this.idleSheet,
1,[1],!0);this.anims.truck2w=new ig.Animation(this.idleSheet,1,[2],!0);this.anims.truck2n=new ig.Animation(this.idleSheet,1,[3],!0);this.anims.truck3w=new ig.Animation(this.idleSheet,1,[4],!0);this.anims.truck3n=new ig.Animation(this.idleSheet,1,[5],!0);this.anims.truck4w=new ig.Animation(this.idleSheet,1,[6],!0);this.anims.truck4n=new ig.Animation(this.idleSheet,1,[7],!0)},onTruckTypeChanged:function(a){this.currentAnim=this.anims[a]?this.anims[a]:this.anims.truck1n;this.updatePathHeadRadius()},
setTruckType:function(a){this.customVertices[a]&&(this.truckType=a,this.updateRotatedVertices(),this.onTruckTypeChanged(a))},fetchParkingSlots:function(){this.parkingSlots=ig.currentCtrl.parkingSlots},setupSpawnPositionAndDirection:function(){var a=ig.game.getEntitiesByType(EntitySpawnGrid)[0];if(a){for(var b=null,c=-1,a=[{texts:a.topText,textObjs:a.topTextObj,dir:1,angleRad:Math.PI/2},{texts:a.botText,textObjs:a.botTextObj,dir:3,angleRad:-Math.PI/2},{texts:a.leftText,textObjs:a.leftTextObj,dir:0,
angleRad:0},{texts:a.rightText,textObjs:a.rightTextObj,dir:2,angleRad:Math.PI}],d=0;d<a.length;d++)if(c=a[d].texts.indexOf(this.spawnPoint),-1!==c){b=a[d].textObjs[c];this.direction=a[d].dir;this.angle=this.targetAngle=a[d].angleRad;break}if(b)switch(this.direction){case 0:this.pos.x=-this.size.x-100;this.pos.y=b.pos.y-this.size.y/2;break;case 1:this.pos.x=b.pos.x-this.size.x/2;this.pos.y=-this.size.y-100;break;case 2:this.pos.x=ig.system.width+100;this.pos.y=b.pos.y-this.size.y/2;break;case 3:this.pos.x=
b.pos.x-this.size.x/2,this.pos.y=ig.system.height+100}}},update:function(){if(!ig.game.isPaused&&(this.parent(),!ig.currentCtrl||!ig.currentCtrl.isGameOver))this.handleInput(),this.updateMovementState(),this.updateCollisions(),this.updateParkingState(),this.updatePositionAndRotation(),this.currentAnim&&(this.currentAnim.angle=this.angle+this.angleOffset)},updateMovementState:function(){if("exited"===this.truckState)this.checkOffScreen(!0);else{if(this.isRespondingToCollision()){this.updateBuildingCollisionResponse();
return}if(this.isParkingEngaged()){this.updateParking();return}"exit-ready"===this.truckState&&(0<this.trajectory.length&&!this.isClicked)&&this.vacateSlot()}this.isSpawning&&this.updateSpawning();if(this.isFollowingTrajectory())this.updateTrajectoryMovement();else if(this.hasCompletedTrajectoryButNotMovedOn()){var a=Math.abs(this.getShortestRotation(this.angle,this.targetAngle)),a=Math.pow(1-Math.min(1,a/Math.PI),2);this.moveInFacingDirection(this.speed*a);this.checkOffScreen(!1)}this.updateAngleSimple()},
updatePositionAndRotation:function(){if(0!==this.vel.x||0!==this.vel.y||this.angleChangedThisFrame)this.updatePivotOffset(),this.updateRotatedVertices(),this.pos.x+=this.vel.x*ig.system.tick,this.pos.y+=this.vel.y*ig.system.tick},updateCollisions:function(){this.updateCollisionResponseState()},updateParkingState:function(){this.isParkingEngaged()&&this.updateParking()},draw:function(){this.drawMovementVisuals();this.parent();this.drawParkingTimer();this.drawColorIndicator();this.drawDebugInfo()},
drawMovementVisuals:function(){this.drawMinDistanceIndicator();this.drawTrajectoryAndPathHead();this.drawSnapVisuals()},drawMinDistanceIndicator:function(){if(this.isClicked&&!(0<this.trajectory.length||this.isParked&&"exit-ready"===this.truckState)){var a=ig.system.context,b=this.getPivotPoint(),c=ig.game.io.mouse.getPos(),d=ig.utils.distanceBetween(b.x,b.y,c.x,c.y),d=this.isCurrentPathInvalid||d<this.minTrajectoryDistance;a.save();a.beginPath();a.arc(b.x,b.y,this.minTrajectoryDistance,0,2*Math.PI);
a.strokeStyle="rgba(255, 255, 255, 0.2)";a.lineWidth=2;a.setLineDash&&a.setLineDash([5,5]);a.stroke();a.beginPath();a.moveTo(b.x,b.y);a.lineTo(c.x,c.y);a.strokeStyle=d?"rgba(255, 100, 100, 0.5)":"rgba(100, 255, 100, 0.5)";a.setLineDash&&a.setLineDash([]);a.lineWidth=2;a.stroke();a.restore()}},drawTrajectoryAndPathHead:function(){var a=ig.system.context;if(this.isClicked&&this.pathHead){a.save();a.beginPath();a.arc(this.pathHead.pos.x,this.pathHead.pos.y,this.pathHead.getRadius(),0,2*Math.PI);var b=
this.isCurrentPathInvalid?"rgba(255, 0, 0, 0.5)":"rgba(0, 255, 0, 0.5)";a.fillStyle=this.isCurrentPathInvalid?"rgba(255, 0, 0, 0.2)":"rgba(0, 255, 0, 0.2)";a.fill();a.strokeStyle=b;a.lineWidth=2;a.stroke();a.restore()}if(0!==this.trajectory.length){this.isClicked?(this.dashLineAlpha=ig.utils.lerp(this.dashLineAlpha,0.8,this.alphaLerpSpeed),this.solidLineAlpha=ig.utils.lerp(this.solidLineAlpha,0,this.alphaLerpSpeed)):(this.dashLineAlpha=ig.utils.lerp(this.dashLineAlpha,0,this.alphaLerpSpeed),this.solidLineAlpha=
!this.trajectoryComplete||0<this.trajectory.length?ig.utils.lerp(this.solidLineAlpha,0.8,this.alphaLerpSpeed):ig.utils.lerp(this.solidLineAlpha,0,this.alphaLerpSpeed));b=this.getPivotPoint();if(0.01<this.solidLineAlpha){a.save();a.strokeStyle="rgba(255, 255, 255, "+this.solidLineAlpha+")";a.lineWidth=4;a.beginPath();a.moveTo(b.x,b.y);for(var c=0;c<this.trajectory.length;c++)a.lineTo(this.trajectory[c].x,this.trajectory[c].y);a.stroke();a.restore()}if(0.01<this.dashLineAlpha){a.save();a.setLineDash&&
a.setLineDash([8,4]);a.strokeStyle=this.isCurrentPathInvalid&&this.isClicked?"rgba(255, 0, 0, "+this.dashLineAlpha+")":"rgba(255, 255, 255, "+this.dashLineAlpha+")";a.lineWidth=4;a.beginPath();a.moveTo(b.x,b.y);for(c=0;c<this.trajectory.length;c++)a.lineTo(this.trajectory[c].x,this.trajectory[c].y);a.stroke();a.setLineDash&&a.setLineDash([]);a.restore()}0<this.trajectory.length&&!this.trajectorySnapped?(b=this.trajectory[this.trajectory.length-1],a.save(),a.beginPath(),a.arc(b.x,b.y,this.circleRadius,
0,2*Math.PI),a.fillStyle=this.circleColor,b=this.isClicked?this.circleAlpha:Math.max(this.solidLineAlpha,this.dashLineAlpha),a.globalAlpha=b,a.fill(),a.restore()):this.trajectorySnapped&&(b=this.trajectory[this.trajectory.length-1],a.save(),a.beginPath(),a.arc(b.x,b.y,0.5*this.circleRadius,0,2*Math.PI),a.fillStyle=this.circleColor,a.globalAlpha=1,a.fill(),a.restore())}},drawSnapVisuals:function(){if(this.isClicked)for(var a=ig.system.context,b=this.optimizationRadius||150,c=0;c<this.parkingSlots.length;c++){var d=
this.parkingSlots[c],e=d.slotEntity;if(e&&!(e.isCurrentlyOccupied()||d.color!==this.color))if(d=e.getFrontPoint?e.getFrontPoint():null)a.save(),a.beginPath(),a.arc(d.x-ig.game.screen.x,d.y-ig.game.screen.y,b,0,2*Math.PI),a.strokeStyle="rgba(255, 255, 0, 0.4)",a.lineWidth=2,a.setLineDash&&a.setLineDash([5,8]),a.stroke(),a.restore()}},drawColorIndicator:function(){if(this.color&&this.ctx){var a=this.pos.x+this.size.x/2-ig.game.screen.x,b=this.pos.y+this.size.y/2-ig.game.screen.y,c="rgba(128, 128, 128, 0.8)";
if("undefined"!==typeof GameColors)switch(this.color){case GameColors.RED:c="rgba(255, 0, 0, 0.8)";break;case GameColors.BLUE:c="rgba(0, 0, 255, 0.8)";break;case GameColors.GREEN:c="rgba(0, 255, 0, 0.8)";break;case GameColors.YELLOW:c="rgba(255, 255, 0, 0.8)";break;case GameColors.PURPLE:c="rgba(128, 0, 128, 0.8)";break;case GameColors.ORANGE:c="rgba(255, 165, 0, 0.8)"}this.ctx.save();this.ctx.fillStyle=c;this.ctx.beginPath();this.ctx.arc(a,b,this.colorIndicatorRadius,0,2*Math.PI);this.ctx.fill();
this.ctx.strokeStyle="rgba(0, 0, 0, 0.5)";this.ctx.lineWidth=2;this.ctx.stroke();this.ctx.restore()}},drawParkingTimer:function(){if(this.isParked&&"waiting"===this.truckState&&this.parkingTargetSlotInfo){var a=Math.min(1,Math.max(0,this.parkingWaitTimer/this.parkingWaitTime)),b=ig.system.context,c=this.parkingTargetSlotInfo.center.x-ig.game.screen.x,d=this.parkingTargetSlotInfo.center.y-ig.game.screen.y,e=this.timerCircleSize/2;b.save();b.beginPath();b.arc(c,d,e,0,2*Math.PI);b.fillStyle="rgba(0, 0, 0, 0.4)";
b.fill();b.beginPath();b.moveTo(c,d);b.arc(c,d,e,-Math.PI/2,-Math.PI/2+2*Math.PI*a,!1);b.lineTo(c,d);b.fillStyle=this.timerCircleColor;b.fill();b.beginPath();b.arc(c,d,e,0,2*Math.PI);b.lineWidth=this.timerStrokeWidth;b.strokeStyle=this.timerCircleStroke;b.stroke();b.restore()}},drawDebugInfo:function(){if(ig.game.debugMode&&this.ctx){var a=this.pos.x+this.size.x/2-ig.game.screen.x,b=this.pos.y+this.size.y/2-ig.game.screen.y,c=this.getPivotPoint(),d=c.x-ig.game.screen.x,c=c.y-ig.game.screen.y;this.ctx.save();
this.ctx.strokeStyle="rgba(0, 255, 0, 0.8)";this.ctx.lineWidth=3;this.ctx.beginPath();this.ctx.moveTo(a,b);var e=a+40*Math.cos(this.angle),g=b+40*Math.sin(this.angle);this.ctx.lineTo(e,g);this.ctx.stroke();this.ctx.strokeStyle="rgba(255, 255, 0, 0.8)";this.ctx.lineWidth=2;this.ctx.beginPath();this.ctx.moveTo(a,b);e=a+35*Math.cos(this.targetAngle);g=b+35*Math.sin(this.targetAngle);this.ctx.lineTo(e,g);this.ctx.stroke();this.ctx.fillStyle="rgba(255, 0, 0, 0.8)";this.ctx.beginPath();this.ctx.arc(d,c,
4,0,2*Math.PI);this.ctx.fill();this.currentTarget&&(this.ctx.strokeStyle="rgba(0, 255, 255, 0.6)",this.ctx.lineWidth=2,this.ctx.beginPath(),this.ctx.moveTo(d,c),this.ctx.lineTo(this.currentTarget.x-ig.game.screen.x,this.currentTarget.y-ig.game.screen.y),this.ctx.stroke());this.ctx.fillStyle="rgba(255, 255, 255, 0.9)";this.ctx.font="12px Arial";d=Math.sqrt(this.vel.x*this.vel.x+this.vel.y*this.vel.y).toFixed(1);this.ctx.fillText("Speed: "+d,a-30,b-45);this.ctx.restore();if(0<this.attackVertices.length){this.ctx.save();
this.ctx.strokeStyle="rgba(0, 0, 255, 0.8)";this.ctx.lineWidth=2;this.ctx.beginPath();a=this.attackVertices[this.attackVertices.length-1];this.ctx.moveTo(a.x-ig.game.screen.x,a.y-ig.game.screen.y);for(a=0;a<this.attackVertices.length;a++)b=this.attackVertices[a],this.ctx.lineTo(b.x-ig.game.screen.x,b.y-ig.game.screen.y);this.ctx.closePath();this.ctx.stroke();this.ctx.restore()}}},handleInput:function(){if(this.isParked&&"exit-ready"!==this.truckState||this.isParking){if(this.isClicked||this.isDragging)this.onReleased()}else{if(ig.input.pressed("click")){var a=
ig.game.io.getClickPos();if(this.containsPointer(a))this.onClicked()}ig.input.state("click")&&this.isClicked&&this.clicking();if(ig.input.released("click")&&this.isClicked)this.onReleased()}},onClicked:function(){this.isClicked=!0;this.clickTime=ig.system.clock.delta();this._startNewPathDrawing()},_startNewPathDrawing:function(){this.isCurrentPathInvalid=!1;this.pathHead&&(this.pathHead.kill(),this.pathHead=null);var a=ig.game.io.getClickPos(),b=this.isParked&&"exit-ready"===this.truckState||this.isFollowingTrajectory()?
this.getPivotPoint():a;this.pathHead=ig.game.spawnEntity(EntityPathHead,b.x,b.y,{radius:this.pathHeadRadius});ig.currentCtrl&&(ig.currentCtrl.currentVehicle=this);this.trajectorySnapped=this.trajectoryComplete=!1;this.finalDirectionY=this.finalDirectionX=0;this.trajectory=[];this.isDragging=!1;this.dashLineAlpha=0.8;this.solidLineAlpha=0;this.initialDragPos={x:a.x,y:a.y};this.currentDragPos={x:a.x,y:a.y}},clicking:function(){if(this.isClicked){var a=ig.game.io.getClickPos();this.currentDragPos.x=
a.x;this.currentDragPos.y=a.y;var b=ig.utils.distanceBetween(this.initialDragPos.x,this.initialDragPos.y,a.x,a.y);!this.isDragging&&b>this.minimumDragDistance&&(this.isDragging=!0);if((this.pathHead||!this.isCurrentPathInvalid)&&this.pathHead)this.pathHead.updateSATShape(a),this.isDragging&&(this.checkPathHeadCollisions()?(this.isCurrentPathInvalid=!0,this.trajectory=[],this.pathHead.kill(),this.pathHead=null):(this.isCurrentPathInvalid=!1,this.isCurrentPathInvalid||(this.addCoordinate(a,!0),0<this.trajectory.length&&
!this.isCurrentPathInvalid&&this.attemptSnapToParkingSlot(this.trajectory[this.trajectory.length-1])&&this.calculateTrajectoryDistance()>=this.minTrajectoryDistance&&this.finalizeSnappedTrajectory())))}},onReleased:function(){this.isClicked&&(this.isDragging=this.isClicked=!1,this.pathHead&&(this.pathHead.kill(),this.pathHead=null),this.isCurrentPathInvalid&&0<this.trajectory.length?(this.trajectory=[],this.currentWaypointIndex=0):0<this.trajectory.length&&(this.calculateTrajectoryDistance()<this.minTrajectoryDistance?
(this.trajectory=[],this.currentWaypointIndex=0):(this.dashLineAlpha=0,this.solidLineAlpha=0.8)),this.isCurrentPathInvalid=!1,ig.currentCtrl&&ig.currentCtrl.currentVehicle===this&&(ig.currentCtrl.currentVehicle=null))},containsPointer:function(a){return this.vertices?ig.utils.pointInPolygon(a,this.vertices):!1},updateSpawning:function(){var a=this.speed;switch(this.direction){case 0:this.vel.x=a;this.vel.y=0;0<=this.pos.x&&(this.isSpawning=!1);break;case 1:this.vel.x=0;this.vel.y=a;0<=this.pos.y&&
(this.isSpawning=!1);break;case 2:this.vel.x=-a;this.vel.y=0;this.pos.x<=ig.system.width-this.size.x&&(this.isSpawning=!1);break;case 3:this.vel.x=0,this.vel.y=-a,this.pos.y<=ig.system.height-this.size.y&&(this.isSpawning=!1)}},updateTrajectoryMovement:function(){var a=this.getCurrentWaypoint();if(a){this.isLastWaypoint()&&this.prepareForLastWaypoint(a);this.goTo(a.x,a.y);var b=Math.abs(this.getShortestRotation(this.angle,this.targetAngle)),b=Math.min(1,b/(Math.PI/2)),b=Math.pow(1-b,2),b=Math.max(0.1,
b);this.moveInFacingDirection(this.speed*b);this.checkWaypointReached(a)&&(this.advanceWaypoint(),this.isTrajectoryEmpty()&&this.finalizeTrajectoryMovement())}else this.finalizeTrajectoryMovement()},goTo:function(a,b){this.currentTarget={x:a,y:b};var c=this.getPivotPoint();this.targetAngle=Math.atan2(b-c.y,a-c.x);this.targetAngle=this.normalizeAngle(this.targetAngle)},moveToTarget:function(a){this.goTo(a.x,a.y)},moveInFacingDirection:function(a){a=a||this.speed;this.vel.x=Math.cos(this.angle)*a;this.vel.y=
Math.sin(this.angle)*a},updateAngleSimple:function(){this.angleChangedThisFrame=!1;var a=this.getShortestRotation(this.angle,this.targetAngle);0.01<Math.abs(a)&&(this.angle+=a*this.rotationSpeed,this.angle=this.normalizeAngle(this.angle),this.angleChangedThisFrame=!0)},normalizeAngle:function(a){if(!isFinite(a))return 0;a-=Math.floor(a/(2*Math.PI))*2*Math.PI;0>a&&(a+=2*Math.PI);return a},getShortestRotation:function(a,b){a=this.normalizeAngle(a);b=this.normalizeAngle(b);for(var c=b-a;c>Math.PI;)c-=
2*Math.PI;for(;c<-Math.PI;)c+=2*Math.PI;return c},getDirectionFromAngle:function(){return{x:Math.cos(this.angle),y:Math.sin(this.angle)}},updatePivotOffset:function(){var a=this.size.y*this.pivotOffsetRatio;this.pivotOffset.x=Math.cos(this.angle)*a;this.pivotOffset.y=Math.sin(this.angle)*a},getPivotPoint:function(){return{x:this.pos.x+this.size.x/2+this.pivotOffset.x,y:this.pos.y+this.size.y/2+this.pivotOffset.y}},createAttackVertices:function(){if(!this.useCustomVertices||!this.customVertices||!this.truckType||
!this.customVertices[this.truckType]){var a=this.size.x/2,b=this.size.y*this.attackBoxPercentage,c=this.size.y/2;return[{x:-a,y:-c},{x:a,y:-c},{x:a,y:-c+b},{x:-a,y:-c+b}]}for(var d=this.customVertices[this.truckType],a=Infinity,e=-Infinity,b=Infinity,c=-Infinity,g=0;g<d.length;g++)a=Math.min(a,d[g].y),e=Math.max(e,d[g].y),b=Math.min(b,d[g].x),c=Math.max(c,d[g].x);d=(e-a)*this.attackBoxPercentage;return[{x:b,y:a},{x:c,y:a},{x:c,y:a+d},{x:b,y:a+d}]},updateRotatedVertices:function(){var a=this.pos.x+
this.size.x/2,b=this.pos.y+this.size.y/2,c=this.angle+this.angleOffset;if(this.useCustomVertices&&this.customVertices&&this.truckType&&this.customVertices[this.truckType]){var d=this.customVertices[this.truckType];this.vertices=[];for(var e=0;e<d.length;e++){var g=d[e];this.vertices.push(ig.utils.rotatePoint(a+g.x,b+g.y,a,b,c))}}else d=this.size.x/2,e=this.size.y/2,this.vertices=[ig.utils.rotatePoint(a-d,b-e,a,b,c),ig.utils.rotatePoint(a+d,b-e,a,b,c),ig.utils.rotatePoint(a+d,b+e,a,b,c),ig.utils.rotatePoint(a-
d,b+e,a,b,c)];d=this.createAttackVertices();this.attackVertices=[];for(e=0;e<d.length;e++)g=d[e],this.attackVertices.push(ig.utils.rotatePoint(a+g.x,b+g.y,a,b,c));this.updateSATShape()},updateSATShape:function(){this.shape=new ig.SAT.Shape(this.vertices);0<this.attackVertices.length&&(this.attackShape=new ig.SAT.Shape(this.attackVertices))},getActiveTruckWidth:function(){if(this.useCustomVertices&&this.customVertices&&this.truckType&&this.customVertices[this.truckType]){for(var a=this.customVertices[this.truckType],
b=Infinity,c=-Infinity,d=0;d<a.length;d++)a[d].x<b&&(b=a[d].x),a[d].x>c&&(c=a[d].x);return c-b}return this.size.x},updatePathHeadRadius:function(){this.pathHeadRadius=0.8*(this.getActiveTruckWidth()/2)},isFollowingTrajectory:function(){return 0<this.trajectory.length&&!this.trajectoryComplete},hasCompletedTrajectoryButNotMovedOn:function(){return 0===this.trajectory.length&&this.trajectoryComplete&&!this.isClicked},isTrajectoryEmpty:function(){return 0===this.trajectory.length},finalizeTrajectoryMovement:function(){this.isClicked?
this._startNewPathDrawing():(this.trajectoryComplete=!0,null!==this.finalAngleForMovement&&(this.targetAngle=this.finalAngleForMovement,this.finalAngleForMovement=null))},clearTrajectory:function(){this.trajectory=[];this.trajectoryComplete=!0;this.isClicked=!1;this.currentWaypointIndex=0;this.pathHead&&(this.pathHead.kill(),this.pathHead=null)},addCoordinate:function(a,b){if(!this.isCurrentPathInvalid&&this.pathHead){var c=!1;if(this.isParked&&"exit-ready"===this.truckState&&0===this.trajectory.length){var d=
this.getPivotPoint();if(!this.attackShape||!this.attackShape.pointList||2>this.attackShape.pointList.length)console.warn("EntityTruck.addCoordinate: Truck attackShape or its pointList not available/sufficient for front line check during exit."),c=!0;else if(!this.satInstance||"function"!==typeof this.satInstance.simpleShapeIntersect)console.warn("EntityTruck.addCoordinate: SAT tools not available for front line check during exit."),c=!0;else{var e=this.attackShape.pointList[0],g=this.attackShape.pointList[1],
d=new ig.SAT.Shape([{x:d.x,y:d.y},{x:a.x,y:a.y}]),e=new ig.SAT.Shape([{x:e.x,y:e.y},{x:g.x,y:g.y}]);this.satInstance.simpleShapeIntersect(d,e)||(c=!0)}}!c&&(0===this.trajectory.length&&!(this.isParked&&"exit-ready"===this.truckState))&&(e=this.getPivotPoint(),ig.utils.distanceBetween(e.x,e.y,a.x,a.y)<this.minTrajectoryDistance&&(c=!0));if(c)this.isCurrentPathInvalid=!0;else if(this.isCurrentPathInvalid=!1,c=0===this.trajectory.length?this.getPivotPoint():this.trajectory[this.trajectory.length-1],
e=ig.utils.distanceBetween(c.x,c.y,a.x,a.y),17<e){if(b&&e>this.halfW)for(var e=Math.floor(e/(0.8*this.halfW)),e=Math.max(1,e),g=(a.x-c.x)/e,d=(a.y-c.y)/e,j=1;j<e;j++)this.trajectory.push({x:c.x+j*g,y:c.y+j*d});this.trajectory.push({x:a.x,y:a.y})}}},finalizeSnappedTrajectory:function(){this.isDragging=this.isClicked=this.trajectoryComplete=!1;this.dashLineAlpha=0;this.solidLineAlpha=0.8;this.trajectorySnapped=!0;this.pathHead&&(this.pathHead.kill(),this.pathHead=null);ig.currentCtrl&&ig.currentCtrl.currentVehicle===
this&&(ig.currentCtrl.currentVehicle=null)},calculateTrajectoryDistance:function(){if(0===this.trajectory.length)return 0;for(var a=0,b=this.getPivotPoint(),c=0;c<this.trajectory.length;c++)var d=this.trajectory[c],a=a+ig.utils.distanceBetweenPoints(b,d),b=d;return a},checkPathHeadCollisions:function(){if(!this.pathHead||!this.pathHead.getSATShape())return!1;var a=this.pathHead.getSATShape(),b=ig.currentCtrl;if(!b||!b.collisionShapesData)return!1;for(var c=this.pathHead.pos,d=this.pathHead.getRadius(),
c={minX:c.x-d,minY:c.y-d,maxX:c.x+d,maxY:c.y+d},d=0;d<b.collisionShapesData.length;d++){var e=b.collisionShapesData[d];if(e.type===LevelData.COLLISION_TYPES.BUILDING&&b.checkAABBOverlap(c,e.aabb)&&this.satInstance.simpleShapeIntersect(a,e.shape,!0))return!0}d=ig.game.getEntitiesByType(EntityTruck);for(e=0;e<d.length;e++){var g=d[e];if(g&&g.shape&&g===this&&"none"==this.truckState&&this.calculateTrajectoryDistance()>0.75*this.size.y){g&&"function"===typeof g.updateRotatedVertices&&g.updateRotatedVertices();
var j=b.calculateAABB(g.vertices);if(b.checkAABBOverlap(c,j)&&this.satInstance.simpleShapeIntersect(a,g.shape,!0))return!0}}return!1},getCurrentWaypoint:function(){return 0<this.trajectory.length?this.trajectory[0]:null},getNextWaypoint:function(){return 1<this.trajectory.length?this.trajectory[1]:null},isLastWaypoint:function(){return 1===this.trajectory.length},advanceWaypoint:function(){0<this.trajectory.length&&this.trajectory.shift()},checkWaypointReached:function(a){if(!a)return!1;var b=this.getPivotPoint(),
c=a.x-b.x;a=a.y-b.y;c=Math.sqrt(c*c+a*a);a=this.isLastWaypoint()?5:12;return c<a},passTarget:function(){if(!this.currentTarget)return!1;var a=this.getPivotPoint();return 0<this.vel.x&&a.x>this.currentTarget.x||0>this.vel.x&&a.x<this.currentTarget.x||0<this.vel.y&&a.y>this.currentTarget.y||0>this.vel.y&&a.y<this.currentTarget.y},prepareForLastWaypoint:function(a){var b=this.getPivotPoint();this.targetAngle=this.normalizeAngle(Math.atan2(a.y-b.y,a.x-b.x));var c=a.x-b.x;a=a.y-b.y;b=Math.sqrt(c*c+a*a);
0<b&&(this.finalDirectionX=c/b,this.finalDirectionY=a/b,this.finalAngleForMovement=Math.atan2(this.finalDirectionY,this.finalDirectionX))},setMovementToFinalDirection:function(){this.targetAngle=this.finalAngleForMovement;this.vel.x=this.finalDirectionX*this.speed;this.vel.y=this.finalDirectionY*this.speed},initializeWaypointTimeout:function(){this.currentWaypointStartTime=ig.system.clock.delta()},isStuckOnWaypoint:function(){return!this.currentWaypointStartTime?(this.initializeWaypointTimeout(),
!1):ig.system.clock.delta()-this.currentWaypointStartTime>this.waypointTimeoutDuration},resetWaypointTimeout:function(){this.currentWaypointStartTime=null},isParkingEngaged:function(){return this.isParking||this.isParked},beginParking:function(a){a&&a instanceof EntityParkingSlot&&a.type===LevelData.COLLISION_TYPES.PARKING_SLOT?this.color===a.color&&!a.isCurrentlyOccupied()?this.beginParkingInternal(a):this.needsCollisionRestore=!0:this.needsCollisionRestore=!0},beginParkingInternal:function(a){if(!a||
!(a instanceof EntityParkingSlot))console.error("Truck.beginParking: Invalid parkingSlotEntity provided."),this.needsCollisionRestore=!0;else if(a.isCurrentlyOccupied()&&console.warn("Truck: Attempting to park in already occupied slot (checked via entity):",a.name),!this.isParking&&!this.isParked){a.color!==this.color&&console.warn("Truck.beginParking: Color mismatch! Truck: "+this.color+", Slot: "+a.color);for(var b=null,c=0;c<this.parkingSlots.length;c++)if(this.parkingSlots[c].name===a.name&&this.parkingSlots[c].slotEntity===
a){b=this.parkingSlots[c];break}b?(a=b.orientationAngle,this.parkingTargetSlotInfo=ig.copy(b),this.parkingTargetSlotInfo.finalParkingAngle=this.normalizeAngle(a+Math.PI),this.isParking=!0,this.truckState="parking",this.clearTrajectory(),this.vel.x=0,this.vel.y=0):this.needsCollisionRestore=!0}},updateParking:function(){this.isParking?this.handleParking():this.isParked&&this.handleParkedState()},handleParking:function(){if(!this.parkingTargetSlotInfo||!this.parkingTargetSlotInfo.slotEntity)console.warn("Truck.handleParking: parkingTargetSlotInfo or its slotEntity is missing. Aborting."),
this.finishParking(!0);else{var a=this.parkingTargetSlotInfo.center,b=this.parkingTargetSlotInfo.finalParkingAngle,c={x:this.pos.x+this.size.x/2,y:this.pos.y+this.size.y/2},d=ig.utils.distanceBetweenPoints(c,a),a=ig.utils.angleBetweenPoints(c,a),b=this.getShortestRotation(this.angle,b);0.01<Math.abs(b)&&(this.angle+=b*this.parkingRotationSpeed,this.angle=this.normalizeAngle(this.angle),this.angleChangedThisFrame=!0);c=1;d>this.parkingThreshold?(c=Math.abs(b)>Math.PI/4?0.3:1,this.vel.x=Math.cos(a)*
this.parkingSpeed*c,this.vel.y=Math.sin(a)*this.parkingSpeed*c):(this.vel.x=0,this.vel.y=0,0.05>=Math.abs(b)&&this.finishParking(!1))}},handleParkedState:function(){this.vel.x=0;this.vel.y=0;switch(this.truckState){case "waiting":this.parkingWaitTimer+=ig.system.tick;this.parkingWaitTimer>=this.parkingWaitTime&&(this.truckState="exit-ready",this.targetAngle=this.angle=this.normalizeAngle(this.angle+Math.PI),this.angleChangedThisFrame=!0);break;case "exit-ready":0<this.trajectory.length&&!this.isClicked&&
(this.truckState="exiting",this.isParked=!1,this._originalCollidesWith&&(this._originalCollidesWith=this._originalCollidesWith.filter(function(a){return a!==(LevelData.COLLISION_TYPES.BOUNDARY||2)})),this.needsCollisionRestore=!0)}},finishParking:function(a){var b=this.parkingTargetSlotInfo?this.parkingTargetSlotInfo.slotEntity:null;b&&this.parkingTargetSlotInfo&&!a?(b.setOccupied(!0),this.pos.x=this.parkingTargetSlotInfo.center.x-this.size.x/2,this.pos.y=this.parkingTargetSlotInfo.center.y-this.size.y/
2,this.targetAngle=this.angle=this.parkingTargetSlotInfo.finalParkingAngle,this.isParked=this.angleChangedThisFrame=!0,this.parkingWaitTimer=0,this.truckState="waiting"):(b?b.setOccupied(!1):this.parkingTargetSlotInfo&&this.parkingTargetSlotInfo.name&&(a=ig.game.getEntityByName(this.parkingTargetSlotInfo.name))&&a instanceof EntityParkingSlot&&a.setOccupied(!1),this.isParked=!1,this.truckState="none",this.needsCollisionRestore=!0);this.isParking=!1;this.vel.x=0;this.vel.y=0},vacateSlot:function(){var a=
null;if(this.parkingTargetSlotInfo&&this.parkingTargetSlotInfo.slotEntity)a=this.parkingTargetSlotInfo.slotEntity;else if(this.intendedParkingSlotName){var b=ig.game.getEntityByName(this.intendedParkingSlotName);b instanceof EntityParkingSlot&&(a=b)}a&&a.setOccupied(!1);this.isParked=this.isParking=!1;this.truckState="exited";this.parkingTargetSlotInfo=this.intendedParkingSlotName=null},attemptSnapToParkingSlot:function(a){if(!a||0===this.parkingSlots.length||"none"!==this.truckState)return!1;for(var b=
this.optimizationRadius||150,c=0;c<this.parkingSlots.length;c++){var d=this.parkingSlots[c],e=d.slotEntity;if(e&&this._validateSlot(d).status){var g=e.getFrontPoint?e.getFrontPoint():null;if(g&&ig.utils.distanceBetweenPoints(a,g)<b&&(!this.shape||(!e||!e.worldShape||!this.satInstance)||!this.satInstance.simpleShapeIntersect(this.shape,e.worldShape)))return this.trajectory.push(g),this.targetAngle=d.orientationAngle,!0}}return!1},_validateSlot:function(a){var b=a.slotEntity;return!b?{status:!1,reason:"missing-entity"}:
"function"===typeof b.isCurrentlyOccupied&&b.isCurrentlyOccupied()?{status:!1,reason:"occupied"}:a.color!==this.color?{status:!1,reason:"color-mismatch"}:{status:!0,reason:"valid"}},onCollision:function(a){this.onCollisionInternal(a)},onCollisionInternal:function(a){if(this.collidesWith&&this.collidesWith.includes(a.type)&&(a.type===LevelData.COLLISION_TYPES.BUILDING||a.type===LevelData.COLLISION_TYPES.BOUNDARY)&&!this.isInBuildingCollisionResponse&&!this.isParkingEngaged())this.trajectory=[],this.trajectoryComplete=
!0,this.startBuildingCollisionResponse(),this.collisionCooldown=0.5},onExitCollision:function(a){this.onExitCollisionInternal(a)},onExitCollisionInternal:function(a){if(a.type===LevelData.COLLISION_TYPES.PARKING_SLOT&&(this.parkingTargetSlotInfo&&this.parkingTargetSlotInfo.name===a.name)&&(this.isParked||"exiting"===this.truckState))this.vacateSlot(),this.needsCollisionRestore=!0},updateCollisionResponseState:function(){0<this.collisionCooldown&&(this.collisionCooldown-=ig.system.tick);this.isInBuildingCollisionResponse&&
this.updateBuildingCollisionResponse();this.needsCollisionRestore&&(!this.isParkingEngaged()&&!this.isInBuildingCollisionResponse)&&this.restoreCollisionMask()},startBuildingCollisionResponse:function(){this.isDragging=this.isClicked=!1;this.pathHead&&(this.pathHead.kill(),this.pathHead=null);ig.currentCtrl&&ig.currentCtrl.currentVehicle===this&&(ig.currentCtrl.currentVehicle=null);this.isInBuildingCollisionResponse=!0;this.buildingCollisionResponseProgress=0;this.targetAngle=this.normalizeAngle(this.angle+
Math.PI);this._originalCollidesWith=this.collidesWith?ig.copy(this.collidesWith):[];this.setCollisionMaskForResponse(!0);this.vel.x=0;this.vel.y=0},updateBuildingCollisionResponse:function(){this.buildingCollisionResponseProgress+=this.buildingCollisionResponseSpeed;if(1<=this.buildingCollisionResponseProgress)this.finishBuildingCollisionResponse();else{var a=this.getShortestRotation(this.angle,this.targetAngle),b=Math.sin(this.buildingCollisionResponseProgress*Math.PI);this.angle+=12*(a*this.buildingCollisionResponseSpeed)*
b;this.angle=this.normalizeAngle(this.angle);this.angleChangedThisFrame=!0;0.3<this.buildingCollisionResponseProgress&&(a=(this.buildingCollisionResponseProgress-0.3)/0.7,a*=0.4*this.speed*a,b=this.getDirectionFromAngle(),this.vel.x=b.x*a,this.vel.y=b.y*a)}},finishBuildingCollisionResponse:function(){this.angle=this.targetAngle;this.angleChangedThisFrame=!0;this.isInBuildingCollisionResponse=!1;this.buildingCollisionResponseProgress=0;var a=this.getDirectionFromAngle();this.vel.x=0.6*a.x*this.speed;
this.vel.y=0.6*a.y*this.speed;this.delayedCall(0.3,function(){this.setCollisionMaskForResponse(!1)}.bind(this))},restoreCollisionMask:function(){this._originalCollidesWith&&(this.collidesWith=ig.copy(this._originalCollidesWith));this.needsCollisionRestore=!1},isRespondingToCollision:function(){return this.isInBuildingCollisionResponse},setCollisionMaskForResponse:function(a){a?this.collidesWith&&(this.collidesWith=this.collidesWith.filter(function(a){return a!==(LevelData.COLLISION_TYPES.BUILDING||
1)&&a!==(LevelData.COLLISION_TYPES.BOUNDARY||2)})):this.needsCollisionRestore=!0},checkOffScreen:function(a){if(!this.isInBuildingCollisionResponse&&!(0<this.collisionCooldown||this.isParkingEngaged())){var b=!1;if(this.vertices&&0<this.vertices.length)if(a)for(var b=!0,c=0;c<this.vertices.length;c++){var d=this.vertices[c];if(0<=d.x&&d.x<=ig.system.width&&0<=d.y&&d.y<=ig.system.height){b=!1;break}}else if(this.attackShape&&this.attackShape.pointList&&0<this.attackShape.pointList.length)for(c=0;c<
this.attackShape.pointList.length;c++){if(d=this.attackShape.pointList[c],0>d.x||d.x>ig.system.width||0>d.y||d.y>ig.system.height){b=!0;break}}else for(c=0;c<this.vertices.length;c++){if(d=this.vertices[c],0>d.x||d.x>ig.system.width||0>d.y||d.y>ig.system.height){b=!0;break}}else b=a?0>this.pos.x+this.size.x||this.pos.x>ig.system.width||0>this.pos.y+this.size.y||this.pos.y>ig.system.height:0>this.pos.x||this.pos.x+this.size.x>ig.system.width||0>this.pos.y||this.pos.y+this.size.y>ig.system.height;if(b)if(a)this.onExitScreen();
else this.onCollision({type:LevelData.COLLISION_TYPES.BOUNDARY,name:"screen-boundary"})}},onGameOver:function(){this.vel.x=0;this.vel.y=0;this.isSpawning=!1;this.resetWaypointTimeout();this.clearTrajectory()},onExitScreen:function(){this.kill();ig.currentCtrl&&"function"===typeof ig.currentCtrl.addScore&&ig.currentCtrl.addScore(1)}})});ig.baked=!0;
ig.module("game.controllers.spawn-controller").requires("impact.impact","game.data.level-data","game.entities.warning-signal","game.entities.spawn-grid","game.entities.objects.truck").defines(function(){SpawnController=ig.Class.extend({levelData:null,currentLevelId:null,truckSpawnAreas:[],occupiedSpawnAreas:[],spawningDisabled:!1,spawnInterval:5E3,spawnTimer:null,parkingSlots:null,availableParkingColors:null,isFirstSpawn:!0,init:function(a){this.spawnTimer=new ig.Timer;this.spawnTimer.set(1);this.levelData=
a.currentLevelData;this.currentLevelId=a.currentLevelId;this.occupiedSpawnAreas=[];!this.levelData||!this.currentLevelId?(console.error("SpawnController: LevelData or CurrentLevelId not available from gameController."),this.spawningDisabled=!0):(this.truckSpawnAreas=this.levelData.truckSpawnAreas||[],0===this.truckSpawnAreas.length&&console.warn("SpawnController: No truckSpawnAreas defined for level: "+this.currentLevelId))},pauseGame:function(){this.spawnTimer&&this.spawnTimer.pause();for(var a=
ig.game.getEntitiesByType(EntityWarningSignal),b=0;b<a.length;b++)a[b].pauseBlinking()},resumeGame:function(){this.spawnTimer&&this.spawnTimer.unpause();for(var a=ig.game.getEntitiesByType(EntityWarningSignal),b=0;b<a.length;b++)a[b].resumeBlinking()},update:function(){!this.spawningDisabled&&(!ig.game.isPaused&&!ig.currentCtrl.isGameOver)&&(this.spawnTimer&&0<this.spawnTimer.delta())&&(this.spawnTruckSequence(),this.spawnTimer=null)},getSpawnPosition:function(a){var b=ig.game.getEntitiesByType(EntitySpawnGrid);
if(!b||0===b.length)return console.error("SpawnController: EntitySpawnGrid not found. Cannot determine spawn positions."),null;var c=b[0];if(!c.allTextObj||0===c.allTextObj.length)return console.error("SpawnController: EntitySpawnGrid has no text objects (allTextObj) to define spawn points."),null;for(var b=[{texts:c.topText,dir:1},{texts:c.botText,dir:3},{texts:c.leftText,dir:0},{texts:c.rightText,dir:2}],d=0;d<c.allTextObj.length;d++){var e=c.allTextObj[d];if(e.name===a){c=null;for(d=0;d<b.length;d++)if(-1!==
b[d].texts.indexOf(e.name)){c=b[d].dir;break}return null===c?(console.warn("SpawnController: Spawn area '"+a+"' found, but its direction couldn't be determined."),null):{x:e.pos.x,y:e.pos.y,dir:c}}}console.warn("SpawnController: Spawn area name '"+a+"' not found in EntitySpawnGrid.");return null},spawnTruckSequence:function(){if(!this.spawningDisabled&&this.truckSpawnAreas&&0!==this.truckSpawnAreas.length){var a=this.truckSpawnAreas.filter(function(a){return-1===this.occupiedSpawnAreas.indexOf(a)},
this);if(0!==a.length){var b=Math.floor(Math.random()*a.length),c=a[b];if(a=this.getSpawnPosition(c))if(this.occupiedSpawnAreas.push(c),a=ig.game.spawnEntity(EntityWarningSignal,a.x,a.y,{blinkCount:this.isFirstSpawn?3:6,blinkDuration:1,direction:a.dir})){var d=this;a.onBlinkingComplete=function(){d.spawnActualTruck(c);d.spawnTimer=new ig.Timer(d.spawnInterval/1E3);d.isFirstSpawn=!1;var a=d.occupiedSpawnAreas.indexOf(c);-1<a?d.occupiedSpawnAreas.splice(a,1):console.warn("SpawnController: Tried to unmark area '"+
c+"' but it was not in the occupied list.");this.kill()}}else console.error("SpawnController: Failed to spawn EntityWarningSignal for area '"+c+"'."),a=this.occupiedSpawnAreas.indexOf(c),-1<a&&this.occupiedSpawnAreas.splice(a,1);else console.error("SpawnController: Could not get spawn position for area '"+c+"'. Aborting this truck spawn.")}}},spawnActualTruck:function(a){if(a){var b=this.assignColor(a);(b=ig.game.spawnEntity(EntityTruck,0,0,{spawnPoint:a,color:b.color,intendedParkingSlotName:b.intendedParkingSlotName}))?
ig.currentCtrl.setupTruckCollision(b):console.error("SpawnController: Failed to spawn EntityTruck at area: "+a)}else console.error("SpawnController: Invalid area name provided for spawning truck.")},assignColor:function(a){if(!this.availableParkingColors&&!this.parkingSlots){this.availableParkingColors=[];this.parkingSlots=ig.game.getEntitiesByType(EntityParkingSlot);for(var b=0;b<this.parkingSlots.length;b++){var c=this.parkingSlots[b];c.color&&-1===this.availableParkingColors.indexOf(c.color)&&
this.availableParkingColors.push(c.color)}}var d=null;0<this.availableParkingColors.length&&(d=(a=this.handleSpecialSpawnCase(a))?a[Math.floor(Math.random()*a.length)]:this.availableParkingColors[Math.floor(Math.random()*this.availableParkingColors.length)]);this.handleSpecialSpawnCase();a=this.parkingSlots.filter(function(a){return a.color===d},this);this.intendedParkingSlotName=0<a.length?a[0].name:null;return{color:d,intendedParkingSlotName:this.intendedParkingSlotName}},handleSpecialSpawnCase:function(a){if("level3"===
ig.currentCtrl.currentLevelId){var b=["topLeft","top1","left1","botLeft","bot1"],c=["top10","topRight","right1","bot10","botRight"],d=["red","green"],e=["blue","yellow"],g=ig.copy(this.availableParkingColors);if(a)return-1!==b.indexOf(a)?g=g.filter(function(a){return-1!==d.indexOf(a)}):-1!==c.indexOf(a)&&(g=g.filter(function(a){return-1!==e.indexOf(a)})),g;a=this.truckSpawnAreas.filter(function(a){return-1!==b.indexOf(a)&&-1===this.occupiedSpawnAreas.indexOf(a)},this);var j=this.truckSpawnAreas.filter(function(a){return-1!==
c.indexOf(a)&&-1===this.occupiedSpawnAreas.indexOf(a)},this);0===a.length?g=g.filter(function(a){return-1===d.indexOf(a)}):0===j.length&&(g=g.filter(function(a){return-1===e.indexOf(a)}));return g}}})});ig.baked=!0;
ig.module("game.entities.marketjs-splash").requires("impact.entity").defines(function(){EntityMarketJSSplash=ig.Entity.extend({logoImage:new ig.Image("media/graphics/splash/marketjs/logo.png"),letterImage:new ig.Image("media/graphics/splash/marketjs/letter-m.png"),brandingText:new ig.Image("media/graphics/splash/marketjs/branding-text.png"),scale:{x:1,y:1},init:function(){if(_SETTINGS.DeveloperBranding.Splash.Enabled){this.updateScale();this.originX=0;this.logo={scale:0.02,alpha:0,x:-this.logoImage.width/
2,y:-this.logoImage.height/2};var a=this.tween({logo:{scale:1,alpha:1}},0.48,{delay:0.3,easing:ig.Tween.Easing.Back.EaseOut,onComplete:function(){ig.soundHandler.sfxPlayer.play("logosplash1");this.tween({},0.59,{onComplete:function(){ig.soundHandler.sfxPlayer.play("logosplash2")}}).start()}.bind(this)});this.letterM={scale:0.02,alpha:0,x:-this.letterImage.width/2,y:-this.letterImage.height/2-10};var b=this.tween({letterM:{scale:1,alpha:1}},0.48,{easing:ig.Tween.Easing.Back.EaseOut});this.text={alpha:1,
x:-this.brandingText.width/2,y:-this.brandingText.height/2,coverW:2*this.brandingText.width,coverH:2*this.brandingText.height};this.text.originX=-this.brandingText.width/2-270;this.text.cx=-this.text.coverW-260;this.text.cy=-this.brandingText.height;var c=this.tween({originX:-450,logo:{scale:0.82},letterM:{scale:0.82},text:{originX:150}},0.78,{delay:0.4,easing:ig.Tween.Easing.Back.EaseOut}),d=this.tween({logo:{alpha:0},letterM:{alpha:0},text:{alpha:0}},0.9,{delay:1.5,onComplete:function(){this.goToMenu()}.bind(this)});
a.chain(b);b.chain(c);c.chain(d);a.start();this.tween({},0.25,{onComplete:function(){ig.soundHandler.sfxPlayer.play("logosplash1")}}).start()}else this.goToMenu()},goToMenu:function(){ig.soundHandler.sfxPlayer.soundList.logosplash1.mute(!0);ig.soundHandler.sfxPlayer.soundList.logosplash2.mute(!0);ig.soundHandler.bgmPlayer.play("background");ig.game.director.nextLevel();this.kill()},update:function(){if(0<this.tweens.length){for(var a=[],b=0;b<this.tweens.length;b++)this.tweens[b].update(),this.tweens[b].complete||
a.push(this.tweens[b]);this.tweens=a}this.updateScale()},draw:function(){var a=ig.system.context;a.fillStyle="#FFF";a.fillRect(0,0,ig.system.width,ig.system.height);a.save();a.translate(ig.system.width/2,ig.system.height/2);a.scale(this.scale.x,this.scale.y);a.imageSmoothingEnabled=!0;a.globalAlpha=this.text.alpha;this.brandingText.draw(this.text.originX+this.text.x,this.text.y);a.globalAlpha=1;a.fillRect(this.text.cx,this.text.cy,this.originX-this.text.cx,this.text.coverH);a.save();a.globalAlpha=
this.logo.alpha;a.scale(this.logo.scale,this.logo.scale);this.logoImage.draw(this.originX+this.logo.x,this.logo.y);a.restore();a.save();a.globalAlpha=this.letterM.alpha;a.scale(this.letterM.scale,this.letterM.scale);this.letterImage.draw(this.originX+this.letterM.x,this.letterM.y);a.restore();a.restore()},updateScale:function(){if(!this.skipUpdateScale){var a=window.innerWidth/window.innerHeight,b=ig.system.height/1280,c=ig.system.width/1280;if(ig.responsive)this.scale.x=this.scale.y=1<a?b:c;else if(ig.ua.mobile&&
!ig.sizeHandler.disableStretchToFitOnMobileFlag){var d=ig.system.width/ig.system.height;1<a?(this.scale.x=d/a*b,this.scale.y=b):(this.scale.x=c,this.scale.y=a/d*c)}else this.skipUpdateScale=!0,c=ig.system.width/1080,this.scale.x=this.scale.y=b<c?b:c;1<this.scale.x&&(this.scale.x=1);1<this.scale.y&&(this.scale.y=1)}}})});ig.baked=!0;
ig.module("game.levels.opening").requires("impact.image","game.entities.marketjs-splash").defines(function(){LevelOpening={entities:[{type:"EntityMarketJSSplash",x:0,y:0}],layer:[]}});ig.baked=!0;
ig.module("plugins.utils.transition").requires("impact.entity","impact.timer").defines(function(){EntityTransition=ig.Entity.extend({_wmIgnore:!0,zIndex:9999,fadeAlpha:1,isTransitioning:!1,color:null,init:function(a,b,c){this.parent(a,b,c);this.color=c.color||"white";ig.game.sortEntitiesDeferred()},fadeIn:function(a,b,c){if(!this.isTransitioning){this.isTransitioning=!0;var d={fadeAlpha:0};(new ig.TweenDef(d)).to({fadeAlpha:1},a||500).easing(c||ig.Tween.Easing.Quadratic.EaseIn).onUpdate(function(){this.fadeAlpha=
d.fadeAlpha}.bind(this)).onComplete(function(){this.isTransitioning=!1;b&&b()}.bind(this)).start()}},fadeOut:function(a,b,c,d){if(!this.isTransitioning){this.isTransitioning=!0;var e={fadeAlpha:1};(new ig.TweenDef(e)).to({fadeAlpha:0},a||700).easing(c||ig.Tween.Easing.Quadratic.EaseOut).delay(d||0).onUpdate(function(){this.fadeAlpha=e.fadeAlpha}.bind(this)).onComplete(function(){this.isTransitioning=!1;b&&b()}.bind(this)).start()}},flash:function(a,b){this.fadeIn(a/2,function(){this.fadeOut(a/2,b)}.bind(this))},
draw:function(){if(0<this.fadeAlpha){var a=ig.system.context;a.save();"white"===this.color?a.fillStyle="rgba(255, 255, 255, "+this.fadeAlpha+")":"black"===this.color?a.fillStyle="rgba(0, 0, 0, "+this.fadeAlpha+")":(a.fillStyle=this.color,a.globalAlpha=this.fadeAlpha);a.fillRect(0,0,ig.system.width,ig.system.height);a.restore()}}})});ig.baked=!0;
ig.module("plugins.utils.buttons.button-text").requires("plugins.utils.buttons.button-base").defines(function(){EntityButtonText=EntityButtonBase.extend({textConfigs:{fontSize:24,fontFace:"Arial",fontColor:"#000",text:"ButtonBase\nText",width:0,height:0,align:"center",vAlign:"middle",strokeColor:"#000",strokeWidth:0,justify:!1,inferWhitespace:!0,overflow:!0,debug:!1},textPosOffset:{x:0,y:0},init:function(a,b,c){c.idleSheetInfo&&(this.idleSheetInfo=c.idleSheetInfo);this.parent(a,b,c)},onClickCallback:function(){}})});
ig.baked=!0;ig.module("plugins.utils.buttons.button-image").requires("plugins.utils.buttons.button-base").defines(function(){EntityButtonImage=EntityButtonBase.extend({hasText:!1,init:function(a,b,c){c.idleSheetInfo&&(this.idleSheetInfo=c.idleSheetInfo);this.parent(a,b,c)},onClickCallback:function(){}})});ig.baked=!0;
ig.module("plugins.utils.buttons.button-sound").requires("plugins.utils.buttons.button-image").defines(function(){EntityButtonSound=EntityButtonImage.extend({idleSheetInfo:{sheetImage:new ig.Image("media/graphics/sprites/ui/btn-blank.png"),frameCountX:1,frameCountY:1},volume:0.5,mutedFlag:!1,init:function(a,b,c){this.parent(a,b,c);this.anims.off=new ig.Animation(this.idleSheet,1,[0],!0);this.anims.on=new ig.Animation(this.idleSheet,1,[1],!0);this.mutedFlag=0===ig.game.load("music");this.handleOnLoadAudio()},
onClickCallback:function(){this.handleAudio()},handleAudio:function(){this.mutedFlag?(this.currentAnim=this.anims.on,ig.soundHandler.bgmPlayer.volume(this.volume),ig.soundHandler.sfxPlayer.volume(this.volume),ig.soundHandler.bgmPlayer.soundList.background.playing()||ig.soundHandler.bgmPlayer.play("background"),ig.game.save("music",0.5),ig.game.save("sound",0.5),this.mutedFlag=!1):(this.currentAnim=this.anims.off,ig.soundHandler.bgmPlayer.volume(0),ig.soundHandler.sfxPlayer.volume(0),ig.game.save("music",
0),ig.game.save("sound",0),this.mutedFlag=!0)},handleOnLoadAudio:function(){this.mutedFlag?(this.currentAnim=this.anims.off,ig.soundHandler.bgmPlayer.volume(0),ig.soundHandler.sfxPlayer.volume(0)):(this.currentAnim=this.anims.on,ig.soundHandler.bgmPlayer.volume(this.volume),ig.soundHandler.sfxPlayer.volume(this.volume))}})});ig.baked=!0;
ig.module("plugins.utils.buttons.button-factory").requires("impact.entity","plugins.utils.buttons.button-base","plugins.utils.buttons.button-text","plugins.utils.buttons.button-image","plugins.utils.buttons.button-sound").defines(function(){});ig.baked=!0;
ig.module("plugins.utils.objects.popup-base").requires("plugins.utils.entity-extended","plugins.utils.buttons.button-factory","game.entities.text").defines(function(){EntityPopupBase=ig.EntityExtended.extend({name:"popup-base",collides:ig.Entity.COLLIDES.NEVER,type:ig.Entity.TYPE.A,gravityFactor:0,idleSheetInfo:{sheetImage:new ig.Image("media/graphics/sprites/ui/popup.png"),frameCountX:1,frameCountY:1},zIndex:1E3,popupAlpha:0,isVisible:!1,displayOverlay:!0,isTweening:!1,elements:{},tweenPosY:500,
headerTextConfig:{text:"Popup Header",fontSize:48,fontFamily:"Arial",fontColor:"#000000",align:"center",vAlign:"middle"},headerTextOffset:{x:0,y:0},hasCloseButton:!0,init:function(a,b,c){c.idleSheetInfo&&(this.idleSheetInfo=c.idleSheetInfo);this.parent(a,b,c);this.zIndex=ig.game.LAYERS.POPUP;this.currentAnim=this.idle=new ig.Animation(this.idleSheet,1,[0],!0);this.currentAnim.alpha=this.popupAlpha;this.repos();this.elements.overlay=ig.game.spawnEntity(EntityPopupOverlay,0,0,{zIndex:this.zIndex-2,
displayOverlay:this.displayOverlay,opacityCap:this.opacityCap});this.hasCloseButton&&(this.elements.buttonClose=ig.game.spawnEntity(EntityButtonClosePopup,0,0,{zIndex:this.zIndex+2,popupEntity:this}));a={};ig.merge(a,this.headerTextConfig);c&&c.headerTextConfig&&ig.merge(a,c.headerTextConfig);a.width=a.width||this.size.x;a.height=a.height||0.2*this.size.y;c=c&&c.headerTextOffset?c.headerTextOffset:this.headerTextOffset;this.elements.headerText=ig.game.spawnEntity(EntityText,this.pos.x+(c.x||0),this.pos.y+
(c.y||0),{textConfig:a,alpha:this.popupAlpha,zIndex:this.zIndex+1});this.tweenEnter();ig.game.sortEntitiesDeferred()},enterCb:function(){},exitCb:function(){},onTweenUpdateEnter:function(){},onTweenUpdateExit:function(){},tweenEnter:function(){this.isTweening=this.isVisible=!0;var a=this.pos.y;this.pos.y+=this.tweenPosY;var b={popupAlpha:this.popupAlpha,posY:this.pos.y};(new ig.TweenDef(b)).to({popupAlpha:1},300).onUpdate(function(){this.popupAlpha=b.popupAlpha;this.updateElementsAlpha(this.popupAlpha)}.bind(this)).onComplete(function(){this.popupAlpha=
1;this.updateElementsAlpha(this.popupAlpha)}.bind(this)).start();(new ig.TweenDef(b)).easing(ig.Tween.Easing.Back.EaseOut).to({posY:a},300).onUpdate(function(){this.pos.y=b.posY;this.updateElementsPosition()}.bind(this)).onComplete(function(){this.pos.y=a;this.updateElementsPosition();this.isTweening=!1;"function"===typeof this.enterCb&&this.enterCb()}.bind(this)).start()},tweenExit:function(){this.isVisible=!1;this.isTweening=!0;var a={popupAlpha:this.popupAlpha,posY:this.pos.y},b=this.pos.y+this.tweenPosY;
(new ig.TweenDef(a)).to({popupAlpha:0},200).onUpdate(function(){this.popupAlpha=a.popupAlpha;this.updateElementsAlpha(this.popupAlpha)}.bind(this)).onComplete(function(){this.popupAlpha=0;this.updateElementsAlpha(this.popupAlpha)}.bind(this)).start();(new ig.TweenDef(a)).easing(ig.Tween.Easing.Back.EaseIn).to({posY:b},300).onUpdate(function(){this.pos.y=a.posY;this.updateElementsPosition()}.bind(this)).onComplete(function(){this.pos.y=b;this.updateElementsPosition();this.isTweening=!1;"function"===
typeof this.exitCb&&this.exitCb();this.delayedCall(0.05,this.kill.bind(this))}.bind(this)).start()},updateElementsAlpha:function(a){this.currentAnim&&(this.currentAnim.alpha=a);this.elements.overlay&&"function"===typeof this.elements.overlay.updateAlpha&&this.elements.overlay.updateAlpha(a);this.elements.buttonClose&&"function"===typeof this.elements.buttonClose.updateAlpha&&this.elements.buttonClose.updateAlpha(a);this.elements.headerText&&"function"===typeof this.elements.headerText.updateAlpha&&
this.elements.headerText.updateAlpha(a)},updateElementsPosition:function(){this.elements.buttonClose&&(this.elements.buttonClose.pos.x=this.pos.x+this.size.x-this.elements.buttonClose.size.x-10,this.elements.buttonClose.pos.y=this.pos.y+10);if(this.elements.headerText){var a=this.settings&&this.settings.headerTextOffset?this.settings.headerTextOffset:this.headerTextOffset;this.elements.headerText.pos.x=this.pos.x+(a.x||0);this.elements.headerText.pos.y=this.pos.y+(a.y||0)}},kill:function(){this.elements.overlay&&
this.elements.overlay.kill();this.elements.buttonClose&&this.elements.buttonClose.kill();this.elements.headerText&&this.elements.headerText.kill();this.elements={};this.parent()},draw:function(){var a=ig.system.context;a.save();a.shadowColor="rgba(0, 0, 0, "+0.5*this.popupAlpha+")";a.shadowBlur=20;a.shadowOffsetX=5;a.shadowOffsetY=5;this.parent();a.restore()},update:function(){this.parent()},repos:function(){this.isTweening||(this.pos.x=0.5*ig.system.width-0.5*this.size.x+ig.game.screen.x,this.pos.y=
0.5*ig.system.height-0.5*this.size.y+ig.game.screen.y,this.updateElementsPosition())}});EntityPopupOverlay=ig.Entity.extend({name:"popup-overlay",size:{x:0,y:0},zIndex:999,alpha:0,displayOverlay:!0,opacityCap:0.6,init:function(a,b,c){this.parent(0,0,c);void 0!==c.displayOverlay&&(this.displayOverlay=c.displayOverlay);c.zIndex&&(this._zIndex=this.zIndex=c.zIndex);this.size={x:ig.system.width,y:ig.system.height};void 0!==c.alpha&&(this.alpha=c.alpha)},updateAlpha:function(a){this.alpha=Math.min(this.opacityCap,
a)},show:function(){this.alpha=this.opacityCap;this.zIndex=this._zIndex;ig.game.sortEntitiesDeferred()},hide:function(){this.alpha=0;this._zIndex=this.zIndex;this.zIndex=-1;ig.game.sortEntitiesDeferred()},draw:function(){if(this.displayOverlay&&!(0>=this.alpha)){var a=ig.system.context;a.save();a.fillStyle="rgba(0, 0, 0, "+this.alpha+")";a.fillRect(this.pos.x-ig.game.screen.x,this.pos.y-ig.game.screen.y,this.size.x,this.size.y);a.restore()}},update:function(){this.pos.x=ig.game.screen.x;this.pos.y=
ig.game.screen.y;this.size.x=ig.system.width;this.size.y=ig.system.height;this.parent()}});EntityButtonClosePopup=EntityButtonImage.extend({name:"button-close-popup",idleSheetInfo:{sheetImage:new ig.Image("media/graphics/sprites/ui/btn-exit.png"),frameCountX:1,frameCountY:1},popupEntity:null,singleClickOnly:!0,init:function(a,b,c){this.parent(a,b,c);c.popupEntity&&(this.popupEntity=c.popupEntity);c.zIndex&&(this.zIndex=c.zIndex);this.alpha=void 0!==c.alpha?c.alpha:0},onClickCallback:function(){this.popupEntity&&
this.popupEntity.tweenExit()},updateAlpha:function(a){this.alpha=a;this.currentAnim&&(this.currentAnim.alpha=a)}})});ig.baked=!0;
ig.module("game.entities.buttons.button-nav").requires("plugins.utils.buttons.button-image").defines(function(){ig.EntityButtonNext=ig.global.EntityButtonNext=EntityButtonImage.extend({name:"button-next",idleSheetInfo:{sheetImage:new ig.Image("media/graphics/sprites/ui/btn-right.png"),frameCountX:1,frameCountY:1},init:function(a,b,c){this.parent(a,b,c)},onClickCallback:function(){if(this._parent)this._parent.onNext()}});ig.EntityButtonPrev=ig.global.EntityButtonPrev=EntityButtonImage.extend({name:"button-prev",
idleSheetInfo:{sheetImage:new ig.Image("media/graphics/sprites/ui/btn-left.png"),frameCountX:1,frameCountY:1},init:function(a,b,c){this.parent(a,b,c)},onClickCallback:function(){if(this._parent)this._parent.onPrev()}})});ig.baked=!0;
ig.module("game.entities.objects.popup-level-select").requires("plugins.utils.objects.popup-base","game.entities.text","game.entities.buttons.button-nav","game.data.level-data").defines(function(){EntityPopupLevelSelect=EntityPopupBase.extend({name:"popup-level-select",headerTextConfig:{text:"Level 1",fontSize:120,fontFamily:"bebasneue-bold",fontColor:"#FFFFFF",align:"center",vAlign:"middle",shadowEnabled:!0,shadowOffsetX:4,shadowOffsetY:4,shadowBlur:1,shadowColor:"#000000",overflow:!0},headerTextOffset:{x:0,
y:60},displayOverlay:!1,hasCloseButton:!0,thumbnailList:[new ig.Image("media/graphics/sprites/maps/map-1-preview.png"),new ig.Image("media/graphics/sprites/maps/map-2-preview.png"),new ig.Image("media/graphics/sprites/maps/map-3-preview.png")],thumbnailImage:null,currentThumbnailIndex:0,thumbnailBounds:null,init:function(a,b,c){this.parent(a,b,c);this.elements.buttons={};this.elements.buttons.next=this.spawnEntity(EntityButtonNext,this.pos.x,this.pos.y);this.elements.buttons.prev=this.spawnEntity(EntityButtonPrev,
this.pos.x,this.pos.y);this.levelDataManager=ig.currentCtrl.levelDataManager;this.thumbnailImage=this.thumbnailList[this.currentThumbnailIndex];this.elements.headerText.setTextContent(this.levelDataManager.getLevelData("level1").title);this.ctx=ig.system.context;this.updateButtonStates();ig.game.sortEntitiesDeferred()},onNext:function(){this.currentThumbnailIndex<this.thumbnailList.length-1&&(this.currentThumbnailIndex++,this.thumbnailImage=this.thumbnailList[this.currentThumbnailIndex],this.elements.headerText.setTextContent(this.levelDataManager.getLevelData("level"+
(this.currentThumbnailIndex+1)).title),this.updateButtonStates())},onPrev:function(){0<this.currentThumbnailIndex&&(this.currentThumbnailIndex--,this.thumbnailImage=this.thumbnailList[this.currentThumbnailIndex],this.elements.headerText.setTextContent(this.levelDataManager.getLevelData("level"+(this.currentThumbnailIndex+1)).title),this.updateButtonStates())},updateButtonStates:function(){this.elements.buttons&&(0>=this.currentThumbnailIndex?this.elements.buttons.prev.hide():this.elements.buttons.prev.show(),
this.currentThumbnailIndex>=this.thumbnailList.length-1?this.elements.buttons.next.hide():this.elements.buttons.next.show())},calculateThumbnailBounds:function(){this.thumbnailImage&&(this.thumbnailBounds={x:this.pos.x+0.5*this.size.x-0.5*this.thumbnailImage.width,y:this.pos.y+0.5*this.size.y+this.headerTextOffset.y-0.5*this.thumbnailImage.height,width:this.thumbnailImage.width,height:this.thumbnailImage.height})},isClickInThumbnail:function(a,b){return!this.thumbnailBounds?!1:a>=this.thumbnailBounds.x&&
a<=this.thumbnailBounds.x+this.thumbnailBounds.width&&b>=this.thumbnailBounds.y&&b<=this.thumbnailBounds.y+this.thumbnailBounds.height},onSelect:function(){ig.game.currentLevel=this.currentThumbnailIndex+1;ig.currentCtrl.transition.fadeIn(500,function(){ig.game.director.jumpTo(LevelGame)})},updateElementsAlpha:function(a){this.parent(a);if(this.elements.buttons)for(var b in this.elements.buttons)this.elements.buttons[b]&&"function"===typeof this.elements.buttons[b].updateAlpha&&this.elements.buttons[b].updateAlpha(a)},
updateElementsPosition:function(){this.parent();this.elements.buttons&&(this.elements.buttons.next.pos.x=this.pos.x+this.size.x-this.elements.buttons.next.size.x-20,this.elements.buttons.next.pos.y=this.pos.y+0.5*this.size.y,this.elements.buttons.prev.pos.x=this.pos.x+20,this.elements.buttons.prev.pos.y=this.elements.buttons.next.pos.y);this.elements.headerText&&this.elements.headerText._updateAnchorPosition()},exitCb:function(){ig.currentCtrl.tweenShow()},kill:function(){this.elements.buttons&&(this.elements.buttons.next.kill(),
this.elements.buttons.prev.kill());this.parent()},update:function(){this.parent();this.calculateThumbnailBounds();if(ig.input.released("click")&&!this.isTweening){var a=ig.game.io.getClickPos();if(this.isClickInThumbnail(a.x,a.y))this.onSelect()}},draw:function(){this.parent();this.ctx.save();this.ctx.globalAlpha=this.popupAlpha;this.thumbnailImage&&this.thumbnailImage.draw(this.pos.x+0.5*this.size.x-0.5*this.thumbnailImage.width,this.pos.y+0.5*this.size.y+this.headerTextOffset.y-0.5*this.thumbnailImage.height);
this.ctx.restore()}})});ig.baked=!0;
ig.module("game.entities.buttons.button-play").requires("plugins.utils.buttons.button-base","game.entities.objects.popup-level-select").defines(function(){ig.EntityButtonPlay=ig.global.EntityButtonPlay=EntityButtonBase.extend({name:"button-play",idleSheetInfo:{sheetImage:new ig.Image("media/graphics/sprites/ui/btn-play.png"),frameCountX:1,frameCountY:1},buttonTextConfig:{fontSize:72,fontFamily:"bebasneue-bold",fontColor:"#fff",text:_STRINGS.Game.Buttons.Play,align:"left",vAlign:"bottom"},hasText:!0,
buttonTextOffset:{x:0,y:0},init:function(a,b,c){this.parent(a,b,c);this.currentAnim=this.idle=new ig.Animation(this.idleSheet,1,[0],!0);this.buttonTextOffset.x=0.25*this.size.x;this.textEntity.anchorTo(this,{targetAnchor:{x:0,y:0},offset:this.buttonTextOffset});ig.game.sortEntitiesDeferred()},onClickCallback:function(){var a=getQueryVariable("level");if(a){ig.game.currentLevel=a;if(ig.currentCtrl.levelDataManager.getLevelData("level"+ig.game.currentLevel)){this._parent.transition.fadeIn(500,function(){ig.game.director.jumpTo(LevelGame)});
return}console.warn("Level: "+ig.game.currentLevel+" not found. Showing Level Select.")}this._parent.tweenHide();ig.game.spawnEntity(EntityPopupLevelSelect,0,0,{zIndex:ig.game.LAYERS.POPUP})},update:function(){this.parent()},draw:function(){this.parent()}})});ig.baked=!0;
ig.module("game.entities.buttons.button-audio-sfx").requires("plugins.utils.buttons.button-base").defines(function(){ig.EntityButtonAudioSFX=ig.global.EntityButtonAudioSFX=EntityButtonBase.extend({name:"button-audio-sfx",idleSheetInfo:{sheetImage:new ig.Image("media/graphics/sprites/ui/btn-sfx.png"),frameCountX:1,frameCountY:2},buttonTextConfig:{fontSize:72,fontFamily:"bebasneue-bold",fontColor:"#2d2d2d",text:_STRINGS.Game.Buttons.On,align:"center",vAlign:"bottom"},hasText:!0,buttonTextOffset:{x:0,
y:0},mutedFlag:!1,init:function(a,b,c){this.parent(a,b,c);this.mutedFlag=0===ig.game.load("sound");this.on=new ig.Animation(this.idleSheet,1,[0],!0);this.off=new ig.Animation(this.idleSheet,1,[1],!0);this.currentAnim=this.on;this.handleOnLoadAudio();ig.game.sortEntitiesDeferred()},handleAudio:function(){this.mutedFlag?(this.currentAnim=this.on,ig.soundHandler.sfxPlayer.volume(0.5),ig.game.save("sound",0.5),this.updateButtonLabelText(_STRINGS.Game.Buttons.On),this.mutedFlag=!1):(this.currentAnim=this.off,
ig.soundHandler.sfxPlayer.volume(0),ig.game.save("sound",0),this.updateButtonLabelText(_STRINGS.Game.Buttons.Off),this.mutedFlag=!0);this.needsRedraw=!0},handleOnLoadAudio:function(){this.mutedFlag?(this.currentAnim=this.off,this.updateButtonLabelText(_STRINGS.Game.Buttons.Off),ig.soundHandler.sfxPlayer.volume(0)):(this.currentAnim=this.on,this.updateButtonLabelText(_STRINGS.Game.Buttons.On),ig.soundHandler.sfxPlayer.volume(0.5));this.needsRedraw=!0},onClickCallback:function(){this.handleAudio()},
update:function(){this.parent()},draw:function(){this.parent()}})});ig.baked=!0;
ig.module("game.entities.buttons.button-audio-bgm").requires("plugins.utils.buttons.button-base").defines(function(){ig.EntityButtonAudioBGM=ig.global.EntityButtonAudioBGM=EntityButtonBase.extend({name:"button-audio-bgm",idleSheetInfo:{sheetImage:new ig.Image("media/graphics/sprites/ui/btn-bgm.png"),frameCountX:1,frameCountY:2},buttonTextConfig:{fontSize:72,fontFamily:"bebasneue-bold",fontColor:"#2d2d2d",text:_STRINGS.Game.Buttons.On,align:"center",vAlign:"bottom"},hasText:!0,buttonTextOffset:{x:0,
y:0},mutedFlag:!1,init:function(a,b,c){this.parent(a,b,c);this.mutedFlag=0===ig.game.load("music");this.on=new ig.Animation(this.idleSheet,1,[0],!0);this.off=new ig.Animation(this.idleSheet,1,[1],!0);this.currentAnim=this.on;this.handleOnLoadAudio();ig.game.sortEntitiesDeferred()},handleAudio:function(){this.mutedFlag?(this.currentAnim=this.on,ig.soundHandler.bgmPlayer.volume(0.5),ig.game.save("music",0.5),this.updateButtonLabelText(_STRINGS.Game.Buttons.On),this.mutedFlag=!1):(this.currentAnim=this.off,
ig.soundHandler.bgmPlayer.volume(0),ig.game.save("music",0),this.updateButtonLabelText(_STRINGS.Game.Buttons.Off),this.mutedFlag=!0);this.needsRedraw=!0},handleOnLoadAudio:function(){this.mutedFlag?(this.currentAnim=this.off,this.updateButtonLabelText(_STRINGS.Game.Buttons.Off),ig.soundHandler.bgmPlayer.volume(0)):(this.currentAnim=this.on,this.updateButtonLabelText(_STRINGS.Game.Buttons.On),ig.soundHandler.bgmPlayer.volume(0.5));this.needsRedraw=!0},onClickCallback:function(){this.handleAudio()},
update:function(){this.parent()},draw:function(){this.parent()}})});ig.baked=!0;
ig.module("game.entities.buttons.button-main-menu").requires("plugins.utils.buttons.button-base").defines(function(){ig.EntityButtonMainMenu=ig.global.EntityButtonMainMenu=EntityButtonBase.extend({name:"button-main-menu",idleSheetInfo:{sheetImage:new ig.Image("media/graphics/sprites/ui/btn-menu.png"),frameCountX:1,frameCountY:1},buttonTextConfig:{fontSize:48,fontFamily:"bebasneue-bold",fontColor:"#000000",text:_STRINGS.Game.Buttons.Menu,align:"center",vAlign:"middle"},hasText:!0,buttonTextOffset:{x:20,
y:5},init:function(a,b,c){this.parent(a,b,c);this.buttonTextConfig.width=0.9*this.size.x;this.buttonTextConfig.height=this.size.y;this.currentAnim=this.idle=new ig.Animation(this.idleSheet,1,[0],!0);ig.game.sortEntitiesDeferred()},onClickCallback:function(){ig.currentCtrl.transition.fadeIn(500,function(){ig.game.director.jumpTo(LevelMenu)}.bind(this))},update:function(){this.parent()},draw:function(){this.parent()}})});ig.baked=!0;
ig.module("game.entities.buttons.button-replay").requires("plugins.utils.buttons.button-base").defines(function(){ig.EntityButtonReplay=ig.global.EntityButtonReplay=EntityButtonBase.extend({name:"button-replay",idleSheetInfo:{sheetImage:new ig.Image("media/graphics/sprites/ui/btn-replay.png"),frameCountX:1,frameCountY:1},buttonTextConfig:{fontSize:48,fontFamily:"bebasneue-bold",fontColor:"#000000",text:_STRINGS.Game.Buttons.Replay,align:"center",vAlign:"middle"},hasText:!0,buttonTextOffset:{x:20,
y:5},init:function(a,b,c){this.parent(a,b,c);this.buttonTextConfig.width=0.9*this.size.x;this.buttonTextConfig.height=this.size.y;this.currentAnim=this.idle=new ig.Animation(this.idleSheet,1,[0],!0);ig.game.sortEntitiesDeferred()},onClickCallback:function(){ig.currentCtrl.transition.fadeIn(500,function(){ig.game.director.reloadLevel()}.bind(this))},update:function(){this.parent()},draw:function(){this.parent()}})});ig.baked=!0;
ig.module("game.entities.objects.popup-settings").requires("plugins.utils.objects.popup-base","game.entities.text","game.entities.buttons.button-audio-sfx","game.entities.buttons.button-audio-bgm","game.entities.buttons.button-main-menu","game.entities.buttons.button-replay").defines(function(){EntityPopupSettings=EntityPopupBase.extend({name:"popup-settings",headerTextConfig:{text:"Settings",fontSize:120,fontFamily:"bebasneue-bold",fontColor:"#FFFFFF",align:"center",vAlign:"middle",shadowEnabled:!0,
shadowOffsetX:4,shadowOffsetY:4,shadowBlur:1,shadowColor:"#000000",overflow:!0},headerTextOffset:{x:0,y:60},displayOverlay:!1,hasCloseButton:!0,init:function(a,b,c){this.parent(a,b,c);this.elements.buttons={};this.elements.buttons.sound=this.spawnEntity(EntityButtonAudioSFX,this.pos.x,this.pos.y);this.elements.buttons.bgm=this.spawnEntity(EntityButtonAudioBGM,this.pos.x,this.pos.y);ig.game.sortEntitiesDeferred()},updateElementsAlpha:function(a){this.parent(a);if(this.elements.buttons)for(var b in this.elements.buttons)this.elements.buttons[b]&&
"function"===typeof this.elements.buttons[b].updateAlpha&&this.elements.buttons[b].updateAlpha(a)},updateElementsPosition:function(){this.parent();this.elements.buttons&&(this.elements.buttons.sound.pos.x=this.pos.x+0.5*this.size.x-0.5*this.elements.buttons.sound.size.x,this.elements.buttons.sound.pos.y=this.pos.y+0.5*this.size.y-72,this.elements.buttons.bgm.pos.x=this.elements.buttons.sound.pos.x,this.elements.buttons.bgm.pos.y=this.elements.buttons.sound.pos.y+this.elements.buttons.sound.size.y+
60,this.elements.buttons.sound.textEntity._updateAnchorPosition(),this.elements.buttons.bgm.textEntity._updateAnchorPosition());this.elements.headerText&&this.elements.headerText._updateAnchorPosition()},exitCb:function(){ig.currentCtrl.tweenShow()},kill:function(){this.elements.buttons&&(this.elements.buttons.sound.kill(),this.elements.buttons.bgm.kill());this.parent()},update:function(){this.parent()}});EntityPopupSettingsGame=EntityPopupBase.extend({name:"popup-settings-game",headerTextConfig:{text:"Paused",
fontSize:120,fontFamily:"bebasneue-bold",fontColor:"#FFFFFF",align:"center",vAlign:"middle",shadowEnabled:!0,shadowOffsetX:4,shadowOffsetY:4,shadowBlur:1,shadowColor:"#000000",overflow:!0},headerTextOffset:{x:0,y:60},displayOverlay:!0,opacityCap:0.4,hasCloseButton:!0,init:function(a,b,c){this.parent(a,b,c);this.elements.buttons={};this.elements.buttons.sound=this.spawnEntity(EntityButtonAudioSFX,this.pos.x,this.pos.y);this.elements.buttons.bgm=this.spawnEntity(EntityButtonAudioBGM,this.pos.x,this.pos.y);
this.elements.buttons.menu=this.spawnEntity(EntityButtonMainMenu,this.pos.x,this.pos.y);this.elements.buttons.replay=this.spawnEntity(EntityButtonReplay,this.pos.x,this.pos.y);ig.game.sortEntitiesDeferred()},updateElementsAlpha:function(a){this.parent(a);if(this.elements.buttons)for(var b in this.elements.buttons)this.elements.buttons[b]&&"function"===typeof this.elements.buttons[b].updateAlpha&&this.elements.buttons[b].updateAlpha(a)},updateElementsPosition:function(){this.parent();this.elements.buttons&&
(this.elements.buttons.sound.pos.x=this.pos.x+0.5*this.size.x-0.5*this.elements.buttons.sound.size.x,this.elements.buttons.sound.pos.y=this.pos.y+0.5*this.size.y-108,this.elements.buttons.bgm.pos.x=this.elements.buttons.sound.pos.x,this.elements.buttons.bgm.pos.y=this.elements.buttons.sound.pos.y+this.elements.buttons.sound.size.y+60,this.elements.buttons.menu.pos.x=this.pos.x+0.5*this.size.x-this.elements.buttons.menu.size.x-20,this.elements.buttons.menu.pos.y=this.pos.y+0.8*this.size.y,this.elements.buttons.replay.pos.x=
this.pos.x+0.5*this.size.x+20,this.elements.buttons.replay.pos.y=this.elements.buttons.menu.pos.y,this.elements.buttons.sound.textEntity._updateAnchorPosition(),this.elements.buttons.bgm.textEntity._updateAnchorPosition(),this.elements.buttons.menu.textEntity._updateAnchorPosition(),this.elements.buttons.replay.textEntity._updateAnchorPosition());this.elements.headerText&&this.elements.headerText._updateAnchorPosition()},exitCb:function(){ig.currentCtrl.resumeGame()},kill:function(){this.elements.buttons&&
(this.elements.buttons.sound.kill(),this.elements.buttons.bgm.kill(),this.elements.buttons.menu.kill(),this.elements.buttons.replay.kill());this.parent()},update:function(){this.parent()}})});ig.baked=!0;
ig.module("game.entities.buttons.button-settings").requires("plugins.utils.buttons.button-base","game.entities.objects.popup-settings").defines(function(){ig.EntityButtonSettings=ig.global.EntityButtonSettings=EntityButtonBase.extend({name:"button-settings",idleSheetInfo:{sheetImage:new ig.Image("media/graphics/sprites/ui/btn-settings.png"),frameCountX:1,frameCountY:1},buttonTextConfig:{fontSize:72,fontFamily:"bebasneue-bold",fontColor:"#fff",text:_STRINGS.Game.Buttons.Settings,align:"left",vAlign:"bottom"},
hasText:!0,buttonTextOffset:{x:0,y:0},init:function(a,b,c){this.parent(a,b,c);this.currentAnim=this.idle=new ig.Animation(this.idleSheet,1,[0],!0);this.buttonTextOffset.x=0.25*this.size.x;this.textEntity.anchorTo(this,{targetAnchor:{x:0,y:0},offset:this.buttonTextOffset});ig.game.sortEntitiesDeferred()},onClickCallback:function(){this._parent.tweenHide();ig.game.spawnEntity(EntityPopupSettings,0,0,{zIndex:ig.game.LAYERS.POPUP})},update:function(){this.parent()},draw:function(){this.parent()}})});
ig.baked=!0;
ig.module("game.controllers.menu-controller").requires("plugins.utils.entity-extended","plugins.utils.transition","game.entities.buttons.button-play","game.entities.buttons.button-settings","game.entities.buttons.button-more-games").defines(function(){ig.MenuController=ig.global.MenuController=ig.EntityExtended.extend({zIndex:1,name:"menu-controller",bgImage:new ig.Image("media/graphics/sprites/ui/menu-bg.png"),titleImage:new ig.Image("media/graphics/sprites/ui/title.png"),positions:{title:{},playButton:{}},
buttons:{},transition:null,init:function(a,b,c){this.parent(a,b,c);this.ctx=ig.system.context;this.transition=ig.game.spawnEntity(EntityTransition,0,0,{color:"black"});this.transition.fadeOut(1E3);this.levelDataManager=new LevelData;this.initPositions();this.initButtons();this.tweenIn();window.menuctrl=this;ig.currentCtrl=this;ig.game.sortEntitiesDeferred()},initPositions:function(){this.positions.title.x=0;this.positions.title.y=0;this.positions.title._x=0.5*ig.system.width-0.5*this.titleImage.width;
this.positions.title._y=100;this.positions.title.alpha=0},initButtons:function(){this.buttons.updateAlpha=function(a){for(var b in this.buttons)this.buttons[b]&&"function"===typeof this.buttons[b].updateAlpha&&this.buttons[b].updateAlpha(a)}.bind(this);this.buttons.disable=function(){for(var a in this.buttons)this.buttons[a]&&"function"===typeof this.buttons[a].disable&&this.buttons[a].disable()}.bind(this);this.buttons.enable=function(){for(var a in this.buttons)this.buttons[a]&&"function"===typeof this.buttons[a].enable&&
this.buttons[a].enable()}.bind(this);this.buttons.play=this.spawnEntity(EntityButtonPlay,-999,-999);this.buttons.play.pos.x=0.5*ig.system.width-0.5*this.buttons.play.size.x;this.buttons.play.pos.y=this.positions.title._y+this.titleImage.height+80;this.buttons.settings=this.spawnEntity(EntityButtonSettings,-999,-999);this.buttons.settings.pos.x=0.5*ig.system.width-0.5*this.buttons.settings.size.x;this.buttons.settings.pos.y=this.buttons.play.pos.y+this.buttons.play.size.y+20;this.buttons.moreGames=
this.spawnEntity(EntityButtonMoreGames,-999,-999);this.buttons.moreGames.pos.x=0.5*ig.system.width-0.5*this.buttons.moreGames.size.x;this.buttons.moreGames.pos.y=this.buttons.settings.pos.y+this.buttons.settings.size.y+20;this.buttons.fullscreen=this.spawnEntity(ig.FullscreenButton,20,20)},tweenIn:function(){this.positions.title.x=this.positions.title._x;this.positions.title.y=this.positions.title._y-100;this.positions.title.alpha=0;var a=(new ig.TweenDef(this.positions.title)).to({x:this.positions.title._x,
y:this.positions.title._y,alpha:1},800).easing(ig.Tween.Easing.Cubic.EaseOut);this.buttons.updateAlpha(0);this.buttons.disable();var b={value:0},c=(new ig.TweenDef(b)).to({value:1},800).easing(ig.Tween.Easing.Cubic.EaseOut).onUpdate(function(){this.buttons.updateAlpha(b.value)}.bind(this)).onComplete(function(){this.buttons.enable()}.bind(this));a.chain(c);a.start()},tweenHide:function(){(new ig.TweenDef(this.positions.title)).to({alpha:0},100).easing(ig.Tween.Easing.Cubic.EaseIn).start();this.buttons.updateAlpha(0);
this.buttons.disable();var a={value:1};(new ig.TweenDef(a)).to({value:0},100).easing(ig.Tween.Easing.Cubic.EaseIn).onUpdate(function(){this.buttons.updateAlpha(a.value)}.bind(this)).onComplete(function(){this.buttons.disable()}.bind(this)).start()},tweenShow:function(){(new ig.TweenDef(this.positions.title)).to({alpha:1},100).easing(ig.Tween.Easing.Cubic.EaseOut).start();this.buttons.updateAlpha(0);this.buttons.disable();var a={value:0};(new ig.TweenDef(a)).to({value:1},100).easing(ig.Tween.Easing.Cubic.EaseOut).onUpdate(function(){this.buttons.updateAlpha(a.value)}.bind(this)).onComplete(function(){this.buttons.enable()}.bind(this)).start()},
update:function(){this.parent()},draw:function(){this.parent();this.bgImage.draw(0,0);this.ctx.save();this.ctx.globalAlpha=this.positions.title.alpha;this.titleImage.draw(this.positions.title.x,this.positions.title.y);this.ctx.restore()}})});ig.baked=!0;ig.module("game.levels.menu").requires("game.controllers.menu-controller").defines(function(){LevelMenu={entities:[{type:"MenuController",x:0,y:0}],layer:[]}});ig.baked=!0;ig.module("plugins.utils.objects.popup").requires("impact.entity","plugins.utils.objects.popup-base").defines(function(){});
ig.baked=!0;
ig.module("game.entities.objects.popup-gameover").requires("plugins.utils.objects.popup-base","game.entities.text","game.entities.buttons.button-main-menu","game.entities.buttons.button-replay").defines(function(){EntityPopupGameOver=EntityPopupBase.extend({name:"popup-gameover",headerTextConfig:{text:"Game Over!",fontSize:120,fontFamily:"bebasneue-bold",fontColor:"#FFFFFF",align:"center",vAlign:"middle",shadowEnabled:!0,shadowOffsetX:4,shadowOffsetY:4,shadowBlur:1,shadowColor:"#000000",overflow:!0},
bodyTextConfig:{text:"Score: 0\nBest: 0",fontSize:84,fontFamily:"bebasneue-bold",fontColor:"#FFFFFF",align:"center",vAlign:"middle",shadowEnabled:!0,shadowOffsetX:4,shadowOffsetY:4,shadowBlur:1,shadowColor:"#000000",overflow:!0},bodyTextOffset:{x:0,y:0},headerTextOffset:{x:0,y:60},currentScore:1,bestScore:2,displayOverlay:!1,hasCloseButton:!1,init:function(a,b,c){c&&"undefined"!==typeof c.currentScore&&(this.currentScore=c.currentScore);c&&"undefined"!==typeof c.bestScore&&(this.bestScore=c.bestScore);
this.bodyTextConfig.text="Score: "+this.currentScore+"\nBest: "+this.bestScore;this.parent(a,b,c);a={};ig.merge(a,this.bodyTextConfig);c&&c.bodyTextConfig&&ig.merge(a,c.bodyTextConfig);a.width=a.width||0.8*this.size.x;a.height=a.height||0.3*this.size.y;c=c&&c.bodyTextOffset?c.bodyTextOffset:this.bodyTextOffset;this.elements.bodyText=ig.game.spawnEntity(EntityText,this.pos.x,this.pos.y,{textConfig:a,alpha:this.popupAlpha,zIndex:this.zIndex+1});this.elements.bodyText&&this.elements.bodyText.anchorTo(this,
{targetAnchor:{x:0,y:0},selfAnchor:"center-middle",offset:c});this.elements.buttons={};this.elements.buttons.menu=this.spawnEntity(EntityButtonMainMenu,this.pos.x,this.pos.y);this.elements.buttons.replay=this.spawnEntity(EntityButtonReplay,this.pos.x,this.pos.y);this.updateScores(this.currentScore,this.bestScore);ig.game.sortEntitiesDeferred()},updateScores:function(a,b){this.currentScore=a;this.bestScore=b;var c="Score: "+this.currentScore+"\nBest: "+this.bestScore;this.elements.bodyText?this.elements.bodyText.setTextContent(c):
this.bodyTextConfig.text=c},updateElementsAlpha:function(a){this.parent(a);this.elements.bodyText&&"function"===typeof this.elements.bodyText.updateAlpha&&this.elements.bodyText.updateAlpha(a);if(this.elements.buttons)for(var b in this.elements.buttons)this.elements.buttons[b]&&"function"===typeof this.elements.buttons[b].updateAlpha&&this.elements.buttons[b].updateAlpha(a)},updateElementsPosition:function(){this.parent();this.elements.buttons&&(this.elements.buttons.menu.pos.x=this.pos.x+0.5*this.size.x-
this.elements.buttons.menu.size.x-20,this.elements.buttons.menu.pos.y=this.pos.y+0.8*this.size.y,this.elements.buttons.replay.pos.x=this.pos.x+0.5*this.size.x+20,this.elements.buttons.replay.pos.y=this.elements.buttons.menu.pos.y,this.elements.buttons.menu.textEntity._updateAnchorPosition(),this.elements.buttons.replay.textEntity._updateAnchorPosition());this.elements.bodyText&&this.elements.bodyText._updateAnchorPosition();this.elements.headerText&&this.elements.headerText._updateAnchorPosition()},
kill:function(){this.elements.bodyText&&this.elements.bodyText.kill();this.elements.buttons&&(this.elements.buttons.menu.kill(),this.elements.buttons.replay.kill());this.parent()},update:function(){this.parent()}})});ig.baked=!0;
ig.module("game.entities.buttons.button-pause").requires("plugins.utils.buttons.button-base","game.entities.objects.popup-settings").defines(function(){ig.EntityButtonPause=ig.global.EntityButtonPause=EntityButtonImage.extend({name:"button-pause",idleSheetInfo:{sheetImage:new ig.Image("media/graphics/sprites/ui/btn-pause.png"),frameCountX:1,frameCountY:1},init:function(a,b,c){this.parent(a,b,c)},onClickCallback:function(){ig.currentCtrl.pauseGame();ig.game.spawnEntity(EntityPopupSettingsGame,0,0,
{zIndex:ig.game.LAYERS.POPUP})}})});ig.baked=!0;
ig.module("game.controllers.game-controller").requires("plugins.utils.entity-extended","plugins.utils.buttons.button-factory","plugins.utils.objects.popup","game.data.level-data","game.entities.ui.drag-point","game.entities.objects.truck","game.entities.spawn-grid","game.entities.objects.parking-slot","game.entities.objects.popup-gameover","game.entities.text","plugins.utils.transition","game.entities.buttons.button-pause").defines(function(){ig.GameController=ig.global.GameController=ig.EntityExtended.extend({zIndex:1,
name:"game-controller",bgImage:null,levelDataManager:null,currentLevelData:null,collisionShapesData:[],trucksCollisionStatus:{},previousCollisionStatus:{},sat:null,isGameOver:!1,score:0,init:function(a,b,c){this.parent(a,b,c);window.gamectrl=this;ig.currentCtrl=this;ig.game.isPaused=!1;this.ctx=ig.system.context;this.sat=new ig.SAT;this.trucksCollisionStatus={};this.previousCollisionStatus={};this.levelDataManager=new LevelData;this.spawnGrid=ig.game.spawnEntity(EntitySpawnGrid,0,0,{isHideDraw:!0});
this.setupLevel("level"+ig.game.currentLevel);this.spawnController=new SpawnController(this);this.scoreText=ig.game.spawnEntity(EntityText,0,0,{zIndex:this.zIndex+20,textConfig:{fontSize:72,fontFamily:"bebasneue-bold",fontColor:"#fff",text:"0",align:"center",vAlign:"bottom",shadowEnabled:!0,shadowOffsetX:3,shadowOffsetY:3,shadowBlur:1,width:200,height:70}});this.scoreText.anchorTo({pos:{x:0,y:0},size:{x:1920,y:1080}},{targetAnchor:{x:0,y:-1},selfAnchor:"top",offset:{x:0,y:20}});this.transition=ig.game.spawnEntity(EntityTransition,
0,0,{color:"black"});this.transition.fadeOut(1E3);this.buttonPause=ig.game.spawnEntity(EntityButtonPause,-999,-999,{zIndex:ig.game.LAYERS.UI});this.buttonPause.pos.x=ig.system.width-this.buttonPause.size.x-20;this.buttonPause.pos.y=20;ig.game.sortEntitiesDeferred()},setupLevel:function(a){this.currentLevelId=a;(this.currentLevelData=this.levelDataManager.getLevelData(this.currentLevelId))?(this.bgImage=this.levelDataManager.getMapImage(this.currentLevelId),this.collisionShapesData=[],this.loadCollisionData()):
console.error("GameController: Failed to load level data for:",this.currentLevelId)},loadCollisionData:function(){if(this.currentLevelData.buildings&&this.currentLevelData.buildings.length){for(var a=0;a<this.currentLevelData.buildings.length;a++){var b=this.currentLevelData.buildings[a];if(b.vertices&&b.vertices.length){var c=new ig.SAT.Shape(b.vertices),d=this.calculateAABB(b.vertices);if(c&&d)if(b.type===LevelData.COLLISION_TYPES.PARKING_SLOT){for(var c=b.vertices[0].x,d=b.vertices[0].y,e=1;e<
b.vertices.length;e++)c=Math.min(c,b.vertices[e].x),d=Math.min(d,b.vertices[e].y);ig.game.spawnEntity(EntityParkingSlot,c,d,{name:b.name||"unnamed_slot_"+a,color:b.color,vertices:b.vertices,isOccupied:!1,colorIndicator:{anchor:b.colorIndicator.anchor,anchorOffset:b.colorIndicator.anchorOffset},frontPoint:b.frontPoint,backPoint:b.backPoint,swapPoints:b.swapPoints})}else this.collisionShapesData.push({shape:c,aabb:d,type:b.type||LevelData.COLLISION_TYPES.NONE,name:b.name||"unnamed_building_"+a,color:b.color||
"blue"})}}this.setupParkingSlots()}},setupParkingSlots:function(){this.parkingSlots=[];var a=ig.game.getEntitiesByType(EntityParkingSlot);if(!a||0===a.length)console.warn("GameController: No EntityParkingSlot instances found in the game.");else for(var b=0;b<a.length;b++){var c=a[b];if(!c.worldShape||!c.worldShape.pointList||0===c.worldShape.pointList.length)console.warn("GameController: EntityParkingSlot '"+c.name+"' has no worldShape or pointList. Skipping.");else{var d=c.worldShape.pointList.map(function(a){return{x:a.x,
y:a.y}}),e=ig.utils.getParkingSlotGeometry(d);if(e){var g=c.getSlotAngle(),d={center:e.center,vertices:d,name:c.name||"Slot_"+b,orientationAngle:g,slotLength:e.length,slotWidth:e.width,color:c.color};c.slotData=d;d.slotEntity=c;this.parkingSlots.push(d)}else console.warn("GameController: Could not calculate geometry for slot '"+c.name+"'. Skipping.")}}},setupTruckCollision:function(a){a&&(a.collisionType=LevelData.COLLISION_TYPES.TRUCK,a._originalCollidesWith||(a._originalCollidesWith=[LevelData.COLLISION_TYPES.BUILDING,
LevelData.COLLISION_TYPES.BOUNDARY,LevelData.COLLISION_TYPES.PARKING_SLOT,LevelData.COLLISION_TYPES.TRUCK]),a.collidesWith=ig.copy(a._originalCollidesWith),this.trucksCollisionStatus[a.id]={isColliding:!1,collidedWith:null},this.previousCollisionStatus[a.id]={isColliding:!1,collidedWith:null})},getTruckCollisionData:function(a){if(!a||!a.shape)return console.log("DEBUG: getTruckCollisionData failed - truck:",!!a,"shape:",a?!!a.shape:"no truck"),null;if("function"===typeof a.updateRotatedVertices)a.updateRotatedVertices();
else return console.warn("DEBUG: truck.updateRotatedVertices not found for truck:",a.id),null;var b=this.calculateAABB(a.vertices);return!b?(console.warn("DEBUG: Failed to calculate AABB for truck:",a.id),null):{entity:a,shape:a.shape,attackShape:a.attackShape,aabb:b,type:a.collisionType,collidesWith:a.collidesWith||[],name:"Truck-"+a.id}},drawShapeByPoints:function(a,b){if(a&&a.length){this.ctx.save();this.ctx.fillStyle=b;this.ctx.globalAlpha=0.5;this.ctx.strokeStyle="black";this.ctx.lineWidth=1;
this.ctx.beginPath();this.ctx.moveTo(a[0].x,a[0].y);for(var c=1;c<a.length;c++)this.ctx.lineTo(a[c].x,a[c].y);this.ctx.closePath();this.ctx.fill();this.ctx.stroke();this.ctx.restore()}},calculateAABB:function(a){if(!a||0===a.length)return null;for(var b=a[0].x,c=a[0].y,d=a[0].x,e=a[0].y,g=1;g<a.length;g++)b=Math.min(b,a[g].x),c=Math.min(c,a[g].y),d=Math.max(d,a[g].x),e=Math.max(e,a[g].y);return{minX:b,minY:c,maxX:d,maxY:e}},checkAABBOverlap:function(a,b){return!a||!b||a.maxX<b.minX||a.minX>b.maxX||
a.maxY<b.minY||a.minY>b.maxY?!1:!0},update:function(){this.parent();if(!this.isGameOver){this.spawnController&&this.spawnController.update();var a=ig.game.getEntitiesByType(EntityTruck),b;for(b in this.trucksCollisionStatus)this.trucksCollisionStatus.hasOwnProperty(b)&&(this.previousCollisionStatus[b]=ig.copy(this.trucksCollisionStatus[b]));for(var c in this.trucksCollisionStatus)this.trucksCollisionStatus.hasOwnProperty(c)&&(this.trucksCollisionStatus[c].isColliding=!1,this.trucksCollisionStatus[c].collidedWith=
null);for(b=0;b<a.length;b++)if((c=a[b])&&!c._killed&&c.id)if(this.trucksCollisionStatus[c.id]||this.setupTruckCollision(c),c.needsCollisionRestore&&(c._originalCollidesWith?c.collidesWith=ig.copy(c._originalCollidesWith):(console.warn("GameController: Truck "+c.name+" needs collision restore but no _originalCollidesWith found. Setting to default."),c.collidesWith=[LevelData.COLLISION_TYPES.BUILDING,LevelData.COLLISION_TYPES.BOUNDARY,LevelData.COLLISION_TYPES.PARKING_SLOT,LevelData.COLLISION_TYPES.TRUCK]),
c.needsCollisionRestore=!1),c.shape){var d=this.getTruckCollisionData(c);if(d){this.checkTruckVsLevelCollision(d);for(var e=0;e<a.length;e++){var g=a[e];if(!(c===g||!g||g._killed||!g.shape)){var j=this.getTruckCollisionData(g);if(j&&d.collidesWith.includes(j.type)&&j.collidesWith.includes(d.type)&&this.checkAABBOverlap(d.aabb,j.aabb)){var p=!1;d.attackShape&&this.sat.simpleShapeIntersect(d.attackShape,j.shape)&&(p=!0);!p&&(j.attackShape&&this.sat.simpleShapeIntersect(j.attackShape,d.shape))&&(p=!0);
p&&(this.handleTruckVsTruckCollision(c,g),this.trucksCollisionStatus[c.id]={isColliding:!0,collidedWith:{type:j.type,name:j.name,entity:g}},this.trucksCollisionStatus[g.id]={isColliding:!0,collidedWith:{type:d.type,name:d.name,entity:c}})}}}}}for(b=0;b<a.length;b++)if((c=a[b])&&c.id&&this.trucksCollisionStatus[c.id]&&this.previousCollisionStatus[c.id])d=this.previousCollisionStatus[c.id],this.trucksCollisionStatus[c.id].isColliding&&!d.isColliding&&(c.collisionEntryTime=ig.system.clock.delta());this.checkCollisionExits(a)}},
checkCollisionExits:function(a){for(var b=0;b<a.length;b++){var c=a[b];if(c&&c.id&&this.previousCollisionStatus[c.id]&&this.trucksCollisionStatus[c.id]){var d=this.previousCollisionStatus[c.id],e=this.trucksCollisionStatus[c.id];if(d.isColliding&&!e.isColliding&&"function"===typeof c.onExitCollision&&d.collidedWith)c.onExitCollision(d.collidedWith)}}},checkTruckVsLevelCollision:function(a){if(a&&a.shape&&a.aabb){for(var b=a.entity,c=0;c<this.collisionShapesData.length;c++){var d=this.collisionShapesData[c];
if(a.collidesWith.includes(d.type)&&this.checkAABBOverlap(a.aabb,d.aabb)&&a.attackShape&&this.sat.simpleShapeIntersect(a.attackShape,d.shape)){this.handleTruckVsLevelCollision(b,d,d.type);this.trucksCollisionStatus[b.id]={isColliding:!0,collidedWith:{type:d.type,name:d.name,shapeData:d}};return}}c=ig.game.getEntitiesByType(EntityParkingSlot);for(d=0;d<c.length;d++){var e=c[d];if(a.collidesWith.includes(e.type)&&!(b.intendedParkingSlotName&&e.name!==b.intendedParkingSlotName)&&(e=c[d],a.collidesWith.includes(e.type)&&
!(b.intendedParkingSlotName&&e.name!==b.intendedParkingSlotName)))if(e.worldShape&&e.worldShape.pointList&&0<e.worldShape.pointList.length){var g=this.calculateAABB(e.worldShape.pointList);if(g&&this.checkAABBOverlap(a.aabb,g)&&a.attackShape&&this.sat.simpleShapeIntersect(a.attackShape,e.worldShape)){this.handleTruckVsLevelCollision(b,e,LevelData.COLLISION_TYPES.PARKING_SLOT);this.trucksCollisionStatus[b.id]={isColliding:!0,collidedWith:{type:e.type,name:e.name,entity:e}};break}}else console.warn("GameController: ParkingSlot "+
e.name+" has no valid worldShape for collision check.")}}},handleTruckVsLevelCollision:function(a,b,c){var d=a.isParking;switch(c){case LevelData.COLLISION_TYPES.BUILDING:case LevelData.COLLISION_TYPES.BOUNDARY:d||(a.vel.x=0,a.vel.y=0);break;case LevelData.COLLISION_TYPES.PARKING_SLOT:!d&&(a.intendedParkingSlotName===b.name&&"none"===a.truckState)&&(!a._originalCollidesWith&&a.collidesWith?(console.warn("GameController: truck._originalCollidesWith was not set for "+a.name+". Initializing from current collidesWith."),
a._originalCollidesWith=ig.copy(a.collidesWith)):!a._originalCollidesWith&&!a.collidesWith&&(console.warn("GameController: truck._originalCollidesWith and truck.collidesWith are not set for "+a.name+". Initializing to default."),a._originalCollidesWith=[LevelData.COLLISION_TYPES.BUILDING,LevelData.COLLISION_TYPES.BOUNDARY,LevelData.COLLISION_TYPES.PARKING_SLOT,LevelData.COLLISION_TYPES.TRUCK]),a.collidesWith=a.collidesWith.filter(function(a){return a!==LevelData.COLLISION_TYPES.BUILDING&&a!==LevelData.COLLISION_TYPES.BOUNDARY}),
"function"===typeof a.beginParking&&a.isLastWaypoint()&&a.beginParking(b));break;default:d||(a.vel.x=0,a.vel.y=0)}if("function"===typeof a.onCollision&&c!==LevelData.COLLISION_TYPES.PARKING_SLOT)a.onCollision(b)},handleTruckVsTruckCollision:function(a,b){if(!this.isGameOver){a.isParking||(a.vel.x=0,a.vel.y=0);b.isParking||(b.vel.x=0,b.vel.y=0);this.onGameOver();var c=this.score,d=ig.game.load("bestScore")||0;c>d&&(d=c,ig.game.save("bestScore",c));ig.game.spawnEntity(EntityPopupGameOver,0,0,{currentScore:c,
bestScore:d})}},onGameOver:function(){this.isGameOver=!0;for(var a=ig.game.getEntitiesByType(EntityTruck),b=0;b<a.length;b++)a[b].onGameOver()},addScore:function(a){this.score+=a;this.scoreText.setTextContent(this.score)},pauseGame:function(){ig.game.isPaused=!0;this.spawnController.pauseGame()},resumeGame:function(){ig.game.isPaused=!1;this.spawnController.resumeGame()},draw:function(){this.parent();this.bgImage&&this.bgImage.draw(0,0);if(ig.game.debugMode){for(var a=0;a<this.collisionShapesData.length;a++){var b=
this.collisionShapesData[a],c="blue";switch(b.type){case LevelData.COLLISION_TYPES.BUILDING:c="purple";break;case LevelData.COLLISION_TYPES.BOUNDARY:c="green"}this.drawShapeByPoints(b.shape.pointList,c)}a=ig.game.getEntitiesByType(EntityTruck);for(b=0;b<a.length;b++)if((c=a[b])&&c.shape&&c.id&&this.trucksCollisionStatus[c.id]){var d=this.trucksCollisionStatus[c.id];this.drawShapeByPoints(c.shape.pointList,d&&d.isColliding?"red":"lime")}}}})});ig.baked=!0;
ig.module("game.levels.game").requires("game.controllers.game-controller").defines(function(){LevelGame={entities:[{type:"GameController",x:0,y:0}],layer:[]}});ig.baked=!0;
ig.module("game.main").requires("impact.game","impact.debug.debug","plugins.patches.fps-limit-patch","plugins.patches.timer-patch","plugins.patches.user-agent-patch","plugins.patches.webkit-image-smoothing-patch","plugins.patches.windowfocus-onMouseDown-patch","plugins.patches.input-patch","plugins.data.vector","plugins.data.color-rgb","plugins.font.font-loader","plugins.handlers.dom-handler","plugins.handlers.size-handler","plugins.handlers.api-handler","plugins.handlers.visibility-handler","plugins.audio.sound-handler",
"plugins.io.io-manager","plugins.io.storage-manager","plugins.splash-loader","plugins.math.sat","plugins.tween","plugins.tweens-handler","plugins.url-parameters","plugins.director","plugins.impact-storage","plugins.fullscreen","plugins.utils.utils","plugins.utils.pointer","plugins.animation-scale","plugins.utils.entity-extended","plugins.utils.text-to-canvas","plugins.utils.colors","plugins.branding.splash","game.entities.branding-logo-placeholder","game.entities.buttons.button-more-games","game.entities.path-head",
"game.entities.text","game.controllers.spawn-controller","game.levels.opening","game.levels.menu","game.levels.game").defines(function(){this.START_OBFUSCATION;this.FRAMEBREAKER;MyGame=ig.Game.extend({name:"truck-master",version:"1.0.0",frameworkVersion:"2.0.2",sessionData:{},io:null,paused:!1,tweens:null,LAYERS:{BACKGROUND:0,CONTROLLERS:1,FOREGROUND:2,UI:50,POPUP:100},debugMode:!1,currentLevel:1,isPaused:!1,init:function(){this.tweens=new ig.TweensHandler;this.io=new IoManager;this.setupStorageManager();
this.setupUrlParams=new ig.UrlParameters;this.removeLoadingWheel();this.finalize();this.debugMode=ig.Entity._debugShowBoxes},initData:function(){return this.sessionData={sound:0.5,music:0.5,score:0,bestScore:0}},finalize:function(){this.start();ig.sizeHandler.reorient()},removeLoadingWheel:function(){try{$("#ajaxbar").css("background","none")}catch(a){console.log(a)}},showDebugMenu:function(){console.log("showing debug menu ...");ig.Entity._debugShowBoxes=!0;$(".ig_debug").show()},start:function(){this.director=
new ig.Director(this,[LevelOpening,LevelMenu,LevelGame]);if(_SETTINGS.Branding.Splash.Enabled)try{this.branding=new ig.BrandingSplash}catch(a){console.log(a),console.log("Loading original levels ..."),this.director.loadLevel(this.director.currentLevel)}else this.director.loadLevel(this.director.currentLevel);ig.soundHandler.bgmPlayer.volume(ig.game.sessionData.music);ig.soundHandler.sfxPlayer.volume(ig.game.sessionData.sound)},fpsCount:function(){this.fpsTimer||(this.fpsTimer=new ig.Timer(1));this.fpsTimer&&
0>this.fpsTimer.delta()&&(null!=this.fpsCounter?this.fpsCounter++:(ig.game.fps=this.fpsCounter,this.fpsCounter=0,this.fpsTimer.reset()))},endGame:function(){console.log("End game");ig.soundHandler.bgmPlayer.stop();ig.apiHandler.run("MJSEnd")},pauseGame:function(){console.log("Game Paused")},resumeGame:function(){console.log("Game Resumed")},showOverlay:function(a){for(i=0;i<a.length;i++)$("#"+a[i])&&$("#"+a[i]).show(),document.getElementById(a[i])&&(document.getElementById(a[i]).style.visibility=
"visible")},hideOverlay:function(a){for(i=0;i<a.length;i++)$("#"+a[i])&&$("#"+a[i]).hide(),document.getElementById(a[i])&&(document.getElementById(a[i]).style.visibility="hidden")},currentBGMVolume:1,addition:0.1,update:function(){this.paused?(this.updateWhilePaused(),this.checkWhilePaused()):(this.parent(),this.tweens.update(this.tweens.now()))},updateWhilePaused:function(){for(var a=0;a<this.entities.length;a++)this.entities[a].ignorePause&&this.entities[a].update()},checkWhilePaused:function(){for(var a=
{},b=0;b<this.entities.length;b++){var c=this.entities[b];if(c.ignorePause&&!(c.type==ig.Entity.TYPE.NONE&&c.checkAgainst==ig.Entity.TYPE.NONE&&c.collides==ig.Entity.COLLIDES.NEVER))for(var d={},e=Math.floor(c.pos.y/this.cellSize),g=Math.floor((c.pos.x+c.size.x)/this.cellSize)+1,j=Math.floor((c.pos.y+c.size.y)/this.cellSize)+1,p=Math.floor(c.pos.x/this.cellSize);p<g;p++)for(var f=e;f<j;f++)if(a[p])if(a[p][f]){for(var n=a[p][f],l=0;l<n.length;l++)c.touches(n[l])&&!d[n[l].id]&&(d[n[l].id]=!0,ig.Entity.checkPair(c,
n[l]));n.push(c)}else a[p][f]=[c];else a[p]={},a[p][f]=[c]}},draw:function(){this.parent();this.dctf()},dctf:function(){this.COPYRIGHT},clearCanvas:function(a,b,c){var d=a.canvas;a.clearRect(0,0,b,c);d.style.display="none";d.offsetHeight;d.style.display="inherit"},drawDebug:function(){if(!ig.global.wm&&(this.debugEnable(),this.viewDebug&&(ig.system.context.fillStyle="#000000",ig.system.context.globalAlpha=0.35,ig.system.context.fillRect(0,0,ig.system.width/4,ig.system.height),ig.system.context.globalAlpha=
1,this.debug&&0<this.debug.length)))for(i=0;i<this.debug.length;i++)ig.system.context.font="10px Arial",ig.system.context.fillStyle="#ffffff",ig.system.context.fillText(this.debugLine-this.debug.length+i+": "+this.debug[i],10,50+10*i)},debugCL:function(a){this.debug?(50>this.debug.length||this.debug.splice(0,1),this.debug.push(a),this.debugLine++):(this.debug=[],this.debugLine=1,this.debug.push(a));console.log(a)},debugEnable:function(){ig.input.pressed("click")&&(this.debugEnableTimer=new ig.Timer(2));
this.debugEnableTimer&&0>this.debugEnableTimer.delta()?ig.input.released("click")&&(this.debugEnableTimer=null):this.debugEnableTimer&&0<this.debugEnableTimer.delta()&&(this.debugEnableTimer=null,this.viewDebug=this.viewDebug?!1:!0)},drawVersion:function(){if("undefined"!==typeof _SETTINGS.Versioning&&null!==_SETTINGS.Versioning&&_SETTINGS.Versioning.DrawVersion){var a=ig.system.context;fontSize=_SETTINGS.Versioning.FontSize;fontFamily=_SETTINGS.Versioning.FontFamily;fillStyle=_SETTINGS.Versioning.FillStyle;
a.save();a.textBaseline="bottom";a.textAlign="left";a.font=fontSize+" "+fontFamily||"10px Arial";a.fillStyle=fillStyle||"#ffffff";a.fillText("v"+_SETTINGS.Versioning.Version+"+build."+_SETTINGS.Versioning.Build,10,ig.system.height-10);a.restore()}}});ig.domHandler=null;ig.domHandler=new ig.DomHandler;ig.domHandler.forcedDeviceDetection();ig.domHandler.forcedDeviceRotation();ig.apiHandler=new ig.ApiHandler;ig.sizeHandler=new ig.SizeHandler(ig.domHandler);ig.ua.mobile?(ig.Sound.enabled=!1,ig.main("#canvas",
MyGame,60,ig.sizeHandler.mobile.actualResolution.x,ig.sizeHandler.mobile.actualResolution.y,ig.sizeHandler.scale,ig.SplashLoader),ig.sizeHandler.resize()):ig.main("#canvas",MyGame,60,ig.sizeHandler.desktop.actualResolution.x,ig.sizeHandler.desktop.actualResolution.y,ig.sizeHandler.scale,ig.SplashLoader);ig.visibilityHandler=new ig.VisibilityHandler;ig.soundHandler=null;ig.soundHandler=new ig.SoundHandler;ig.sizeHandler.reorient();this.DOMAINLOCK_BREAKOUT_ATTEMPT;this.END_OBFUSCATION});
