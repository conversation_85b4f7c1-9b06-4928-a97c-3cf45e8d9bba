ig.module('game.controllers.game-controller')
.requires(
    'plugins.utils.entity-extended',
    'plugins.utils.buttons.button-factory',
    'plugins.utils.objects.popup',
    'game.data.level-data',
    'game.entities.ui.drag-point',
    'game.entities.objects.truck',
    'game.entities.spawn-grid',
    'game.entities.objects.parking-slot',
    'game.entities.objects.popup-gameover',
    'game.entities.text',
    'plugins.utils.transition',
    'game.entities.buttons.button-pause'
)
.defines(function () {
    "use strict";

    ig.GameController = ig.global.GameController = ig.EntityExtended.extend({
        zIndex: 1,
        name: 'game-controller',
        bgImage: null,
        levelDataManager: null,
        currentLevelData: null,
        collisionShapesData: [],
        trucksCollisionStatus: {},
        previousCollisionStatus: {},
        sat: null,
        isGameOver: false,
        score: 0,
        init: function (x, y, settings) {
            this.parent(x, y, settings);
            window.gamectrl = this;
            ig.currentCtrl = this;
            ig.game.isPaused = false;
            this.ctx = ig.system.context;
            this.sat = new ig.SAT();
            this.trucksCollisionStatus = {};
            this.previousCollisionStatus = {};

            this.levelDataManager = new LevelData();
            this.spawnGrid = ig.game.spawnEntity(EntitySpawnGrid, 0, 0, {
                isHideDraw: true
            });

            // this.testBtn = this.spawnEntity(EntityButtonText, 20, 20, {
            //     buttonTextConfig: {
            //         fontSize: 32,
            //         fontFamily: 'Arial',
            //         fontColor: '#000',
            //         text: 'Spawn Truck',
            //         align: 'center',
            //         vAlign: 'middle',
            //         strokeColor: '#000',
            //         strokeWidth: 0,
            //         justify: false,
            //         debug: true
            //     },
            //     onClickCallback: function () {
            //         var truck = ig.game.spawnEntity(EntityTruck, 0, 0, { spawnPoint: "right4" });
            //         this.setupTruckCollision(truck);
            //     }.bind(this) // Bind to GameController's context
            // });


            var currentLevel = 'level' + ig.game.currentLevel;
            this.setupLevel(currentLevel);
            this.spawnController = new SpawnController(this); 

            this.scoreText = ig.game.spawnEntity(
                EntityText, 
                0, 
                0, 
                {
                    zIndex: this.zIndex + 20,
                    textConfig: {
                        fontSize: 72,
                        fontFamily: 'bebasneue-bold',
                        fontColor: '#fff',
                        text: '0',
                        align: 'center',
                        vAlign: 'bottom',
                        shadowEnabled: true,
                        shadowOffsetX: 3,
                        shadowOffsetY: 3,
                        shadowBlur: 1,
                        width: 200,
                        height: 70
                    }
                }
            );

            this.scoreText.anchorTo(
                // Create a dummy entity to anchor to
                {
                    pos: { x: 0, y: 0 },
                    size: { x: 1920, y: 1080 }
                },
                {
                    targetAnchor: { x: 0, y: -1 },
                    selfAnchor: "top",
                    offset: { x: 0, y: 20 }
                }
            );

            this.transition = ig.game.spawnEntity(EntityTransition, 0, 0, { color: 'black' });
            this.transition.fadeOut(1000);

            this.buttonPause = ig.game.spawnEntity(EntityButtonPause, -999, -999, { zIndex: ig.game.LAYERS.UI });
            this.buttonPause.pos.x = ig.system.width - this.buttonPause.size.x - 20;
            this.buttonPause.pos.y = 20;

            ig.game.sortEntitiesDeferred();
        },

        setupLevel: function (levelId) {
            this.currentLevelId = levelId;
            this.currentLevelData = this.levelDataManager.getLevelData(this.currentLevelId);
            
            if (this.currentLevelData) {
                this.bgImage = this.levelDataManager.getMapImage(this.currentLevelId);
                this.collisionShapesData = [];
                this.loadCollisionData();
            } else {
                console.error("GameController: Failed to load level data for:", this.currentLevelId);
            }
        },

        loadCollisionData: function () {
            if (!this.currentLevelData.buildings || !this.currentLevelData.buildings.length) return;
            
            for (var i = 0; i < this.currentLevelData.buildings.length; i++) {
                var buildingData = this.currentLevelData.buildings[i];
                if (!buildingData.vertices || !buildingData.vertices.length) continue;
                
                var shape = new ig.SAT.Shape(buildingData.vertices); 
                var aabb = this.calculateAABB(buildingData.vertices);
                if (!shape || !aabb) continue;

                if (buildingData.type === LevelData.COLLISION_TYPES.PARKING_SLOT) {
                    var minX = buildingData.vertices[0].x, minY = buildingData.vertices[0].y;
                    for (var v_idx = 1; v_idx < buildingData.vertices.length; v_idx++) {
                        minX = Math.min(minX, buildingData.vertices[v_idx].x);
                        minY = Math.min(minY, buildingData.vertices[v_idx].y);
                    }
                    ig.game.spawnEntity(EntityParkingSlot, minX, minY, {
                        name: buildingData.name || 'unnamed_slot_' + i,
                        color: buildingData.color,
                        vertices: buildingData.vertices, 
                        isOccupied: false,
                        colorIndicator: {
                            anchor: buildingData.colorIndicator.anchor,
                            anchorOffset: buildingData.colorIndicator.anchorOffset
                        },
                        frontPoint: buildingData.frontPoint,
                        backPoint: buildingData.backPoint,
                        swapPoints: buildingData.swapPoints
                    });
                } else {
                    var shapeObject = {
                        shape: shape, 
                        aabb: aabb,   
                        type: buildingData.type || LevelData.COLLISION_TYPES.NONE,
                        name: buildingData.name || 'unnamed_building_' + i,
                        color: buildingData.color || 'blue'
                    };
                    this.collisionShapesData.push(shapeObject);
                }
            }

            this.setupParkingSlots();
        },

        setupParkingSlots: function () {
            this.parkingSlots = []; 

            var parkingSlotEntities = ig.game.getEntitiesByType(EntityParkingSlot);

            if (!parkingSlotEntities || parkingSlotEntities.length === 0) {
                console.warn("GameController: No EntityParkingSlot instances found in the game.");
                return;
            }

            for (var i = 0; i < parkingSlotEntities.length; i++) {
                var entity = parkingSlotEntities[i];

                // Use the pre-calculated worldShape
                if (!entity.worldShape || !entity.worldShape.pointList || entity.worldShape.pointList.length === 0) {
                    console.warn("GameController: EntityParkingSlot '" + entity.name + "' has no worldShape or pointList. Skipping.");
                    continue;
                }
                
                var vertices = entity.worldShape.pointList.map(function (v) {
                    return { x: v.x, y: v.y }; 
                });

                var geometry = ig.utils.getParkingSlotGeometry(vertices);
                if (!geometry) {
                    console.warn("GameController: Could not calculate geometry for slot '" + entity.name + "'. Skipping.");
                    continue;
                }

                // The entity is the source of truth for the final angle, as it handles manual overrides.
                var orientationAngle = entity.getSlotAngle();
                var slotColor = entity.color;
                var slotName = entity.name || "Slot_" + i;

                var slotData = {
                    center: geometry.center,
                    vertices: vertices,
                    name: slotName,
                    orientationAngle: orientationAngle,
                    slotLength: geometry.length,
                    slotWidth: geometry.width,
                    color: slotColor
                };

                entity.slotData = slotData;
                slotData.slotEntity = entity;
                this.parkingSlots.push(slotData);
            }
        },

        setupTruckCollision: function (truck) {
            if (!truck) return;

            truck.collisionType = LevelData.COLLISION_TYPES.TRUCK;
            // Ensure _originalCollidesWith is initialized if it's not already.
            // This stores the default collision types truck should interact with.
            if (!truck._originalCollidesWith) { 
                truck._originalCollidesWith = [
                    LevelData.COLLISION_TYPES.BUILDING,
                    LevelData.COLLISION_TYPES.BOUNDARY,
                    LevelData.COLLISION_TYPES.PARKING_SLOT,
                    LevelData.COLLISION_TYPES.TRUCK
                ];
            }
            truck.collidesWith = ig.copy(truck._originalCollidesWith);

            this.trucksCollisionStatus[truck.id] = {
                isColliding: false,
                collidedWith: null
            };
            this.previousCollisionStatus[truck.id] = {
                isColliding: false,
                collidedWith: null
            };
        },

        getTruckCollisionData: function (truck) {
            // FIXED: Removed pathHead check - shape functionality is now directly on truck
            if (!truck || !truck.shape) {
                console.log("DEBUG: getTruckCollisionData failed - truck:", !!truck, "shape:", truck ? !!truck.shape : "no truck");
                return null;
            }

            // Ensure truck vertices are updated based on its current rotation and position.
            if (typeof truck.updateRotatedVertices === 'function') {
                truck.updateRotatedVertices();
            } else {
                console.warn("DEBUG: truck.updateRotatedVertices not found for truck:", truck.id);
                return null;
            }

            var aabb = this.calculateAABB(truck.vertices);
            if (!aabb) {
                console.warn("DEBUG: Failed to calculate AABB for truck:", truck.id);
                return null;
            }

            return {
                entity: truck,
                shape: truck.shape, // The SAT.js shape for collision detection
                attackShape: truck.attackShape,
                aabb: aabb, // The Axis-Aligned Bounding Box
                type: truck.collisionType, // Type of the colliding entity (e.g., TRUCK)
                collidesWith: truck.collidesWith || [], // Types of entities it can collide with
                name: 'Truck-' + truck.id // Unique name for identification
            };
        },

        /**
         * Draws a polygon defined by an array of points.
         * @param {Array<Object>} points - Array of points {x, y}.
         * @param {string} color - Fill color for the shape.
         */
        drawShapeByPoints: function (points, color) {
            if (!points || !points.length) return;
            this.ctx.save();
            this.ctx.fillStyle = color;
            this.ctx.globalAlpha = 0.5; // Semi-transparent for debugging
            this.ctx.strokeStyle = "black";
            this.ctx.lineWidth = 1;
            this.ctx.beginPath();
            this.ctx.moveTo(points[0].x, points[0].y);
            for (var i = 1; i < points.length; i++) {
                this.ctx.lineTo(points[i].x, points[i].y);
            }
            this.ctx.closePath();
            this.ctx.fill();
            this.ctx.stroke();
            this.ctx.restore();
        },

        /**
         * Calculates the Axis-Aligned Bounding Box (AABB) for a set of vertices.
         * @param {Array<Object>} vertices - Array of points {x, y}.
         * @returns {Object|null} AABB object {minX, minY, maxX, maxY} or null if no vertices.
         */
        calculateAABB: function (vertices) {
            if (!vertices || vertices.length === 0) return null;
            var minX = vertices[0].x, minY = vertices[0].y;
            var maxX = vertices[0].x, maxY = vertices[0].y;
            for (var i = 1; i < vertices.length; i++) {
                minX = Math.min(minX, vertices[i].x);
                minY = Math.min(minY, vertices[i].y);
                maxX = Math.max(maxX, vertices[i].x);
                maxY = Math.max(maxY, vertices[i].y);
            }
            return { minX: minX, minY: minY, maxX: maxX, maxY: maxY };
        },

        checkAABBOverlap: function (aabb1, aabb2) {
            if (!aabb1 || !aabb2) return false;
            // Check for non-overlapping conditions.
            if (aabb1.maxX < aabb2.minX || aabb1.minX > aabb2.maxX) return false;
            if (aabb1.maxY < aabb2.minY || aabb1.minY > aabb2.maxY) return false;
            return true; // Overlap detected.
        },

        update: function () {
            this.parent();
            if (this.isGameOver) {
                return;
            };

            if (this.spawnController) {
                this.spawnController.update();
            }
            var allTrucks = ig.game.getEntitiesByType(EntityTruck);

            // Store previous collision states to detect collision exits.
            for (var truckId in this.trucksCollisionStatus) {
                if (this.trucksCollisionStatus.hasOwnProperty(truckId)) {
                    this.previousCollisionStatus[truckId] = ig.copy(this.trucksCollisionStatus[truckId]);
                }
            }

            // Reset current collision states.
            for (var id in this.trucksCollisionStatus) {
                if (this.trucksCollisionStatus.hasOwnProperty(id)) {
                    this.trucksCollisionStatus[id].isColliding = false;
                    this.trucksCollisionStatus[id].collidedWith = null;
                }
            }

            // Main collision detection loop for trucks.
            for (var t = 0; t < allTrucks.length; t++) {
                var truck = allTrucks[t];
                if (!truck || truck._killed || !truck.id) continue; // Skip invalid or killed trucks.

                // Initialize collision status for new trucks.
                if (!this.trucksCollisionStatus[truck.id]) {
                    this.setupTruckCollision(truck);
                }
                
                // Restore default collision interactions if needed (e.g., after parking).
                if (truck.needsCollisionRestore) {
                    if (truck._originalCollidesWith) {
                        truck.collidesWith = ig.copy(truck._originalCollidesWith);
                    } else {
                        // Fallback if _originalCollidesWith was somehow not set.
                        console.warn("GameController: Truck " + truck.name + " needs collision restore but no _originalCollidesWith found. Setting to default.");
                        truck.collidesWith = [
                            LevelData.COLLISION_TYPES.BUILDING,
                            LevelData.COLLISION_TYPES.BOUNDARY,
                            LevelData.COLLISION_TYPES.PARKING_SLOT,
                            LevelData.COLLISION_TYPES.TRUCK
                        ];
                    }
                    truck.needsCollisionRestore = false;
                }


                if (!truck.shape) continue; // Skip trucks without a collision shape.
                var truckData = this.getTruckCollisionData(truck);
                if (!truckData) continue; // Skip if data retrieval fails.

                // Check collision with static level elements.
                this.checkTruckVsLevelCollision(truckData);

                // Check collision with other trucks.
                for (var o = 0; o < allTrucks.length; o++) {
                    var otherTruck = allTrucks[o];
                    // Skip self, invalid, killed, or shapeless trucks.
                    if (truck === otherTruck || !otherTruck || otherTruck._killed || !otherTruck.shape) continue;
                    var otherTruckData = this.getTruckCollisionData(otherTruck);
                    if (!otherTruckData) continue;

                    // Check if both trucks are configured to collide with each other.
                    if (truckData.collidesWith.includes(otherTruckData.type) &&
                        otherTruckData.collidesWith.includes(truckData.type)) {
                        // Broad phase: AABB overlap check.
                        if (this.checkAABBOverlap(truckData.aabb, otherTruckData.aabb)) {
                            // --- Custom Attack/Defend Collision ---
                            var attackCollision = false;
                            // Check if truck1's attack box hits truck2's defend box
                            if (truckData.attackShape && this.sat.simpleShapeIntersect(truckData.attackShape, otherTruckData.shape)) {
                                attackCollision = true;
                            }
                            // Check if truck2's attack box hits truck1's defend box
                            if (!attackCollision && otherTruckData.attackShape && this.sat.simpleShapeIntersect(otherTruckData.attackShape, truckData.shape)) {
                                attackCollision = true;
                            }

                            if (attackCollision) {
                                this.handleTruckVsTruckCollision(truck, otherTruck);
                                // Update collision status for both trucks.
                                this.trucksCollisionStatus[truck.id] = {
                                    isColliding: true,
                                    collidedWith: { type: otherTruckData.type, name: otherTruckData.name, entity: otherTruck }
                                };
                                this.trucksCollisionStatus[otherTruck.id] = {
                                    isColliding: true,
                                    collidedWith: { type: truckData.type, name: truckData.name, entity: truck }
                                };
                            }
                        }
                    }
                }
            }

            // Track collision entry times.
            for (var i = 0; i < allTrucks.length; i++) {
                var currentTruck = allTrucks[i];
                if (!currentTruck || !currentTruck.id || 
                    !this.trucksCollisionStatus[currentTruck.id] || 
                    !this.previousCollisionStatus[currentTruck.id]) {
                    continue;
                }
                var currentStatus = this.trucksCollisionStatus[currentTruck.id];
                var prevStatus = this.previousCollisionStatus[currentTruck.id];
                // If collision just started in this frame.
                if (currentStatus.isColliding && !prevStatus.isColliding) {
                    currentTruck.collisionEntryTime = ig.system.clock.delta();
                }
            }
            this.checkCollisionExits(allTrucks);
        },

        /**
         * Checks if any trucks have exited a collision state.
         * @param {Array<EntityTruck>} allTrucks - List of all truck entities.
         */
        checkCollisionExits: function (allTrucks) {
            for (var t = 0; t < allTrucks.length; t++) {
                var truck = allTrucks[t];
                if (!truck || !truck.id || 
                    !this.previousCollisionStatus[truck.id] || 
                    !this.trucksCollisionStatus[truck.id]) continue;

                var prevStatus = this.previousCollisionStatus[truck.id];
                var currStatus = this.trucksCollisionStatus[truck.id];

                // If truck was colliding and now is not.
                if (prevStatus.isColliding && !currStatus.isColliding) {
                    if (typeof truck.onExitCollision === 'function' && prevStatus.collidedWith) {
                        truck.onExitCollision(prevStatus.collidedWith);
                    }
                }
            }
        },

        /**
         * Checks and handles collisions between a truck and static level elements (buildings, boundaries, parking slots).
         * @param {Object} truckData - Collision data for the truck.
         */
        checkTruckVsLevelCollision: function (truckData) {
            if (!truckData || !truckData.shape || !truckData.aabb) return;
            var truck = truckData.entity;
            
            // Check against general collision shapes (buildings, boundaries).
            for (var i = 0; i < this.collisionShapesData.length; i++) {
                var levelShapeData = this.collisionShapesData[i];
                if (!truckData.collidesWith.includes(levelShapeData.type)) continue; // Skip if truck doesn't collide with this type.

                // Broad phase: AABB overlap.
                if (this.checkAABBOverlap(truckData.aabb, levelShapeData.aabb)) {
                    // Narrow phase: Precise SAT collision.
                    if (truckData.attackShape && this.sat.simpleShapeIntersect(truckData.attackShape, levelShapeData.shape)) {
                        this.handleTruckVsLevelCollision(truck, levelShapeData, levelShapeData.type);
                        this.trucksCollisionStatus[truck.id] = {
                            isColliding: true,
                            collidedWith: { type: levelShapeData.type, name: levelShapeData.name, shapeData: levelShapeData }
                        };
                        return; // Truck can only collide with one level object at a time here.
                    }
                }
            }

            // Check against parking slots.
            var parkingSlots = ig.game.getEntitiesByType(EntityParkingSlot);
            for (var j = 0; j < parkingSlots.length; j++) {
                var slot = parkingSlots[j];
                if (!truckData.collidesWith.includes(slot.type)) continue;
                // If truck has an intended slot, only check collision with that specific slot.
                if (truck.intendedParkingSlotName && slot.name !== truck.intendedParkingSlotName) {
                    continue;
                }

                var slot = parkingSlots[j];
                if (!truckData.collidesWith.includes(slot.type)) continue;
                if (truck.intendedParkingSlotName && slot.name !== truck.intendedParkingSlotName) {
                    continue;
                }

                // Use the pre-calculated worldShape from the slot entity
                if (slot.worldShape && slot.worldShape.pointList && slot.worldShape.pointList.length > 0) {
                    // slot.worldShape.pointList are SAT.Vector2D objects in world coordinates.
                    // calculateAABB can directly use objects with .x and .y properties.
                    var slotAABB = this.calculateAABB(slot.worldShape.pointList); 

                    if (slotAABB && this.checkAABBOverlap(truckData.aabb, slotAABB)) { // Ensure slotAABB is valid
                        // worldSlotShape is now directly slot.worldShape
                        if (truckData.attackShape && this.sat.simpleShapeIntersect(truckData.attackShape, slot.worldShape)) {
                            this.handleTruckVsLevelCollision(truck, slot, LevelData.COLLISION_TYPES.PARKING_SLOT);
                            this.trucksCollisionStatus[truck.id] = {
                                isColliding: true,
                                collidedWith: { type: slot.type, name: slot.name, entity: slot }
                            };
                            return; // Collision with a parking slot found.
                        }
                    }
                } else {
                    console.warn("GameController: ParkingSlot " + slot.name + " has no valid worldShape for collision check.");
                }
            }
        },

        /**
         * Handles the outcome of a collision between a truck and a level element.
         * @param {EntityTruck} truck - The truck involved in the collision.
         * @param {Object} collisionTarget - The level element (shape data or entity) the truck collided with.
         * @param {string} targetType - The type of the collision target (e.g., BUILDING, PARKING_SLOT).
         */
        handleTruckVsLevelCollision: function (truck, collisionTarget, targetType) {
            var truckIsActivelyParking = truck.isParking; // Flag to check if truck is in parking maneuver.

            switch (targetType) {
                case LevelData.COLLISION_TYPES.BUILDING:
                case LevelData.COLLISION_TYPES.BOUNDARY:
                    // Stop truck movement if it hits a building or boundary, unless it's already parking.
                    if (!truckIsActivelyParking) {
                        truck.vel.x = 0;
                        truck.vel.y = 0;
                    }
                    break;

                case LevelData.COLLISION_TYPES.PARKING_SLOT:
                    var parkingSlot = collisionTarget;
                    // Conditions for initiating parking sequence upon collision with the designated slot.
                    var conditionMet = !truckIsActivelyParking &&
                                       truck.intendedParkingSlotName === parkingSlot.name &&
                                       truck.truckState === 'none'; // Ensure truck is in a state to start parking.

                    if (conditionMet) {
                        // Initialize _originalCollidesWith if not already set. This is crucial for restoring
                        // collision behavior after parking is complete or aborted.
                        if (!truck._originalCollidesWith && truck.collidesWith) {
                             console.warn("GameController: truck._originalCollidesWith was not set for " + truck.name + ". Initializing from current collidesWith.");
                             truck._originalCollidesWith = ig.copy(truck.collidesWith);
                        } else if (!truck._originalCollidesWith && !truck.collidesWith) {
                             // Fallback if both are missing, though ideally collidesWith should always exist.
                             console.warn("GameController: truck._originalCollidesWith and truck.collidesWith are not set for " + truck.name + ". Initializing to default.");
                             truck._originalCollidesWith = [
                                LevelData.COLLISION_TYPES.BUILDING,
                                LevelData.COLLISION_TYPES.BOUNDARY,
                                LevelData.COLLISION_TYPES.PARKING_SLOT,
                                LevelData.COLLISION_TYPES.TRUCK
                             ];
                        }
                        
                        // Temporarily modify truck's collision behavior to allow passing through buildings/boundaries
                        // during the parking maneuver.
                        truck.collidesWith = truck.collidesWith.filter(function (type) {
                            return type !== LevelData.COLLISION_TYPES.BUILDING &&
                                   type !== LevelData.COLLISION_TYPES.BOUNDARY;
                        });
                        
                        // FIXED: Use movementComponent instead of inputTrajectoryComponent
                        if (typeof truck.beginParking === 'function' && truck.isLastWaypoint()) {
                             truck.beginParking(parkingSlot);
                        }
                    }
                    break;
                    
                default: 
                    // Default collision response for other types: stop movement if not parking.
                    if (!truckIsActivelyParking) {
                        truck.vel.x = 0;
                        truck.vel.y = 0;
                    }
                    break;
            }

            // Trigger generic onCollision behavior for the truck if it's not a parking slot collision
            // (parking slot interactions are handled more specifically).
            if (typeof truck.onCollision === 'function' && 
                targetType !== LevelData.COLLISION_TYPES.PARKING_SLOT) {
                truck.onCollision(collisionTarget); 
            }
        },

        /**
         * Handles the collision event between two trucks.
         * @param {EntityTruck} truck1 - The first truck.
         * @param {EntityTruck} truck2 - The second truck.
         */
        handleTruckVsTruckCollision: function (truck1, truck2) {
            if (this.isGameOver) return; // Do nothing if game is already over.

            // Stop movement for non-parking trucks involved in the collision.
            if (!truck1.isParking) {
                truck1.vel.x = 0;
                truck1.vel.y = 0;
            }
            if (!truck2.isParking) {
                truck2.vel.x = 0;
                truck2.vel.y = 0;
            }
            
            // Trigger game over sequence.
            this.onGameOver();
            var score = this.score;
            var bestScore = ig.game.load('bestScore') || 0;
            if (score > bestScore) {
                bestScore = score;
                ig.game.save('bestScore', score); // Save new best score.
            }
            // Display game over popup.
            ig.game.spawnEntity(EntityPopupGameOver, 0, 0, {
                currentScore: score,
                bestScore: bestScore
            });
        },

        /**
         * Handles game over logic. Sets the game over flag and notifies all trucks.
         */
        onGameOver: function () {
            this.isGameOver = true;
            var allTrucks = ig.game.getEntitiesByType(EntityTruck);
            for (var t = 0; t < allTrucks.length; t++) {
                var truck = allTrucks[t];
                truck.onGameOver(); // Notify each truck of game over.
            }
        },

        /**
         * Adds points to the score and updates the score display.
         * @param {number} points - Points to add.
         */
        addScore: function (points) {
            this.score += points;
            this.scoreText.setTextContent(this.score);
        },

        pauseGame: function () {
            ig.game.isPaused = true;
            this.spawnController.pauseGame();
        },

        resumeGame: function () {
            ig.game.isPaused = false;
            this.spawnController.resumeGame();
        },

        draw: function () {
            this.parent(); // Call parent draw method.
            if (this.bgImage) {
                this.bgImage.draw(0, 0); // Draw background image.
            }

            // Debug drawing, only active if ig.game.debugMode is true.
            if (!ig.game.debugMode) return;

            // Draw static collision shapes.
            for (var i = 0; i < this.collisionShapesData.length; i++) {
                var shapeData = this.collisionShapesData[i]; 
                var color = "blue"; // Default color.
                switch (shapeData.type) {
                    case LevelData.COLLISION_TYPES.BUILDING: color = "purple"; break;
                    case LevelData.COLLISION_TYPES.BOUNDARY: color = "green"; break;
                    // Parking slots are drawn by their own entities if debug is enabled there.
                }
                this.drawShapeByPoints(shapeData.shape.pointList, color);
            }

            // Draw truck collision shapes and highlight if colliding.
            var allTrucks = ig.game.getEntitiesByType(EntityTruck);
            for (var t = 0; t < allTrucks.length; t++) {
                var truck = allTrucks[t];
                if (!truck || !truck.shape || !truck.id || !this.trucksCollisionStatus[truck.id]) continue;
                var collisionStatus = this.trucksCollisionStatus[truck.id];
                var truckColor = (collisionStatus && collisionStatus.isColliding) ? "red" : "lime"; // Red if colliding, lime otherwise.
                this.drawShapeByPoints(truck.shape.pointList, truckColor);
            }
        }
    });
});
